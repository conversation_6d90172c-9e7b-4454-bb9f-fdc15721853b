import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/subtask_models.dart';
import '../services/api/subtasks_api_service.dart';

/// متحكم المهام الفرعية
class SubtasksController extends GetxController {
  final SubtasksApiService _apiService = SubtasksApiService();

  // قوائم المهام الفرعية
  final RxList<Subtask> _allSubtasks = <Subtask>[].obs;
  final RxList<Subtask> _filteredSubtasks = <Subtask>[].obs;
  final RxList<Subtask> _taskSubtasks = <Subtask>[].obs;

  // المهمة الفرعية الحالية
  final Rx<Subtask?> _currentSubtask = Rx<Subtask?>(null);

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<int?> _taskFilter = Rx<int?>(null);
  final RxBool _showCompletedOnly = false.obs;
  final RxBool _showPendingOnly = false.obs;

  // Getters
  List<Subtask> get allSubtasks => _allSubtasks;
  List<Subtask> get filteredSubtasks => _filteredSubtasks;
  List<Subtask> get taskSubtasks => _taskSubtasks;
  Subtask? get currentSubtask => _currentSubtask.value;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  int? get taskFilter => _taskFilter.value;
  bool get showCompletedOnly => _showCompletedOnly.value;
  bool get showPendingOnly => _showPendingOnly.value;

  @override
  void onInit() {
    super.onInit();
    loadAllSubtasks();
  }

  /// تحميل جميع المهام الفرعية
  Future<void> loadAllSubtasks() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final subtasks = await _apiService.getAllSubtasks();
      _allSubtasks.assignAll(subtasks);
      _applyFilters();
      debugPrint('تم تحميل ${subtasks.length} مهمة فرعية');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام الفرعية: $e';
      debugPrint('خطأ في تحميل المهام الفرعية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على مهمة فرعية بالمعرف
  Future<void> getSubtaskById(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final subtask = await _apiService.getSubtaskById(id);
      _currentSubtask.value = subtask;
      debugPrint('تم تحميل المهمة الفرعية: ${subtask?.title}');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهمة الفرعية: $e';
      debugPrint('خطأ في تحميل المهمة الفرعية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء مهمة فرعية جديدة
  Future<bool> createSubtask(int taskId, String title) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newSubtask = await _apiService.createSubtask(taskId, title);
      _allSubtasks.add(newSubtask);
      _applyFilters();
      debugPrint('تم إنشاء مهمة فرعية جديدة: ${newSubtask.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المهمة الفرعية: $e';
      debugPrint('خطأ في إنشاء المهمة الفرعية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مهمة فرعية
  Future<bool> updateSubtask(int id, Subtask subtask) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.updateSubtask(id, subtask);
      final index = _allSubtasks.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allSubtasks[index] = subtask;
        _applyFilters();
      }
      debugPrint('تم تحديث المهمة الفرعية: ${subtask.title}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث المهمة الفرعية: $e';
      debugPrint('خطأ في تحديث المهمة الفرعية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مهمة فرعية
  Future<bool> deleteSubtask(int id) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await _apiService.deleteSubtask(id);
      _allSubtasks.removeWhere((s) => s.id == id);
      _applyFilters();
      debugPrint('تم حذف المهمة الفرعية');
      return true;
    } catch (e) {
      _error.value = 'خطأ في حذف المهمة الفرعية: $e';
      debugPrint('خطأ في حذف المهمة الفرعية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على المهام الفرعية حسب المهمة
  Future<void> getSubtasksByTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final subtasks = await _apiService.getSubtasksByTask(taskId);
      _taskSubtasks.assignAll(subtasks);
      debugPrint('تم تحميل ${subtasks.length} مهمة فرعية للمهمة $taskId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مهام المهمة الفرعية: $e';
      debugPrint('خطأ في تحميل مهام المهمة الفرعية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديد المهمة الفرعية كمكتملة
  Future<bool> markAsCompleted(int subtaskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // استخدام updateSubtask بدلاً من markAsCompleted
      final subtask = _allSubtasks.firstWhereOrNull((s) => s.id == subtaskId);
      if (subtask != null) {
        final updatedSubtask = subtask.copyWith(isCompleted: true);
        await _apiService.updateSubtask(subtaskId, updatedSubtask);

        final index = _allSubtasks.indexWhere((s) => s.id == subtaskId);
        if (index != -1) {
          _allSubtasks[index] = updatedSubtask;
          _applyFilters();
        }
      }
      debugPrint('تم تحديد المهمة الفرعية كمكتملة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديد المهمة الفرعية كمكتملة: $e';
      debugPrint('خطأ في تحديد المهمة الفرعية كمكتملة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديد المهمة الفرعية كغير مكتملة
  Future<bool> markAsIncomplete(int subtaskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      // استخدام updateSubtask بدلاً من markAsIncomplete
      final subtask = _allSubtasks.firstWhereOrNull((s) => s.id == subtaskId);
      if (subtask != null) {
        final updatedSubtask = subtask.copyWith(isCompleted: false);
        await _apiService.updateSubtask(subtaskId, updatedSubtask);

        final index = _allSubtasks.indexWhere((s) => s.id == subtaskId);
        if (index != -1) {
          _allSubtasks[index] = updatedSubtask;
          _applyFilters();
        }
      }
      debugPrint('تم تحديد المهمة الفرعية كغير مكتملة');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديد المهمة الفرعية كغير مكتملة: $e';
      debugPrint('خطأ في تحديد المهمة الفرعية كغير مكتملة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allSubtasks.where((subtask) {
      // مرشح البحث
      if (_searchQuery.value.isNotEmpty) {
        final query = _searchQuery.value.toLowerCase();
        if (!subtask.title.toLowerCase().contains(query)) {
          return false;
        }
      }

      // مرشح المهمة
      if (_taskFilter.value != null && subtask.taskId != _taskFilter.value) {
        return false;
      }

      // مرشح المكتملة فقط
      if (_showCompletedOnly.value && !subtask.isCompleted) {
        return false;
      }

      // مرشح المعلقة فقط
      if (_showPendingOnly.value && subtask.isCompleted) {
        return false;
      }

      return true;
    }).toList();

    _filteredSubtasks.assignAll(filtered);
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تعيين مرشح المهمة
  void setTaskFilter(int? taskId) {
    _taskFilter.value = taskId;
    _applyFilters();
  }

  /// تعيين مرشح المكتملة فقط
  void setCompletedFilter(bool showCompletedOnly) {
    _showCompletedOnly.value = showCompletedOnly;
    if (showCompletedOnly) {
      _showPendingOnly.value = false;
    }
    _applyFilters();
  }

  /// تعيين مرشح المعلقة فقط
  void setPendingFilter(bool showPendingOnly) {
    _showPendingOnly.value = showPendingOnly;
    if (showPendingOnly) {
      _showCompletedOnly.value = false;
    }
    _applyFilters();
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _taskFilter.value = null;
    _showCompletedOnly.value = false;
    _showPendingOnly.value = false;
    _applyFilters();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  @override
  Future<void> refresh() async {
    await loadAllSubtasks();
  }
}
