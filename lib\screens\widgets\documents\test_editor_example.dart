import 'package:flutter/material.dart';

import 'advanced_quill_editor_widget.dart';

/// مثال لاستخدام المحرر المحسن مع حل مشكلة ترتيب الأسطر
/// هذا المثال يوضح كيفية استخدام المحرر بعد إصلاح مشكلة ترتيب الأسطر
class TestEditorExample extends StatefulWidget {
  const TestEditorExample({super.key});

  @override
  State<TestEditorExample> createState() => _TestEditorExampleState();
}

class _TestEditorExampleState extends State<TestEditorExample> {
  String _content = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المحرر المحسن'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // تعليمات للمستخدم
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                border: Border.all(color: Colors.green.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ تم إصلاح مشكلة ترتيب الأسطر!',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'الآن عند الضغط على Enter:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('• السطر الجديد يظهر في الأسفل (الترتيب الطبيعي)'),
                  Text('• السطر القديم يبقى في الأعلى'),
                  Text('• النص العربي يظهر بشكل صحيح'),
                  Text('• المؤشر يعمل بشكل طبيعي'),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المحرر المحسن
            Expanded(
              child: AdvancedQuillEditorWidget(
                initialContent: 'مرحباً! اكتب هنا واضغط Enter لإضافة سطر جديد.\n\nستلاحظ أن الأسطر الجديدة تظهر في الأسفل بالترتيب الصحيح.',
                onContentChanged: (content) {
                  setState(() {
                    _content = content;
                  });
                },
                placeholder: 'ابدأ الكتابة هنا واختبر الضغط على Enter...',
                showToolbar: true,
                enableAutoSave: false, // إيقاف الحفظ التلقائي للاختبار
                minHeight: 300,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // معلومات المحتوى
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border.all(color: Colors.blue.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'معلومات المحتوى:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text('عدد الأحرف: ${_content.length}'),
                  Text('عدد الأسطر: ${_content.split('\n').length}'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}