import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../models/dashboard_models.dart';


import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../models/task_model.dart';
import '../../../../models/user_model.dart';
import '../../../../models/task_status_enum.dart';
import '../../../tasks/task_detail_screen.dart';

/// عنصر قائمة المهام
///
/// يعرض قائمة بالمهام
///
/// ملاحظة: يوصى بتوحيد هذا المكون مع مكونات المخططات الأخرى في مجلد widgets/charts
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class TaskListWidget extends StatelessWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات العنصر
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const TaskListWidget({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحكم المهام
    final TaskController taskController = Get.find<TaskController>();

    // تحكم المستخدمين
    final UserController userController = Get.find<UserController>();

    return Obx(() {
      final tasks = taskController.allTasks;
      final users = userController.users;

      if (tasks.isEmpty) {
        return Center(
          child: Text(
            'لا توجد مهام',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        );
      }

      // ترتيب المهام
      final sortBy = settings['sortBy'] ?? 'dueDate';
      final sortDirection = settings['sortDirection'] ?? 'asc';
      final sortedTasks = List<Task>.from(tasks);

      switch (sortBy) {
        case 'dueDate':
          sortedTasks.sort((a, b) {
            if (a.dueDate == null && b.dueDate == null) return 0;
            if (a.dueDate == null) return sortDirection == 'asc' ? 1 : -1;
            if (b.dueDate == null) return sortDirection == 'asc' ? -1 : 1;
            return sortDirection == 'asc'
                ? a.dueDate!.compareTo(b.dueDate!)
                : b.dueDate!.compareTo(a.dueDate!);
          });
          break;
        case 'priority':
          sortedTasks.sort((a, b) {
            return sortDirection == 'asc'
                ? a.priority.compareTo(b.priority)
                : b.priority.compareTo(a.priority);
          });
          break;
        case 'status':
          sortedTasks.sort((a, b) {
            return sortDirection == 'asc'
                ? a.status.compareTo(b.status)
                : b.status.compareTo(a.status);
          });
          break;
        case 'title':
          sortedTasks.sort((a, b) {
            return sortDirection == 'asc'
                ? a.title.compareTo(b.title)
                : b.title.compareTo(a.title);
          });
          break;
        case 'createdAt':
          sortedTasks.sort((a, b) {
            return sortDirection == 'asc'
                ? a.createdAt.compareTo(b.createdAt)
                : b.createdAt.compareTo(a.createdAt);
          });
          break;
      }

      // تحديد عدد المهام المراد عرضها
      final maxItems = settings['maxItems'] ?? 10;
      final displayedTasks = sortedTasks.take(maxItems).toList();

      return ListView.builder(
        itemCount: displayedTasks.length,
        itemBuilder: (context, index) {
          final task = displayedTasks[index];
          return _buildTaskItem(context, task, users);
        },
      );
    });
  }

  /// بناء عنصر المهمة
  Widget _buildTaskItem(BuildContext context, Task task, List<User> users) {
    // البحث عن المستخدم المسند إليه المهمة
    final assignee = task.assigneeId != null
        ? users.firstWhereOrNull((user) => user.id == task.assigneeId)
        : null;

    // تحديد لون الحالة
    final taskStatus = TaskStatus.fromString(task.status);
    final statusColor = _getStatusColor(taskStatus);

    // تحديد لون الأولوية
    final taskPriority = TaskPriority.fromString(task.priority);
    final priorityColor = _getPriorityColor(taskPriority);

    // تنسيق تاريخ الاستحقاق
    final dueDate = task.dueDate != null
        ? DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)
        : null;
    final dueDateText = dueDate != null
        ? DateFormat('yyyy-MM-dd').format(dueDate)
        : 'غير محدد';

    // التحقق مما إذا كانت المهمة متأخرة
    final isOverdue = dueDate != null &&
        dueDate.isBefore(DateTime.now()) &&
        task.status != TaskStatus.completed.stringValue &&
        task.status != TaskStatus.cancelled.stringValue;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        onTap: () {
          Get.to(() => TaskDetailScreen(taskId: task.id.toString()));
        },
        title: Text(
          task.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (settings['showStatus'] == true) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getStatusName(taskStatus),
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: priorityColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getPriorityName(taskPriority),
                    style: TextStyle(
                      fontSize: 12,
                      color: priorityColor,
                    ),
                  ),
                ],
              ),
            ],
            if (settings['showDueDate'] == true) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 12,
                    color: isOverdue ? Colors.red : Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ الاستحقاق: $dueDateText',
                    style: TextStyle(
                      fontSize: 12,
                      color: isOverdue ? Colors.red : Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        leading: settings['showAssignee'] == true && assignee != null
            ? CircleAvatar(
                backgroundColor: Colors.blue.withAlpha(51), // 0.2 * 255 = 51
                child: Text(
                  assignee.name.isNotEmpty
                      ? assignee.name[0].toUpperCase()
                      : '?',
                  style: TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
        trailing: null,
      ),
    );
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.waitingForInfo:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على اسم الحالة
  String _getStatusName(TaskStatus status) {
    return status.displayNameAr;
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.urgent:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على اسم الأولوية
  String _getPriorityName(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return 'منخفضة';
      case TaskPriority.medium:
        return 'متوسطة';
      case TaskPriority.high:
        return 'عالية';
      case TaskPriority.urgent:
        return 'عاجلة';
      default:
        return 'غير معروفة';
    }
  }
}
