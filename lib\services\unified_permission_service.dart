import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/permission_models.dart';
import '../services/api/permissions_api_service.dart';
import '../controllers/auth_controller.dart';

/// خدمة الصلاحيات الموحدة - متوافقة مع ASP.NET Core API
class UnifiedPermissionService extends GetxService {
  final PermissionsApiService _apiService = PermissionsApiService();
  
  // قوائم الصلاحيات
  final RxList<Permission> _permissions = <Permission>[].obs;
  final RxMap<String, bool> _userPermissions = <String, bool>{}.obs;
  
  // Getters
  List<Permission> get permissions => _permissions;
  Map<String, bool> get userPermissions => _userPermissions;

  @override
  void onInit() {
    super.onInit();
    loadPermissions();
  }

  /// تحميل جميع الصلاحيات
  Future<void> loadPermissions() async {
    try {
      final permissions = await _apiService.getAllPermissions();
      _permissions.assignAll(permissions);
      debugPrint('تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    }
  }

  /// تحميل صلاحيات المستخدم
  Future<void> loadUserPermissions(int userId) async {
    try {
      // استخدام getRolePermissions مع دور المستخدم
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser != null) {
        final userPermissions = await _apiService.getRolePermissions(currentUser.role.value);
        _userPermissions.clear();

        for (final permission in userPermissions) {
          _userPermissions[permission.name] = true;
        }

        debugPrint('تم تحميل ${userPermissions.length} صلاحية للمستخدم');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل صلاحيات المستخدم: $e');
    }
  }

  /// التحقق من صلاحية المستخدم
  bool hasPermission(String permissionName) {
    // المنطق الجديد: الاعتماد فقط على الصلاحيات الفعلية من الباك اند
    return _userPermissions[permissionName] ?? false;
  }

  /// التحقق من صلاحيات متعددة
  bool hasAnyPermission(List<String> permissionNames) {
    return permissionNames.any((permission) => hasPermission(permission));
  }

  /// التحقق من جميع الصلاحيات
  bool hasAllPermissions(List<String> permissionNames) {
    return permissionNames.every((permission) => hasPermission(permission));
  }

  /// صلاحيات الواجهات
  bool canAccessDashboard() => hasPermission('dashboard.view');
  bool canAccessTasks() => hasPermission('tasks.view');
  bool canAccessCalendar() => hasPermission('calendar.view');
  bool canAccessReports() => hasPermission('reports.view');
  bool canAccessChat() => hasPermission('chat.view');
  bool canAccessArchive() => hasPermission('archive.view');
  bool canAccessAdmin() => hasPermission('admin.view');
  bool canAccessPowerBI() => hasPermission('powerbi.view');

  /// صلاحيات المهام
  bool canCreateTask() => hasPermission('tasks.create');
  bool canEditTask() => hasPermission('tasks.edit');
  bool canDeleteTask() => hasPermission('tasks.delete');
  bool canAssignTask() => hasPermission('tasks.assign');
  bool canViewAllTasks() => hasPermission('tasks.view_all');

  /// صلاحيات المستخدمين
  bool canCreateUser() => hasPermission('users.create');
  bool canEditUser() => hasPermission('users.edit');
  bool canDeleteUser() => hasPermission('users.delete');
  bool canViewAllUsers() => hasPermission('users.view_all');
  bool canManagePermissions() => hasPermission('permissions.manage');

  /// صلاحيات التقارير
  bool canCreateReport() => hasPermission('reports.create');
  bool canEditReport() => hasPermission('reports.edit');
  bool canDeleteReport() => hasPermission('reports.delete');
  bool canExportReport() => hasPermission('reports.export');
  bool canScheduleReport() => hasPermission('reports.schedule');

  /// صلاحيات الأرشيف
  bool canUploadDocument() => hasPermission('archive.upload');
  bool canDownloadDocument() => hasPermission('archive.download');
  bool canDeleteDocument() => hasPermission('archive.delete');
  bool canManageCategories() => hasPermission('archive.manage_categories');

  /// صلاحيات النظام
  bool canManageSystem() => hasPermission('system.manage');
  bool canViewSystemLogs() => hasPermission('system.logs');
  bool canBackupSystem() => hasPermission('system.backup');
  bool canRestoreSystem() => hasPermission('system.restore');

  /// منح صلاحية للمستخدم (عبر الدور)
  Future<bool> grantPermission(int userId, int permissionId) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser != null) {
        // منح الصلاحية للدور
        await _apiService.grantPermissionToRole(currentUser.role.value, permissionId);

        // تحديث الصلاحيات محلياً
        final permission = _permissions.firstWhereOrNull((p) => p.id == permissionId);
        if (permission != null) {
          _userPermissions[permission.name] = true;
        }

        debugPrint('تم منح الصلاحية للمستخدم');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية: $e');
      return false;
    }
  }

  /// إلغاء صلاحية من المستخدم (عبر الدور)
  Future<bool> revokePermission(int userId, int permissionId) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser != null) {
        // إلغاء الصلاحية من الدور
        await _apiService.revokePermissionFromRole(currentUser.role.value, permissionId);

        // تحديث الصلاحيات محلياً
        final permission = _permissions.firstWhereOrNull((p) => p.id == permissionId);
        if (permission != null) {
          _userPermissions[permission.name] = false;
        }

        debugPrint('تم إلغاء الصلاحية من المستخدم');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية: $e');
      return false;
    }
  }

  /// منح صلاحية ديناميكية لمستخدم (حسب scope/type)
  Future<bool> grantPermissionToUser({
    required int userId,
    required String scope,
    required String type,
    String? description,
  }) async {
    try {
      // TODO: تنفيذ منح الصلاحية للمستخدم عند توفر الـ API
      debugPrint('تم منح الصلاحية للمستخدم $userId: $type - $scope');
      return true;
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية للمستخدم: $e');
      return false;
    }
  }

  /// إلغاء صلاحية ديناميكية من مستخدم (حسب scope/type)
  Future<bool> revokePermissionFromUser({
    required int userId,
    required String scope,
    required String type,
    String? description,
  }) async {
    try {
      // TODO: تنفيذ إلغاء الصلاحية من المستخدم عند توفر الـ API
      debugPrint('تم إلغاء الصلاحية من المستخدم $userId: $type - $scope');
      return true;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية من المستخدم: $e');
      return false;
    }
  }

  /// تحديث صلاحيات المستخدم الحالي
  Future<void> refreshCurrentUserPermissions() async {
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;
    
    if (currentUser != null) {
      await loadUserPermissions(currentUser.id);
    }
  }

  /// مسح الصلاحيات المحلية
  void clearPermissions() {
    _userPermissions.clear();
  }

  /// التحقق من صلاحية المستخدم عبر API المحسن
  Future<bool> checkPermissionAsync(String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية الوصول إلى: $permissionName');
      
      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // استخدام API للتحقق من الصلاحية
      final hasPermission = await _apiService.checkUserPermission(currentUser.id, permissionName);
      
      // تحديث التخزين المحلي
      _userPermissions[permissionName] = hasPermission;
      
      debugPrint('✅ نتيجة التحقق من صلاحية $permissionName: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية $permissionName: $e');
      return false;
    }
  }

  /// جلب جميع صلاحيات المستخدم الحالي من الخادم
  Future<void> loadAllUserPermissions() async {
    try {
      debugPrint('🔄 جلب جميع صلاحيات المستخدم من الخادم...');
      
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return;
      }

      // جلب جميع الصلاحيات من API المحسن
      final allPermissions = await _apiService.getAllUserPermissions(currentUser.id);
      
      // مسح الصلاحيات القديمة وإضافة الجديدة
      _userPermissions.clear();
      for (final permission in allPermissions) {
        _userPermissions[permission] = true;
      }
      
      debugPrint('✅ تم تحميل ${allPermissions.length} صلاحية للمستخدم ${currentUser.name}');
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات المستخدم: $e');
    }
  }

  /// التحقق من صلاحيات متعددة دفعة واحدة
  Future<Map<String, bool>> checkMultiplePermissions(List<String> permissionNames) async {
    try {
      debugPrint('🔍 التحقق من ${permissionNames.length} صلاحية دفعة واحدة');
      
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return Map.fromIterable(permissionNames, value: (_) => false);
      }

      // استخدام API للتحقق من صلاحيات متعددة
      final results = await _apiService.checkMultiplePermissions(currentUser.id, permissionNames);
      
      // تحديث التخزين المحلي
      for (final entry in results.entries) {
        _userPermissions[entry.key] = entry.value;
      }
      
      debugPrint('✅ تم التحقق من ${results.length} صلاحية');
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
      return Map.fromIterable(permissionNames, value: (_) => false);
    }
  }

  /// التحقق من صلاحية الوصول إلى واجهة معينة
  Future<bool> checkInterfaceAccess(String interfaceName) async {
    try {
      // خريطة ربط أسماء الواجهات بالصلاحيات
      final interfacePermissions = {
        'dashboard': 'dashboard.view',
        'tasks': 'tasks.view',
        'departments': 'departments.view',
        'messages': 'chat.view',
        'reports': 'reports.view',
        'notifications': 'notifications.view',
        'profile': 'profile.view',
        'admin': 'admin.view',
        'calendar': 'calendar.view',
        'power_bi': 'powerbi.view',
        'reports2': 'reports.view',
      };

      final permissionName = interfacePermissions[interfaceName];
      if (permissionName != null) {
        // التحقق من الصلاحية عبر API
        return await checkPermissionAsync(permissionName);
      }

      // إذا لم تكن الواجهة معرفة، ارجع false
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية الوصول للواجهة $interfaceName: $e');
      return false;
    }
  }
}
