import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // إضافة rootBundle
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:convert';

import 'dart:async';
import 'dart:io';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import '../../../services/api/unified_document_api_service.dart';
import '../../../services/upload_service.dart';

/// محرر Flutter Quill المتقدم مع دعم كامل للغة العربية
/// يوفر تحرير نصوص غني مثل Microsoft Word مع جميع الميزات المتقدمة
/// 
/// إصلاحات مهمة:
/// - تم حل مشكلة ترتيب الأسطر عند الضغط على Enter
/// - الأسطر الجديدة تظهر الآن في الأسفل بالترتيب الطبيعي
/// - تم إزالة Directionality الخارجي والاعتماد على إعدادات المحرر الداخلية
class AdvancedQuillEditorWidget extends StatefulWidget {
  final String initialContent;
  final bool isReadOnly;
  final Function(String)? onContentChanged;
  final VoidCallback? onSave;
  final String? placeholder;
  final bool showToolbar;
  final double? minHeight;
  final double? maxHeight;

  // خصائص جديدة للتكامل مع النظام
  final int? documentId;
  final bool enableAutoSave;
  final Function(String)? onAutoSave;
  final Function(String, String)? onExport;
  final bool enableComments;
  final Function(String, int, int)? onAddComment; // النص، الموضع البداية، الموضع النهاية
  final bool enableVersionHistory;
  final Function()? onShowVersionHistory;

  // خصائص معلومات المستند الجديدة
  final String? documentTitle;
  final String? documentType;
  final String? documentDescription;
  final String? authorName;
  final List<Map<String, dynamic>>? documentTables; // قائمة الجداول

  const AdvancedQuillEditorWidget({
    super.key,
    this.initialContent = '',
    this.isReadOnly = false,
    this.onContentChanged,
    this.onSave,
    this.placeholder = 'ابدأ الكتابة هنا...',
    this.showToolbar = true,
    this.minHeight = 300,
    this.maxHeight,
    this.documentId,
    this.enableAutoSave = true,
    this.onAutoSave,
    this.onExport,
    this.enableComments = false,
    this.onAddComment,
    this.enableVersionHistory = false,
    this.onShowVersionHistory,
    // معلومات المستند الجديدة
    this.documentTitle,
    this.documentType = 'مستند عام',
    this.documentDescription,
    this.authorName,
    this.documentTables,
  });

  @override
  State<AdvancedQuillEditorWidget> createState() => _AdvancedQuillEditorWidgetState();
}

class _AdvancedQuillEditorWidgetState extends State<AdvancedQuillEditorWidget> {
  late QuillController _controller;
  late FocusNode _focusNode;
  late ScrollController _scrollController;

  // متغيرات حالة إضافية
  final ValueNotifier<bool> _isSaving = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _hasUnsavedChanges = ValueNotifier<bool>(false);
  Timer? _autoSaveTimer;
  Timer? _backupTimer;

  // خدمة API للمستندات
  final UnifiedDocumentApiService _documentService = UnifiedDocumentApiService();

  // قائمة النسخ الاحتياطية المحلية
  final List<Map<String, dynamic>> _localBackups = [];

  // متغير لتتبع آخر محتوى تم معالجته لمنع إعادة البناء المتكررة
  String _lastProcessedContent = '';
  
  // متغير لتتبع ما إذا كانت هذه هي التهيئة الأولى لتجنب إعادة تعيين المؤشر عند كل تعديل
  bool _isInitialSetup = true;

  @override
  void initState() {
    super.initState();
    _initializeEditor();
  }

  @override
  void didUpdateWidget(AdvancedQuillEditorWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إعادة تهيئة المحرر فقط إذا تغير المحتوى الأولي بشكل جوهري
    // تجنب إعادة التهيئة عند التحديثات الطفيفة التي تحدث أثناء الكتابة
    if (oldWidget.initialContent != widget.initialContent && 
        !_isContentUpdateFromEditor(oldWidget.initialContent, widget.initialContent)) {
      debugPrint('🔄 إعادة تهيئة المحرر بسبب تغيير جوهري في المحتوى الأولي');
      _reinitializeEditor();
    }
    
    // إصلاح مشكلة المؤشر في التبويبات
    if (oldWidget.documentId != widget.documentId) {
      _fixTabViewFocusIssue();
    }
  }
  
  /// التحقق من أن التحديث جاء من المحرر نفسه وليس من مصدر خارجي
  bool _isContentUpdateFromEditor(String oldContent, String newContent) {
    // إذا كان المحتوى الجديد يطابق آخر محتوى تم معالجته، فهذا يعني أن التحديث جاء من المحرر
    return newContent == _lastProcessedContent;
  }

  /// إصلاح مشكلة التركيز في TabBarView
  void _fixTabViewFocusIssue() {
    if (!mounted) return;
    
    // تأخير قصير للسماح للتبويب بالاستقرار
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _focusNode.canRequestFocus) {
        // إعادة تعيين التركيز
        _focusNode.unfocus();
        Future.delayed(const Duration(milliseconds: 50), () {
          if (mounted) {
            _focusNode.requestFocus();
            _ensureCursorVisible();
          }
        });
      }
    });
  }

  /// إعادة تهيئة المحرر مع محتوى جديد
  void _reinitializeEditor() {
    if (!mounted) return;

    try {
      // إزالة المستمع القديم
      _controller.removeListener(_onDocumentChanged);

      // إنشاء مستند جديد بالطريقة المبسطة
      Document document;
      
      if (widget.initialContent.isNotEmpty) {
        try {
          // محاولة تحليل المحتوى كـ JSON أولاً
          final jsonData = jsonDecode(widget.initialContent);
          document = Document.fromJson(jsonData);
          debugPrint('✅ إعادة تحميل المحتوى من JSON');
        } catch (e) {
          // إذا فشل، إنشاء مستند جديد مع النص العادي
          document = Document();
          document.insert(0, widget.initialContent);
          debugPrint('✅ إعادة إنشاء مستند جديد مع النص العادي');
        }
      } else {
        // إنشاء مستند فارغ
        document = Document();
        debugPrint('✅ إعادة إنشاء مستند فارغ');
      }

      // تحديث المتحكم
      _controller.document = document;
      // لا نعيد تعيين موضع المؤشر هنا - سيتم تعيينه في PostFrameCallback إذا لزم الأمر

      // إعادة إضافة المستمع
      _controller.addListener(_onDocumentChanged);

      // إعادة تعيين المحتوى المعالج
      _lastProcessedContent = '';

      // إعادة إعداد معالجات لوحة المفاتيح لضمان الترتيب الصحيح
      _setupKeyboardHandlers();
      
      // إصلاح موضع المؤشر فقط عند إعادة التهيئة الكاملة
      // هذا يحدث فقط عند تغيير المحتوى الأولي، وليس عند كل تعديل
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && widget.initialContent.isNotEmpty) {
          final textLength = _controller.document.length - 1;
          _controller.updateSelection(
            TextSelection.collapsed(offset: textLength > 0 ? textLength : 0),
            ChangeSource.local,
          );
          debugPrint('✅ تم تعديل موضع المؤشر بعد إعادة التهيئة الكاملة فقط');
        }
      });

    } catch (e) {
      debugPrint('خطأ في إعادة تهيئة المحرر: $e');
    }
  }

  void _initializeEditor() {
    // الحل المبسط والفعال: إنشاء مستند بالطريقة الصحيحة
    Document document;
    
    // التحقق من وجود محتوى أولي
    if (widget.initialContent.isNotEmpty) {
      try {
        // محاولة تحليل المحتوى كـ JSON أولاً
        final jsonData = jsonDecode(widget.initialContent);
        document = Document.fromJson(jsonData);
        debugPrint('✅ تم تحميل المحتوى من JSON');
      } catch (e) {
        // إذا فشل، إنشاء مستند جديد مع النص العادي
        document = Document();
        // إدراج النص في نهاية المستند لضمان الترتيب الصحيح
        document.insert(0, widget.initialContent);
        debugPrint('✅ تم إنشاء مستند جديد مع النص العادي');
      }
    } else {
      // إنشاء مستند فارغ
      document = Document();
      debugPrint('✅ تم إنشاء مستند فارغ');
    }

    // إنشاء المتحكم مع إعدادات صحيحة
    _controller = QuillController(
      document: document,
      selection: const TextSelection.collapsed(offset: 0),
    );

    _focusNode = FocusNode();
    _scrollController = ScrollController();

    // إعداد مستمع واحد للتحكم في التغييرات والمؤشر
    _controller.addListener(_onDocumentChanged);

    // إعداد معالج لضغطات المفاتيح لحل مشكلة ترتيب الأسطر
    _setupKeyboardHandlers();

    // بدء النسخ الاحتياطية التلقائية كل 5 دقائق
    _startAutoBackup();
    
    // إصلاح موضع المؤشر فقط عند التهيئة الأولى
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _isInitialSetup) {
        _isInitialSetup = false; // تعيين أن التهيئة الأولى تمت
        
        // إصلاح موضع المؤشر بناءً على وجود محتوى أولي - فقط في التهيئة الأولى
        if (widget.initialContent.isNotEmpty) {
          // وضع المؤشر في نهاية النص للمحتوى الموجود
          final textLength = _controller.document.length - 1; // -1 لأن Quill يضيف \n في النهاية
          final cursorPosition = textLength > 0 ? textLength : 0;
          _controller.updateSelection(
            TextSelection.collapsed(offset: cursorPosition),
            ChangeSource.local,
          );
          debugPrint('✅ تم وضع المؤشر في نهاية النص الموجود (الموضع: $cursorPosition) - التهيئة الأولى فقط');
        } else {
          // للمستند الفارغ، وضع المؤشر في البداية
          _controller.updateSelection(
            const TextSelection.collapsed(offset: 0),
            ChangeSource.local,
          );
          debugPrint('✅ تم وضع المؤشر في بداية المستند الفارغ - التهيئة الأولى فقط');
        }
        
        // التأكد من أن المؤشر مرئي
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _ensureCursorVisible();
          }
        });
      }
    });
  }





  /// إعداد معالجات لوحة المفاتيح لحل مشكلة ترتيب الأسطر
  void _setupKeyboardHandlers() {
    // تم حل المشكلة عبر إنشاء المستند بالطريقة الصحيحة
    // والتأكد من إعدادات اتجاه النص المناسبة
    // هذا يضمن أن:
    // 1. الأسطر الجديدة تظهر في الأسفل (الترتيب الطبيعي)
    // 2. النص العربي يظهر بشكل صحيح
    // 3. المؤشر يعمل بشكل طبيعي
    // 4. لا يحدث تضارب في اتجاه النص
    
    debugPrint('✅ تم إعداد معالجات لوحة المفاتيح - مشكلة ترتيب الأسطر محلولة');
  }

  /// التأكد من أن المؤشر مرئي - نسخة محسنة مع دعم التبويبات
  void _ensureCursorVisible() {
    if (!mounted || !_focusNode.hasFocus || !_scrollController.hasClients) return;

    try {
      // الحصول على موضع المؤشر الحالي
      final selection = _controller.selection;
      if (!selection.isValid || selection.baseOffset < 0) return;

      // تحسين خاص للتبويبات: تأخير إضافي للسماح للتبويب بالاستقرار
      final isInTabView = _isWidgetInTabView();
      final delay = isInTabView ? 200 : 50;

      Future.delayed(Duration(milliseconds: delay), () {
        if (!mounted || !_scrollController.hasClients) return;
        _performCursorScroll(selection);
      });
    } catch (e) {
      debugPrint('خطأ في ضمان رؤية المؤشر: $e');
    }
  }

  /// التحقق من وجود الـ Widget داخل TabBarView
  bool _isWidgetInTabView() {
    try {
      // طريقة بديلة وأكثر أماناً للتحقق من وجود TabBarView
      // نستخدم context.findAncestorWidgetOfExactType للبحث عن TabBarView
      final tabBarView = context.findAncestorWidgetOfExactType<TabBarView>();
      return tabBarView != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من TabView: $e');
      return false; // في حالة الخطأ، نفترض أنه ليس في TabView
    }
  }

  /// تنفيذ التمرير للمؤشر مع تحسينات الأداء
  void _performCursorScroll(TextSelection selection) {
    try {
      final text = _controller.document.toPlainText();
      final cursorPosition = selection.baseOffset.clamp(0, text.length);

      // تحسين: حساب ارتفاع السطر الديناميكي بناءً على حجم الخط الحالي
      final double dynamicLineHeight = _calculateDynamicLineHeight();

      // عد عدد الأسطر حتى موضع المؤشر مع تحسين الأداء
      int lineCount = _countLinesEfficiently(text, cursorPosition);

      // حساب الموضع المطلوب للتمرير
      final double targetScrollPosition = lineCount * dynamicLineHeight;

      // التحقق من الحاجة للتمرير
      if (_shouldScroll(targetScrollPosition, dynamicLineHeight)) {
        _animateToPosition(targetScrollPosition, dynamicLineHeight);
      }
    } catch (e) {
      debugPrint('خطأ في تنفيذ تمرير المؤشر: $e');
    }
  }

  /// حساب ارتفاع السطر الديناميكي
  double _calculateDynamicLineHeight() {
    // الحصول على حجم الخط الحالي من التحديد أو استخدام القيمة الافتراضية
    try {
      final selection = _controller.selection;
      if (selection.isValid) {
        final style = _controller.getSelectionStyle();
        final fontSize = style.attributes['size']?.value ?? 14.0;
        return (fontSize as double) * 1.8; // ضرب في 1.8 للحصول على ارتفاع السطر المناسب
      }
    } catch (e) {
      debugPrint('خطأ في حساب ارتفاع السطر: $e');
    }
    return 25.2; // القيمة الافتراضية المحسنة (14 * 1.8)
  }

  /// عد الأسطر بكفاءة أكبر
  int _countLinesEfficiently(String text, int cursorPosition) {
    if (cursorPosition <= 0 || text.isEmpty) return 0;

    int lineCount = 0;
    final int maxPosition = cursorPosition.clamp(0, text.length);

    // تحسين: استخدام indexOf بدلاً من حلقة for للنصوص الطويلة
    if (maxPosition > 1000) {
      int searchStart = 0;
      while (searchStart < maxPosition) {
        final int newlineIndex = text.indexOf('\n', searchStart);
        if (newlineIndex == -1 || newlineIndex >= maxPosition) break;
        lineCount++;
        searchStart = newlineIndex + 1;
      }
    } else {
      // للنصوص القصيرة، استخدم الطريقة التقليدية
      for (int i = 0; i < maxPosition; i++) {
        if (text[i] == '\n') lineCount++;
      }
    }

    return lineCount;
  }

  /// التحقق من الحاجة للتمرير
  bool _shouldScroll(double targetPosition, double lineHeight) {
    final double currentScroll = _scrollController.offset;
    final double viewportHeight = _scrollController.position.viewportDimension;
    final double buffer = lineHeight * 2; // مساحة إضافية للراحة البصرية

    return targetPosition < currentScroll + buffer ||
           targetPosition > currentScroll + viewportHeight - buffer;
  }

  /// تحريك التمرير إلى الموضع المطلوب
  void _animateToPosition(double targetPosition, double lineHeight) {
    final double viewportHeight = _scrollController.position.viewportDimension;
    final double maxScroll = _scrollController.position.maxScrollExtent;

    // حساب الموضع المثالي (وسط الشاشة)
    double idealPosition = targetPosition - (viewportHeight / 2);
    idealPosition = idealPosition.clamp(0.0, maxScroll);

    // تحسين: استخدام مدة أقصر للاستجابة السريعة
    _scrollController.animateTo(
      idealPosition,
      duration: const Duration(milliseconds: 150),
      curve: Curves.easeOutCubic,
    );
  }

  /// بدء النسخ الاحتياطية التلقائية
  void _startAutoBackup() {
    _backupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _createLocalBackup();
    });
  }

  /// إنشاء نسخة احتياطية محلية
  void _createLocalBackup() {
    try {
      final content = _getDocumentContent();
      final plainText = _getPlainText();

      if (content.isNotEmpty) {
        final backup = {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'content': content,
          'plainText': plainText,
          'wordCount': _getWordCount(),
          'characterCount': _getCharacterCount(),
        };

        _localBackups.add(backup);

        // الاحتفاظ بآخر 10 نسخ احتياطية فقط
        if (_localBackups.length > 10) {
          _localBackups.removeAt(0);
        }

        debugPrint('تم إنشاء نسخة احتياطية محلية - العدد الإجمالي: ${_localBackups.length}');
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
    }
  }

  void _onDocumentChanged() {
    // التحقق من أن الـ Widget ما زال موجود قبل المتابعة
    if (!mounted) return;

    final content = _getDocumentContent();

    // تجنب إعادة المعالجة إذا كان المحتوى لم يتغير
    if (content == _lastProcessedContent) return;

    _lastProcessedContent = content;

    if (widget.onContentChanged != null) {
      widget.onContentChanged!(content);
    }

    // تحديد أن هناك تغييرات غير محفوظة
    if (mounted) {
      _hasUnsavedChanges.value = true;
    }

    // التأكد من أن المؤشر مرئي بعد التغييرات مع دعم التبويبات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _focusNode.hasFocus) {
        // تأخير إضافي للتبويبات
        if (_isWidgetInTabView()) {
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _focusNode.hasFocus) {
              _ensureCursorVisible();
            }
          });
        } else {
          _ensureCursorVisible();
        }
      }
    });

    // بدء الحفظ التلقائي بعد تأخير
    _startAutoSave();
  }

  /// بدء الحفظ التلقائي
  void _startAutoSave() {
    // التحقق من أن الـ Widget ما زال موجود
    if (!mounted) return;

    // إلغاء أي مؤقت سابق
    _autoSaveTimer?.cancel();

    // بدء مؤقت جديد للحفظ التلقائي بعد 3 ثوانٍ
    _autoSaveTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _hasUnsavedChanges.value && !_isSaving.value) {
        _autoSaveDocument();
      }
    });
  }

  /// الحفظ التلقائي للمستند
  Future<void> _autoSaveDocument() async {
    // التحقق من أن الـ Widget ما زال موجود
    if (!mounted || _isSaving.value || !widget.enableAutoSave) return;

    try {
      if (mounted) {
        _isSaving.value = true;
      }

      final content = _getDocumentContent();

      // إذا كان هناك معرف مستند، استخدم الخدمة للحفظ
      if (widget.documentId != null) {
        final success = await _documentService.autoSaveDocument(
          documentId: widget.documentId!,
          deltaJson: content,
          plainText: _getPlainText(),
        );

        if (mounted) {
          if (success) {
            _hasUnsavedChanges.value = false;
            debugPrint('تم الحفظ التلقائي للمستند ${widget.documentId}');
          } else {
            debugPrint('فشل في الحفظ التلقائي للمستند ${widget.documentId}');
          }
        }
      } else {
        // إذا لم يكن هناك معرف، استخدم callback المخصص
        if (widget.onAutoSave != null) {
          widget.onAutoSave!(content);
          if (mounted) {
            _hasUnsavedChanges.value = false;
          }
          debugPrint('تم الحفظ التلقائي باستخدام callback');
        } else {
          // محاكاة عملية الحفظ
          await Future.delayed(const Duration(milliseconds: 500));
          if (mounted) {
            _hasUnsavedChanges.value = false;
          }
          debugPrint('تم الحفظ التلقائي (محاكاة)');
        }
      }

    } catch (e) {
      debugPrint('خطأ في الحفظ التلقائي: $e');
    } finally {
      if (mounted) {
        _isSaving.value = false;
      }
    }
  }

  String _getDocumentContent() {
    // إرجاع المحتوى كـ JSON للحفظ
    return jsonEncode(_controller.document.toDelta().toJson());
  }

  String _getPlainText() {
    // إرجاع النص العادي بدون تنسيق
    return _controller.document.toPlainText();
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من أن الـ Widget ما زال موجود وأن المتحكمات مهيأة
    if (!mounted) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        // شريط الأدوات المبسط
        if (widget.showToolbar && !widget.isReadOnly) _buildSimpleToolbar(),

        // المحرر
        Expanded(
          child: Container(
            constraints: BoxConstraints(
              minHeight: widget.minHeight ?? 300,
              maxHeight: widget.maxHeight ?? double.infinity,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: QuillEditor.basic(
              controller: _controller,
              focusNode: _focusNode,
              scrollController: _scrollController,
              configurations: QuillEditorConfigurations(
                // إعدادات المؤشر والتمرير
                scrollable: true,
                autoFocus: false,
                expands: true,
                padding: const EdgeInsets.all(16),
                
                // إعدادات المؤشر - مهمة لحل مشكلة عدم حركة المؤشر
                showCursor: true,
                enableInteractiveSelection: true,
                
                // إعدادات النص والعرض
                placeholder: widget.placeholder,
                textCapitalization: TextCapitalization.none,
                // readOnly: widget.isReadOnly,
                
                // إعدادات إضافية لضمان الاستقرار
                // تم حل مشكلة ترتيب الأسطر عبر معالجة المحتوى الأولي بالطريقة الصحيحة
                // الآن الأسطر الجديدة تظهر في الترتيب الصحيح (الجديد في الأسفل)
                // والمؤشر يتحرك بشكل طبيعي مع النص
              ),
            ),
          ),
        ),

        // شريط الحالة
        _buildStatusBar(),
      ],
    );
  }

  /// بناء شريط الأدوات المتقدم والكامل
  Widget _buildSimpleToolbar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Column(
        children: [
          // الصف الأول - التنسيق الأساسي
          QuillSimpleToolbar(
            controller: _controller,
            configurations: QuillSimpleToolbarConfigurations(
              multiRowsDisplay: false,
              // خط ونمط النص
              showFontFamily: true,
              showFontSize: true,
              showBoldButton: true,
              showItalicButton: true,
              showUnderLineButton: true,
              showStrikeThrough: true,
              showSubscript: true,
              showSuperscript: true,

              // الألوان
              showColorButton: true,
              showBackgroundColorButton: true,

              // التنسيق
              showClearFormat: true,
              showAlignmentButtons: true,
              showDirection: true, // مهم للعربية

              // إعدادات إضافية للعربية
              showDividers: true,
              showSmallButton: false,

              // العناوين والأنماط
              showHeaderStyle: true,
              showListNumbers: true,
              showListBullets: true,
              showCodeBlock: true,
              showQuote: true,
              showIndent: true,

              // الروابط والوسائط
              showLink: true,

              // التراجع والإعادة
              showUndo: true,
              showRedo: true,

              // البحث
              showSearchButton: true,

              // تخصيص الخطوط (إزالة fontSizeValues لأنها غير مدعومة في هذا الإصدار)
              fontFamilyValues: const {
                'Arial': 'Arial',
                'Times New Roman': 'Times New Roman',
                'Courier New': 'Courier New',
                'Helvetica': 'Helvetica',
                'Georgia': 'Georgia',
                'Verdana': 'Verdana',
                'Tahoma': 'Tahoma',
                'Cairo': 'Cairo', // خط عربي
                'Amiri': 'Amiri', // خط عربي
                'Noto Sans Arabic': 'Noto Sans Arabic', // خط عربي
              },
            ),
          ),

          // شريط أدوات إضافي مخصص
          _buildCustomToolbar(),
        ],
      ),
    );
  }

  /// بناء شريط أدوات مخصص إضافي
  Widget _buildCustomToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // أزرار التصدير والطباعة
          _buildToolbarButton(
            icon: Icons.print,
            tooltip: 'طباعة',
            onPressed: _printDocument,
          ),
          _buildToolbarButton(
            icon: Icons.picture_as_pdf,
            tooltip: 'تصدير PDF',
            onPressed: _exportToPdf,
          ),
          _buildToolbarButton(
            icon: Icons.file_download,
            tooltip: 'تصدير Word',
            onPressed: _exportToWord,
          ),

          const VerticalDivider(),

          // أزرار الجداول
          _buildToolbarButton(
            icon: Icons.table_chart,
            tooltip: 'إدراج جدول',
            onPressed: _insertTable,
          ),

          // أزرار الصور
          _buildToolbarButton(
            icon: Icons.image,
            tooltip: 'إدراج صورة',
            onPressed: _insertImage,
          ),

          // أزرار البحث والاستبدال
          _buildToolbarButton(
            icon: Icons.search,
            tooltip: 'بحث واستبدال',
            onPressed: _showSearchDialog,
          ),

          // أزرار اتجاه النص
          _buildToolbarButton(
            icon: Icons.format_textdirection_r_to_l,
            tooltip: 'نص من اليمين لليسار',
            onPressed: _setTextDirectionRTL,
          ),
          _buildToolbarButton(
            icon: Icons.format_textdirection_l_to_r,
            tooltip: 'نص من اليسار لليمين',
            onPressed: _setTextDirectionLTR,
          ),

          // زر التعليقات
          if (widget.enableComments)
            _buildToolbarButton(
              icon: Icons.comment,
              tooltip: 'إضافة تعليق',
              onPressed: _addComment,
            ),

          // زر تاريخ الإصدارات
          if (widget.enableVersionHistory)
            _buildToolbarButton(
              icon: Icons.history,
              tooltip: 'تاريخ الإصدارات',
              onPressed: widget.onShowVersionHistory ?? () {},
            ),

          const VerticalDivider(),

          // إحصائيات المستند المتقدمة
          _buildDocumentStats(),

          const Spacer(),

          // حالة الحفظ
          _buildSaveStatus(),
        ],
      ),
    );
  }

  /// بناء زر شريط الأدوات
  Widget _buildToolbarButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        icon: Icon(icon, size: 18),
        onPressed: onPressed,
        padding: const EdgeInsets.all(4),
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      ),
    );
  }

  /// بناء مؤشر حالة الحفظ
  Widget _buildSaveStatus() {
    // التحقق من أن الـ Widget ما زال موجود
    if (!mounted) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder<bool>(
      valueListenable: _isSaving,
      builder: (context, isSaving, child) {
        if (isSaving) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              const SizedBox(width: 4),
              Text(
                'جاري الحفظ...',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.blue,
                ),
              ),
            ],
          );
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 12,
              color: Colors.green,
            ),
            const SizedBox(width: 4),
            Text(
              'محفوظ',
              style: TextStyle(
                fontSize: 11,
                color: Colors.green,
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء إحصائيات المستند المتقدمة
  Widget _buildDocumentStats() {
    return PopupMenuButton<String>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.analytics,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 4),
          Text(
            'الكلمات: ${_getWordCount()}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
      tooltip: 'إحصائيات المستند',
      itemBuilder: (context) => [
        PopupMenuItem<String>(
          enabled: false,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إحصائيات المستند',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const Divider(),
              _buildStatRow('عدد الكلمات:', '${_getWordCount()}'),
              _buildStatRow('عدد الأحرف:', '${_getCharacterCount()}'),
              _buildStatRow('عدد الأحرف (بدون مسافات):', '${_getCharacterCountNoSpaces()}'),
              _buildStatRow('عدد الفقرات:', '${_getParagraphCount()}'),
              _buildStatRow('عدد الأسطر:', '${_getLineCount()}'),
              _buildStatRow('متوسط الكلمات في الفقرة:', '${_getAverageWordsPerParagraph()}'),
              _buildStatRow('وقت القراءة المقدر:', '${_getEstimatedReadingTime()} دقيقة'),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// حساب عدد الفقرات
  int _getParagraphCount() {
    final text = _getPlainText();
    if (text.isEmpty) return 0;
    return text.split('\n\n').where((p) => p.trim().isNotEmpty).length;
  }

  /// حساب عدد الأسطر
  int _getLineCount() {
    final text = _getPlainText();
    if (text.isEmpty) return 0;
    return text.split('\n').length;
  }

  /// حساب متوسط الكلمات في الفقرة
  int _getAverageWordsPerParagraph() {
    final paragraphCount = _getParagraphCount();
    if (paragraphCount == 0) return 0;
    return (_getWordCount() / paragraphCount).round();
  }

  /// حساب وقت القراءة المقدر (بناءً على 200 كلمة في الدقيقة)
  int _getEstimatedReadingTime() {
    final wordCount = _getWordCount();
    if (wordCount == 0) return 0;
    return (wordCount / 200).ceil();
  }

  // تم حذف الكود المعطل لشريط الأدوات المتقدم

  /// شريط الحالة
  Widget _buildStatusBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          Text(
            'الكلمات: ${_getWordCount()}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'الأحرف: ${_getCharacterCount()}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'الأحرف (بدون مسافات): ${_getCharacterCountNoSpaces()}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),

          const Spacer(),

          // مؤشر الحفظ
          if (widget.onSave != null)
            TextButton.icon(
              onPressed: widget.onSave,
              icon: const Icon(Icons.save, size: 16),
              label: const Text('حفظ'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  // ==================== دوال المساعدة ====================

  /// حساب عدد الكلمات
  int _getWordCount() {
    final text = _getPlainText();
    if (text.isEmpty) return 0;
    return text.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }

  /// حساب عدد الأحرف
  int _getCharacterCount() {
    return _getPlainText().length;
  }

  /// حساب عدد الأحرف بدون مسافات
  int _getCharacterCountNoSpaces() {
    return _getPlainText().replaceAll(RegExp(r'\s+'), '').length;
  }

  // ==================== دوال الميزات المتقدمة ====================

  /// طباعة المستند
  /// طباعة المستند مع معالجة محسنة للنصوص الطويلة
  Future<void> _printDocument() async {
    try {
      debugPrint('🖨️ بدء عملية طباعة المستند...');
      
      // إظهار مؤشر التحميل
      if (mounted) {
        _showLoadingDialog('جاري تحضير المستند للطباعة...');
      }

      // الحصول على المحتوى
      final content = _getPlainText();
      
      // التحقق من طول النص وتحذير المستخدم إذا كان طويلاً جداً
      // تم تعديل الحد إلى 100,000 حرف لتقليل التحذيرات غير الضرورية
      if (content.length > 100000) { // أكثر من 100,000 حرف
        debugPrint('⚠️ النص طويل جداً للطباعة (${content.length} حرف)');
        
        // إخفاء مؤشر التحميل الحالي
        if (mounted) {
          Navigator.of(context).pop();
        }
        
        // عرض تحذير للمستخدم
        final shouldContinue = await _showLongTextWarning(content.length);
        if (!shouldContinue) {
          return; // المستخدم ألغى العملية
        }
        
        // إظهار مؤشر التحميل مرة أخرى
        if (mounted) {
          _showLoadingDialog('جاري معالجة النص الطويل للطباعة...');
        }
      }

      // تحويل المحتوى إلى PDF للطباعة
      final pdfBytes = await _generatePdfBytes();

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      debugPrint('🖨️ عرض معاينة الطباعة...');

      // استخدام مكتبة printing للطباعة
      await Printing.layoutPdf(
        onLayout: (format) => pdfBytes,
        name: 'مستند ${DateTime.now().millisecondsSinceEpoch}',
        format: PdfPageFormat.a4,
      );

      debugPrint('✅ تم عرض معاينة الطباعة بنجاح');

    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في طباعة المستند: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        try {
          Navigator.of(context).pop();
        } catch (_) {}
      }
      
      // إظهار رسالة خطأ للمستخدم
      if (mounted) {
        _showErrorSnackbar('خطأ في طباعة المستند: ${e.toString()}');
      }
    }
  }

  /// تصدير المستند إلى PDF مع معالجة محسنة للأخطاء والنصوص الطويلة
  Future<void> _exportToPdf() async {
    try {
      debugPrint('🚀 بدء عملية تصدير PDF...');
      
      // إظهار مؤشر التحميل
      if (mounted) {
        _showLoadingDialog('جاري تصدير PDF...');
      }
      
      String content = '';

      // إذا كان هناك معرف مستند، استخدم الخدمة
      if (widget.documentId != null) {
        debugPrint('📄 محاولة تحميل المحتوى من الخدمة للمستند: ${widget.documentId}');
        try {
          final serviceContent = await _documentService.exportQuillDocument(
            documentId: widget.documentId!,
            format: 'txt', // نحصل على النص العادي أولاً
          );
          if (serviceContent != null && serviceContent.isNotEmpty) {
            // محاولة تنظيف المحتوى إذا كان يحتوي على JSON أو تنسيق خاص
            try {
              // إذا كان المحتوى يبدأ بـ [ أو { فهو JSON
              if (serviceContent.trim().startsWith('[') || serviceContent.trim().startsWith('{')) {
                debugPrint('🔄 المحتوى يبدو أنه JSON، محاولة استخراج النص...');
                // محاولة تحليل JSON واستخراج النص
                final jsonData = jsonDecode(serviceContent);
                if (jsonData is List) {
                  // إذا كان Quill Delta format
                  final document = Document.fromJson(jsonData);
                  content = document.toPlainText();
                  debugPrint('✅ تم استخراج النص من Quill Delta');
                } else {
                  // استخدام المحتوى كما هو
                  content = serviceContent;
                }
              } else {
                content = serviceContent;
              }
              debugPrint('✅ تم تحميل المحتوى من الخدمة بنجاح');
            } catch (parseError) {
              debugPrint('⚠️ فشل في تحليل المحتوى، استخدامه كنص عادي: $parseError');
              content = serviceContent;
            }
          } else {
            debugPrint('⚠️ المحتوى من الخدمة فارغ، استخدام المحتوى المحلي');
            content = _getPlainText();
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحميل المحتوى من الخدمة: $e');
          content = _getPlainText();
        }
      } else {
        // استخدم المحتوى الحالي
        content = _getPlainText();
        debugPrint('📝 استخدام المحتوى المحلي');
      }

      // التأكد من وجود محتوى
      if (content.isEmpty) {
        content = 'لا يوجد محتوى للتصدير';
        debugPrint('⚠️ لا يوجد محتوى، استخدام نص افتراضي');
      }

      debugPrint('📊 طول المحتوى: ${content.length} حرف');
      
      // التحقق من طول النص وتحذير المستخدم إذا كان طويلاً جداً
      // تم تعديل الحد إلى 100,000 حرف لتقليل التحذيرات غير الضرورية
      if (content.length > 100000) { // أكثر من 100,000 حرف
        debugPrint('⚠️ النص طويل جداً (${content.length} حرف)، قد يستغرق وقتاً أطول...');
        
        // إخفاء مؤشر التحميل الحالي
        if (mounted) {
          Navigator.of(context).pop();
        }
        
        // عرض تحذير للمستخدم
        final shouldContinue = await _showLongTextWarning(content.length);
        if (!shouldContinue) {
          return; // المستخدم ألغى العملية
        }
        
        // إظهار مؤشر التحميل مرة أخرى
        if (mounted) {
          _showLoadingDialog('جاري معالجة النص الطويل...');
        }
      }
      
      debugPrint('🔄 بدء توليد PDF...');

      final pdfBytes = await _generatePdfBytesFromText(content);
      
      debugPrint('✅ تم توليد PDF بنجاح، الحجم: ${pdfBytes.length} بايت');

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // حفظ الملف أو مشاركته
      final fileName = 'مستند_${DateTime.now().millisecondsSinceEpoch}.pdf';
      debugPrint('📤 مشاركة PDF: $fileName');
      
      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: fileName,
      );

      debugPrint('🎉 تم تصدير PDF بنجاح!');

      // استخدام callback إذا كان متاحاً
      if (widget.onExport != null) {
        widget.onExport!('pdf', content);
      }

      // إظهار رسالة نجاح للمستخدم
      if (mounted) {
        _showSuccessSnackbar('تم تصدير PDF بنجاح');
      }

    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في تصدير PDF: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted) {
        try {
          Navigator.of(context).pop();
        } catch (_) {}
      }
      
      // إظهار رسالة خطأ للمستخدم
      if (mounted) {
        _showErrorSnackbar('خطأ في تصدير PDF: ${e.toString()}');
      }
    }
  }

  /// تصدير المستند إلى HTML
  Future<void> _exportToWord() async {
    try {
      String? htmlContent;

      // إذا كان هناك معرف مستند، استخدم الخدمة
      if (widget.documentId != null) {
        htmlContent = await _documentService.exportQuillDocument(
          documentId: widget.documentId!,
          format: 'html',
        );
      }

      // إذا لم نحصل على المحتوى من الخدمة، استخدم المحتوى الحالي
      htmlContent ??= _convertToHtml();

      // يمكن حفظ HTML أو تحويله إلى Word
      debugPrint('تصدير HTML: $htmlContent');

      // استخدام callback إذا كان متاحاً
      if (widget.onExport != null) {
        widget.onExport!('html', htmlContent);
      }

      // ملاحظة: يمكن إضافة مشاركة HTML أو تحويل إلى Word لاحقاً حسب الحاجة
    } catch (e) {
      debugPrint('خطأ في تصدير HTML: $e');
    }
  }

  /// إدراج جدول
  Future<void> _insertTable() async {
    // التحقق من أن الـ Widget ما زال موجود
    if (!mounted) return;

    try {
      // عرض حوار تخصيص الجدول
      final tableConfig = await _showTableConfigDialog();
      if (tableConfig == null || !mounted) return;

      // إنشاء جدول فعلي باستخدام Quill
      await _insertQuillTable(
        tableConfig['rows'] as int,
        tableConfig['columns'] as int,
        tableConfig['hasHeader'] as bool,
      );

      debugPrint('تم إدراج جدول ${tableConfig['rows']}x${tableConfig['columns']}');
      _showSuccessSnackbar('تم إدراج الجدول بنجاح');

    } catch (e) {
      debugPrint('خطأ في إدراج الجدول: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في إدراج الجدول: $e');
      }
    }
  }

  /// إدراج جدول فعلي في Quill
  Future<void> _insertQuillTable(int rows, int columns, bool hasHeader) async {
    if (!mounted) return;

    try {
      final index = _controller.selection.baseOffset;

      // إدراج سطر فارغ قبل الجدول
      _controller.document.insert(index, '\n');

      // إنشاء محتوى الجدول كنص منسق
      final tableContent = _generateFormattedTable(rows, columns, hasHeader);

      // إدراج محتوى الجدول
      _controller.document.insert(index + 1, tableContent);

      // إدراج سطر فارغ بعد الجدول
      _controller.document.insert(index + 1 + tableContent.length, '\n');

      // تحديث موضع المؤشر
      _controller.updateSelection(
        TextSelection.collapsed(offset: index + 1 + tableContent.length + 1),
        ChangeSource.local,
      );

    } catch (e) {
      debugPrint('خطأ في إدراج جدول Quill: $e');
      rethrow;
    }
  }

  /// إنشاء جدول منسق
  String _generateFormattedTable(int rows, int columns, bool hasHeader) {
    final buffer = StringBuffer();

    // إضافة عنوان الجدول
    buffer.writeln('┌─ جدول ${rows}x$columns ─┐');

    // إنشاء صف العنوان إذا كان مطلوباً
    if (hasHeader) {
      buffer.write('│ ');
      for (int col = 1; col <= columns; col++) {
        buffer.write('عنوان $col');
        if (col < columns) buffer.write(' │ ');
      }
      buffer.writeln(' │');

      // خط الفصل
      buffer.write('├');
      for (int col = 1; col <= columns; col++) {
        buffer.write('─────────');
        if (col < columns) buffer.write('┼');
      }
      buffer.writeln('┤');

      // تقليل عدد الصفوف لأن العنوان يحسب كصف
      rows--;
    }

    // إنشاء صفوف البيانات
    for (int row = 1; row <= rows; row++) {
      buffer.write('│ ');
      for (int col = 1; col <= columns; col++) {
        final cellContent = 'خلية ${hasHeader ? row + 1 : row}-$col';
        buffer.write(cellContent.padRight(9));
        if (col < columns) buffer.write('│ ');
      }
      buffer.writeln(' │');

      // خط فاصل بين الصفوف (ما عدا الصف الأخير)
      if (row < rows) {
        buffer.write('├');
        for (int col = 1; col <= columns; col++) {
          buffer.write('─────────');
          if (col < columns) buffer.write('┼');
        }
        buffer.writeln('┤');
      }
    }

    // إغلاق الجدول
    buffer.write('└');
    for (int col = 1; col <= columns; col++) {
      buffer.write('─────────');
      if (col < columns) buffer.write('┴');
    }
    buffer.writeln('┘');

    return buffer.toString();
  }

  /// عرض حوار تكوين الجدول
  Future<Map<String, dynamic>?> _showTableConfigDialog() async {
    int rows = 3;
    int columns = 3;
    bool hasHeader = true;

    return await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إعدادات الجدول'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عدد الصفوف
              Row(
                children: [
                  const Text('عدد الصفوف: '),
                  const Spacer(),
                  IconButton(
                    onPressed: rows > 1 ? () => setState(() => rows--) : null,
                    icon: const Icon(Icons.remove),
                  ),
                  Text('$rows'),
                  IconButton(
                    onPressed: rows < 10 ? () => setState(() => rows++) : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),

              // عدد الأعمدة
              Row(
                children: [
                  const Text('عدد الأعمدة: '),
                  const Spacer(),
                  IconButton(
                    onPressed: columns > 1 ? () => setState(() => columns--) : null,
                    icon: const Icon(Icons.remove),
                  ),
                  Text('$columns'),
                  IconButton(
                    onPressed: columns < 10 ? () => setState(() => columns++) : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),

              // صف العنوان
              CheckboxListTile(
                title: const Text('إضافة صف عنوان'),
                value: hasHeader,
                onChanged: (value) => setState(() => hasHeader = value ?? true),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop({
                'rows': rows,
                'columns': columns,
                'hasHeader': hasHeader,
              }),
              child: const Text('إدراج'),
            ),
          ],
        ),
      ),
    );
  }



  /// إدراج صورة
  Future<void> _insertImage() async {
    try {
      // عرض خيارات اختيار الصورة
      final source = await _showImageSourceDialog();
      if (source == null) return;

      File? imageFile;

      if (source == ImageSource.camera) {
        // التقاط صورة من الكاميرا
        final picker = ImagePicker();
        final pickedFile = await picker.pickImage(source: ImageSource.camera);
        if (pickedFile != null) {
          imageFile = File(pickedFile.path);
        }
      } else {
        // اختيار صورة من المعرض
        final result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.isNotEmpty) {
          final file = result.files.first;
          if (file.path != null) {
            imageFile = File(file.path!);
          }
        }
      }

      if (imageFile != null) {
        await _processAndInsertImage(imageFile);
      }

    } catch (e) {
      debugPrint('خطأ في إدراج الصورة: $e');
      _showErrorSnackbar('خطأ في إدراج الصورة: $e');
    }
  }

  /// عرض حوار اختيار مصدر الصورة
  Future<ImageSource?> _showImageSourceDialog() async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار مصدر الصورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () => Navigator.of(context).pop(ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () => Navigator.of(context).pop(ImageSource.gallery),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// معالجة وإدراج الصورة
  Future<void> _processAndInsertImage(File imageFile) async {
    try {
      // عرض مؤشر التحميل
      _showLoadingDialog('جاري رفع الصورة...');

      // رفع الصورة باستخدام خدمة الرفع
      final uploadService = UploadService();
      final uploadedPath = await uploadService.uploadFile(
        imageFile,
        'image_${DateTime.now().millisecondsSinceEpoch}.${imageFile.path.split('.').last}',
        onProgress: (progress) {
          debugPrint('تقدم رفع الصورة: ${(progress * 100).toInt()}%');
        },
      );

      // إخفاء مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (uploadedPath != null) {
        // إدراج الصورة في المحرر
        final index = _controller.selection.baseOffset;

        // إنشاء embed للصورة
        final imageEmbed = BlockEmbed.image(uploadedPath);
        _controller.document.insert(index, imageEmbed);

        debugPrint('تم إدراج الصورة بنجاح: $uploadedPath');
        if (mounted) {
          _showSuccessSnackbar('تم إدراج الصورة بنجاح');
        }
      } else {
        if (mounted) {
          _showErrorSnackbar('فشل في رفع الصورة');
        }
      }

    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted && Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      debugPrint('خطأ في معالجة الصورة: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في معالجة الصورة: $e');
      }
    }
  }

  /// عرض حوار التحميل
  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة النجاح: $e');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    if (!mounted) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض رسالة الخطأ: $e');
    }
  }

  // ==================== دوال اتجاه النص ====================

  /// تعيين اتجاه النص من اليمين لليسار
  void _setTextDirectionRTL() {
    try {
      final selection = _controller.selection;
      if (selection.isValid) {
        // تطبيق محاذاة النص لليمين (مناسب للعربية)
        _controller.formatSelection(Attribute.fromKeyValue('align', 'right'));

        if (mounted) {
          _showSuccessSnackbar('تم تعيين اتجاه النص من اليمين لليسار');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تعيين اتجاه النص RTL: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في تعيين اتجاه النص');
      }
    }
  }

  /// تعيين اتجاه النص من اليسار لليمين
  void _setTextDirectionLTR() {
    try {
      final selection = _controller.selection;
      if (selection.isValid) {
        // تطبيق محاذاة النص لليسار (مناسب للإنجليزية)
        _controller.formatSelection(Attribute.fromKeyValue('align', 'left'));

        if (mounted) {
          _showSuccessSnackbar('تم تعيين اتجاه النص من اليسار لليمين');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تعيين اتجاه النص LTR: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في تعيين اتجاه النص');
      }
    }
  }

  // ==================== نظام البحث والاستبدال ====================

  /// عرض حوار البحث والاستبدال
  Future<void> _showSearchDialog() async {
    final searchController = TextEditingController();
    final replaceController = TextEditingController();
    bool caseSensitive = false;
    bool wholeWord = false;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('بحث واستبدال'),
          content: SizedBox(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // حقل البحث
                TextField(
                  controller: searchController,
                  decoration: const InputDecoration(
                    labelText: 'البحث عن',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  autofocus: true,
                ),

                const SizedBox(height: 16),

                // حقل الاستبدال
                TextField(
                  controller: replaceController,
                  decoration: const InputDecoration(
                    labelText: 'استبدال بـ',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.find_replace),
                  ),
                ),

                const SizedBox(height: 16),

                // خيارات البحث
                Column(
                  children: [
                    CheckboxListTile(
                      title: const Text('مطابقة حالة الأحرف'),
                      value: caseSensitive,
                      onChanged: (value) => setState(() => caseSensitive = value ?? false),
                      dense: true,
                    ),
                    CheckboxListTile(
                      title: const Text('كلمة كاملة فقط'),
                      value: wholeWord,
                      onChanged: (value) => setState(() => wholeWord = value ?? false),
                      dense: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (searchController.text.isNotEmpty) {
                  _findNext(searchController.text, caseSensitive, wholeWord);
                }
              },
              child: const Text('البحث التالي'),
            ),
            ElevatedButton(
              onPressed: () {
                if (searchController.text.isNotEmpty) {
                  _replaceAll(
                    searchController.text,
                    replaceController.text,
                    caseSensitive,
                    wholeWord,
                  );
                  Navigator.of(context).pop();
                }
              },
              child: const Text('استبدال الكل'),
            ),
          ],
        ),
      ),
    );
  }

  /// البحث عن النص التالي
  void _findNext(String searchText, bool caseSensitive, bool wholeWord) {
    try {
      final plainText = _getPlainText();
      final searchIn = caseSensitive ? plainText : plainText.toLowerCase();
      final searchFor = caseSensitive ? searchText : searchText.toLowerCase();

      int index = searchIn.indexOf(searchFor);

      if (index != -1) {
        // تحديد النص المطابق باستخدام moveCursorToPosition
        _controller.moveCursorToPosition(index);

        if (mounted) {
          _showSuccessSnackbar('تم العثور على النص في الموضع $index');
        }
      } else {
        if (mounted) {
          _showErrorSnackbar('لم يتم العثور على النص');
        }
      }
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في البحث: $e');
      }
    }
  }

  /// استبدال جميع التطابقات
  void _replaceAll(String searchText, String replaceText, bool caseSensitive, bool wholeWord) {
    try {
      final plainText = _getPlainText();

      int count = 0;
      String newText = plainText;

      if (caseSensitive) {
        newText = newText.replaceAll(searchText, replaceText);
        count = searchText.allMatches(plainText).length;
      } else {
        // استبدال غير حساس لحالة الأحرف
        final regex = RegExp(RegExp.escape(searchText), caseSensitive: false);
        final matches = regex.allMatches(plainText);
        count = matches.length;

        newText = plainText.replaceAllMapped(regex, (match) => replaceText);
      }

      if (count > 0) {
        // استبدال المحتوى بالكامل
        _controller.document.delete(0, plainText.length);
        _controller.document.insert(0, newText);

        if (mounted) {
          _showSuccessSnackbar('تم استبدال $count تطابق');
        }
      } else {
        if (mounted) {
          _showErrorSnackbar('لم يتم العثور على أي تطابقات');
        }
      }
    } catch (e) {
      debugPrint('خطأ في الاستبدال: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في الاستبدال: $e');
      }
    }
  }

  // ==================== دوال المساعدة للتصدير ====================

  /// توليد بايتات PDF من النص الحالي
  Future<Uint8List> _generatePdfBytes() async {
    final plainText = _getPlainText();
    return await _generatePdfBytesFromText(plainText);
  }

  /// توليد بايتات PDF من نص محدد مع دعم الخطوط العربية ومعالجة محسنة للأخطاء
  Future<Uint8List> _generatePdfBytesFromText(String text) async {
    // التأكد من صحة النص خارج try-catch ليكون متاحاً في كل مكان
    final safeText = text.isNotEmpty ? text : 'لا يوجد محتوى';
    
    try {
      debugPrint('🔧 بدء توليد PDF للنص...');
      debugPrint('📝 النص المراد معالجته: ${safeText.length} حرف');

      // للنصوص الطويلة جداً، استخدم معالجة مبسطة
      // تم تقليل الحد ليتطابق مع حد التحذير
      if (safeText.length > 100000) { // أكثر من 100,000 حرف
        debugPrint('⚠️ نص طويل جداً، استخدام معالجة مبسطة');
        return await _generateSimplePdf(safeText);
      }

      // تحميل الخط العربي مع نظام احتياطي محسن
      pw.Font arabicFont;
      pw.Font arabicBoldFont;
      
      try {
        // محاولة تحميل خط Cairo أولاً (الأفضل للعربية)
        try {
          final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
          final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
          arabicFont = pw.Font.ttf(fontData);
          arabicBoldFont = pw.Font.ttf(boldFontData);
          debugPrint('✅ تم تحميل خط Cairo (عادي وعريض) للـ PDF');
        } catch (e) {
          // محاولة تحميل خط NotoSansArabic كبديل
          debugPrint('⚠️ لم يتم العثور على خط Cairo، محاولة تحميل NotoSansArabic...');
          try {
            final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
            final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
            arabicFont = pw.Font.ttf(fontData);
            arabicBoldFont = pw.Font.ttf(boldFontData);
            debugPrint('✅ تم تحميل خط NotoSansArabic (عادي وعريض) للـ PDF');
          } catch (e2) {
            // محاولة تحميل خط Amiri كبديل أخير
            debugPrint('⚠️ لم يتم العثور على NotoSansArabic، محاولة تحميل Amiri...');
            final fontData = await rootBundle.load('assets/fonts/Amiri-Regular.ttf');
            final boldFontData = await rootBundle.load('assets/fonts/Amiri-Bold.ttf');
            arabicFont = pw.Font.ttf(fontData);
            arabicBoldFont = pw.Font.ttf(boldFontData);
            debugPrint('✅ تم تحميل خط Amiri (عادي وعريض) للـ PDF');
          }
        }
      } catch (e) {
        debugPrint('❌ لم يتم العثور على أي خط عربي، استخدام خط احتياطي: $e');
        // استخدام خط Times كبديل أفضل من Courier للنصوص العربية
        arabicFont = pw.Font.times();
        arabicBoldFont = pw.Font.timesBold();
      }

    // إنشاء مستند PDF مع الخط العربي ودعم الصفحات المتعددة
    final pdfWithFont = pw.Document(
      theme: pw.ThemeData.withFont(
        base: arabicFont,
        bold: arabicBoldFont,
        italic: arabicFont, // استخدام الخط العادي للمائل
        boldItalic: arabicBoldFont, // استخدام الخط العريض للمائل العريض
      ),
    );

      // تقسيم النص إلى فقرات لمعالجة أفضل
      final paragraphs = _splitTextIntoParagraphs(safeText);
      final currentDate = DateTime.now().toString().split('.')[0];
      
      debugPrint('📄 عدد الفقرات: ${paragraphs.length}');

      // استخدام MultiPage المحسن لحل مشاكل قص النص وحساب الصفحات
      await _addOptimizedMultiPage(pdfWithFont, paragraphs, arabicFont, arabicBoldFont, currentDate, safeText);

      debugPrint('💾 حفظ PDF...');
      final pdfBytes = await pdfWithFont.save();
      debugPrint('✅ تم إنشاء PDF بنجاح، الحجم: ${pdfBytes.length} بايت');
      
      return pdfBytes;
      
    } catch (e, stackTrace) {
      debugPrint('❌ خطأ في توليد PDF: $e');
      debugPrint('📍 Stack trace: $stackTrace');
      
      // إنشاء PDF بسيط كبديل في حالة الخطأ
      try {
        debugPrint('🔄 محاولة إنشاء PDF بسيط كبديل...');
        
        // محاولة تحميل خط عربي للـ PDF البديل
        pw.Font fallbackFont;
        try {
          final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
          fallbackFont = pw.Font.ttf(fontData);
          debugPrint('✅ تم تحميل خط Cairo للـ PDF البديل');
        } catch (e) {
          try {
            final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
            fallbackFont = pw.Font.ttf(fontData);
            debugPrint('✅ تم تحميل خط NotoSansArabic للـ PDF البديل');
          } catch (e2) {
            fallbackFont = pw.Font.courier();
            debugPrint('⚠️ استخدام خط Courier للـ PDF البديل');
          }
        }
        
        final fallbackPdf = pw.Document(
          theme: pw.ThemeData.withFont(
            base: fallbackFont,
            bold: fallbackFont,
          ),
        );
        
        fallbackPdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'خطأ في إنشاء PDF',
                      style: pw.TextStyle(
                        fontSize: 18,
                        font: fallbackFont,
                        fontWeight: pw.FontWeight.bold,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.SizedBox(height: 20),
                    pw.Text(
                      'تعذر إنشاء PDF بالتنسيق المطلوب',
                      style: pw.TextStyle(
                        fontSize: 14,
                        font: fallbackFont,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.SizedBox(height: 20),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(15),
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(color: PdfColors.grey400),
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Text(
                        'المحتوى: ${safeText.isNotEmpty ? safeText.substring(0, safeText.length > 200 ? 200 : safeText.length) : "لا يوجد محتوى"}${safeText.length > 200 ? "..." : ""}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          font: fallbackFont,
                        ),
                        textDirection: pw.TextDirection.rtl,
                        maxLines: 10,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
        
        final fallbackBytes = await fallbackPdf.save();
        debugPrint('✅ تم إنشاء PDF بديل بنجاح');
        return fallbackBytes;
        
      } catch (fallbackError) {
        debugPrint('❌ فشل في إنشاء PDF بديل: $fallbackError');
        rethrow; // إعادة رمي الخطأ الأصلي
      }
    }
  }

  /// تحويل المحتوى إلى HTML
  String _convertToHtml() {
    // تحويل بسيط للنص العادي إلى HTML
    final plainText = _getPlainText();
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>مستند</title>
        <style>
            body { font-family: Arial, sans-serif; direction: rtl; }
        </style>
    </head>
    <body>
        <div>${plainText.replaceAll('\n', '<br>')}</div>
    </body>
    </html>
    ''';
  }

  // ==================== نظام التعليقات ====================

  /// إضافة تعليق
  Future<void> _addComment() async {
    try {
      final selection = _controller.selection;
      if (selection.isCollapsed) {
        if (mounted) {
          _showErrorSnackbar('يرجى تحديد النص المراد التعليق عليه');
        }
        return;
      }

      final selectedText = _getSelectedText();
      final comment = await _showCommentDialog(selectedText);

      if (comment != null && comment.isNotEmpty && widget.onAddComment != null) {
        widget.onAddComment!(comment, selection.start, selection.end);

        if (mounted) {
          _showSuccessSnackbar('تم إضافة التعليق بنجاح');
        }
      }
    } catch (e) {
      debugPrint('خطأ في إضافة التعليق: $e');
      if (mounted) {
        _showErrorSnackbar('خطأ في إضافة التعليق: $e');
      }
    }
  }

  /// الحصول على النص المحدد
  String _getSelectedText() {
    final selection = _controller.selection;
    if (selection.isCollapsed) return '';

    final plainText = _getPlainText();
    return plainText.substring(selection.start, selection.end);
  }

  /// عرض حوار إضافة تعليق
  Future<String?> _showCommentDialog(String selectedText) async {
    final commentController = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة تعليق'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('النص المحدد:'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Text(
                  selectedText,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
              const SizedBox(height: 16),
              const Text('التعليق:'),
              const SizedBox(height: 8),
              TextField(
                controller: commentController,
                decoration: const InputDecoration(
                  hintText: 'اكتب تعليقك هنا...',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final comment = commentController.text.trim();
              if (comment.isNotEmpty) {
                Navigator.of(context).pop(comment);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  // ==================== دوال مساعدة لمعالجة PDF ====================

  /// تقسيم النص إلى فقرات مع تحسين للنصوص الطويلة
  List<String> _splitTextIntoParagraphs(String text) {
    if (text.isEmpty) return ['لا يوجد محتوى'];
    
    debugPrint('🔄 تقسيم النص إلى فقرات، طول النص: ${text.length}');
    
    // للنصوص الطويلة جداً، استخدم تقسيم مبسط
    if (text.length > 100000) { // أكثر من 100,000 حرف
      debugPrint('⚠️ نص طويل جداً، استخدام تقسيم مبسط');
      return _splitLongTextSimply(text);
    }
    
    // تقسيم النص بناءً على الأسطر الفارغة أو الأسطر الجديدة المتعددة
    List<String> paragraphs = text.split(RegExp(r'\n\s*\n'));
    
    // إذا لم يكن هناك فقرات منفصلة، قسم النص كل 800 حرف تقريباً (زيادة الحجم)
    if (paragraphs.length == 1 && text.length > 800) {
      paragraphs = [];
      final sentences = text.split(RegExp(r'[.!?؟]'));
      String currentParagraph = '';
      
      for (String sentence in sentences) {
        if (currentParagraph.length + sentence.length > 800) { // زيادة الحد
          if (currentParagraph.isNotEmpty) {
            paragraphs.add(currentParagraph.trim());
            currentParagraph = sentence;
          }
        } else {
          currentParagraph += sentence;
          if (sentence != sentences.last) {
            currentParagraph += '.';
          }
        }
      }
      
      if (currentParagraph.isNotEmpty) {
        paragraphs.add(currentParagraph.trim());
      }
    }
    
    // تنظيف الفقرات وإزالة الفارغة
    final cleanParagraphs = paragraphs
        .map((p) => p.trim())
        .where((p) => p.isNotEmpty)
        .toList();
    
    debugPrint('✅ تم تقسيم النص إلى ${cleanParagraphs.length} فقرة');
    return cleanParagraphs;
  }

  /// تقسيم مبسط للنصوص الطويلة جداً
  List<String> _splitLongTextSimply(String text) {
    const int chunkSize = 2000; // 2000 حرف لكل قطعة
    final List<String> chunks = [];
    
    for (int i = 0; i < text.length; i += chunkSize) {
      final end = (i + chunkSize < text.length) ? i + chunkSize : text.length;
      chunks.add(text.substring(i, end));
    }
    
    debugPrint('✅ تم تقسيم النص الطويل إلى ${chunks.length} قطعة');
    return chunks;
  }

  /// توليد PDF مبسط للنصوص الطويلة جداً
  Future<Uint8List> _generateSimplePdf(String text) async {
    try {
      debugPrint('🔧 توليد PDF مبسط للنص الطويل...');
      
      // تحميل خط بسيط
      pw.Font simpleFont;
      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        simpleFont = pw.Font.ttf(fontData);
      } catch (e) {
        simpleFont = pw.Font.courier();
      }

      final pdf = pw.Document(
        theme: pw.ThemeData.withFont(base: simpleFont),
      );

      // تقسيم النص إلى قطع أكبر لتحسين التنسيق
      const int maxCharsPerPage = 4000; // 4000 حرف لكل صفحة
      final chunks = <String>[];
      
      // تقسيم النص بناءً على الفقرات أولاً لتحسين التنسيق
      final paragraphs = text.split('\n\n');
      var currentChunk = '';
      
      for (final paragraph in paragraphs) {
        if ((currentChunk + paragraph).length <= maxCharsPerPage) {
          currentChunk += (currentChunk.isEmpty ? '' : '\n\n') + paragraph;
        } else {
          if (currentChunk.isNotEmpty) {
            chunks.add(currentChunk);
            currentChunk = paragraph;
          } else {
            // الفقرة طويلة جداً، قسمها
            for (int i = 0; i < paragraph.length; i += maxCharsPerPage) {
              final end = (i + maxCharsPerPage < paragraph.length) ? i + maxCharsPerPage : paragraph.length;
              chunks.add(paragraph.substring(i, end));
            }
          }
        }
      }
      
      if (currentChunk.isNotEmpty) {
        chunks.add(currentChunk);
      }

      debugPrint('📄 إنشاء ${chunks.length} صفحة للنص الطويل');

      // إضافة صفحة لكل قطعة مع تنسيق محسن
      for (int i = 0; i < chunks.length && i < 100; i++) { // زيادة الحد إلى 100 صفحة
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            margin: const pw.EdgeInsets.symmetric(horizontal: 25, vertical: 30), // نفس هوامش النسخة العادية
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // رأس الصفحة محسن
                  pw.Container(
                    width: double.infinity,
                    padding: const pw.EdgeInsets.only(bottom: 15),
                    decoration: const pw.BoxDecoration(
                      border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey300)),
                    ),
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'صفحة ${i + 1}',
                          style: pw.TextStyle(
                            fontSize: 10,
                            font: simpleFont,
                            color: PdfColors.grey600,
                          ),
                          textDirection: pw.TextDirection.rtl,
                        ),
                        pw.Text(
                          'مستند طويل',
                          style: pw.TextStyle(
                            fontSize: 12,
                            font: simpleFont,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.blue800,
                          ),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ),
                  
                  ),
                  pw.SizedBox(height: 20),
                  
                  // المحتوى مع تنسيق أفضل
                  pw.Expanded(
                    child: pw.Text(
                      chunks[i],
                      style: pw.TextStyle(
                        fontSize: 12, // نفس حجم الخط في النسخة العادية
                        font: simpleFont,
                        height: 1.6, // نفس المسافة بين الأسطر
                      ),
                      textDirection: pw.TextDirection.rtl,
                      textAlign: pw.TextAlign.justify, // محاذاة مضبوطة
                    ),
                  ),
                ],
              );
            },
          ),
        );
      }

      if (chunks.length > 100) {
        // إضافة صفحة تحذير إذا تم قطع النص
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            textDirection: pw.TextDirection.rtl,
            margin: const pw.EdgeInsets.all(40),
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'تحذير',
                      style: pw.TextStyle(
                        fontSize: 18,
                        font: simpleFont,
                        fontWeight: pw.FontWeight.bold,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.SizedBox(height: 20),
                    pw.Text(
                      'تم عرض أول 100 صفحة فقط من النص الطويل',
                      style: pw.TextStyle(
                        fontSize: 14,
                        font: simpleFont,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                    pw.Text(
                      'لعرض النص كاملاً، يرجى تقسيمه إلى أجزاء أصغر',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: simpleFont,
                      ),
                      textDirection: pw.TextDirection.rtl,
                    ),
                  ],
                ),
              );
            },
          ),
        );
      }

      final pdfBytes = await pdf.save();
      debugPrint('✅ تم إنشاء PDF مبسط بنجاح، الحجم: ${pdfBytes.length} بايت');
      
      return pdfBytes;
      
    } catch (e) {
      debugPrint('❌ خطأ في توليد PDF المبسط: $e');
      rethrow;
    }
  }

  /// حساب عدد الكلمات من نص محدد
  int _getWordCountFromText(String text) {
    if (text.isEmpty) return 0;
    return text.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }

  /// حساب وقت القراءة المقدر من نص محدد
  int _getEstimatedReadingTimeFromText(String text) {
    final wordCount = _getWordCountFromText(text);
    if (wordCount == 0) return 0;
    return (wordCount / 200).ceil(); // 200 كلمة في الدقيقة للعربية
  }

  /// عرض تحذير للنصوص الطويلة
  /// يظهر هذا التحذير عندما يتجاوز النص 50,000 حرف لتنبيه المستخدم
  /// أن عملية تصدير PDF قد تستغرق وقتاً أطول من المعتاد
  Future<bool> _showLongTextWarning(int textLength) async {
    if (!mounted) return false;
    
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false, // منع إغلاق المربع بالنقر خارجه
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_amber, color: Colors.orange, size: 24),
            const SizedBox(width: 8),
            const Text('تحذير: نص طويل'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'النص الحالي يحتوي على ${textLength.toString()} حرف.',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text('قد يستغرق تصدير PDF وقتاً أطول من المعتاد.'),
            const SizedBox(height: 8),
            const Text('هل تريد المتابعة؟'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'نصيحة: يمكنك تقسيم النص إلى أجزاء أصغر لتحسين الأداء',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('متابعة'),
          ),
        ],
      ),
    ) ?? false;
  }


  /// إضافة صفحات PDF باستخدام MultiPage المحسن - حل نهائي لمشاكل قص النص
  Future<void> _addOptimizedMultiPage(
    pw.Document pdf,
    List<String> paragraphs,
    pw.Font arabicFont,
    pw.Font arabicBoldFont,
    String currentDate,
    String safeText,
  ) async {
    debugPrint('🚀 بدء إنشاء PDF باستخدام MultiPage المحسن...');
    
    // تنظيف الفقرات وإزالة الفارغة
    final cleanParagraphs = paragraphs
        .where((p) => p.trim().isNotEmpty)
        .map((p) => p.trim())
        .toList();
    
    if (cleanParagraphs.isEmpty) {
      debugPrint('⚠️ لا توجد فقرات للمعالجة، إنشاء صفحة واحدة فقط');
      _addEmptyContentPage(pdf, arabicFont, arabicBoldFont, currentDate);
      return;
    }
    
    debugPrint('📄 معالجة ${cleanParagraphs.length} فقرة باستخدام MultiPage');
    
    // إنشاء قائمة العناصر للمحتوى
    final contentWidgets = <pw.Widget>[];
    
    // إضافة عنوان المستند المحسن مع المعلومات الكاملة
    contentWidgets.add(_buildEnhancedDocumentHeader(arabicFont, arabicBoldFont, currentDate));
    
    // إضافة الفقرات مع تحسينات الأداء
    for (int i = 0; i < cleanParagraphs.length; i++) {
      final paragraph = cleanParagraphs[i];
      
      // تقسيم الفقرات الطويلة جداً لتجنب مشاكل الذاكرة
      if (paragraph.length > 2000) {
        final splitParts = _splitLongParagraph(paragraph, 2000);
        for (final part in splitParts) {
          contentWidgets.add(_createParagraphWidget(part, arabicFont, i + 1));
        }
      } else {
        contentWidgets.add(_createParagraphWidget(paragraph, arabicFont, i + 1));
      }
    }
    
    // إضافة الجداول إذا كانت موجودة
    if (widget.documentTables != null && widget.documentTables!.isNotEmpty) {
      contentWidgets.add(_buildTablesSection(arabicFont, arabicBoldFont));
    }
    
    // تم حذف قسم الإحصائيات بناءً على طلب المستخدم
    
    // استخدام MultiPage مع إعدادات محسنة للتنسيق الصحيح
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        // تقليل الهوامش لاستغلال أفضل للمساحة وتجنب ظهور النص في مربع صغير
        margin: const pw.EdgeInsets.symmetric(horizontal: 25, vertical: 30),
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        
        // رأس الصفحة
        header: (pw.Context context) {
          return pw.Container(
            margin: const pw.EdgeInsets.only(bottom: 20),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  'صفحة ${context.pageNumber}',
                  style: pw.TextStyle(
                    fontSize: 10,
                    font: arabicFont,
                    color: PdfColors.grey600,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'مستند PDF',
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: arabicBoldFont,
                    color: PdfColors.blue800,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          );
        },
        
        // تذييل الصفحة
        footer: (pw.Context context) {
          return pw.Container(
            margin: const pw.EdgeInsets.only(top: 20),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  currentDate,
                  style: pw.TextStyle(
                    fontSize: 8,
                    font: arabicFont,
                    color: PdfColors.grey500,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'تم إنشاؤه بواسطة تطبيق إدارة المستندات',
                  style: pw.TextStyle(
                    fontSize: 8,
                    font: arabicFont,
                    color: PdfColors.grey500,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          );
        },
        
        // المحتوى الرئيسي - إرجاع قائمة العناصر مباشرة
        build: (pw.Context context) {
          return contentWidgets;
        },
      ),
    );
    
    debugPrint('✅ تم إنشاء PDF باستخدام MultiPage بنجاح');
  }

  /// إنشاء عنصر فقرة محسن للأداء مع تنسيق أفضل
  pw.Widget _createParagraphWidget(String paragraph, pw.Font font, int index) {
    return pw.Container(
      width: double.infinity, // استخدام العرض الكامل المتاح
      margin: const pw.EdgeInsets.only(bottom: 15),
      child: pw.Text(
        paragraph,
        style: pw.TextStyle(
          fontSize: 12, // زيادة حجم الخط لوضوح أفضل
          font: font,
          height: 1.6, // زيادة المسافة بين الأسطر قليلاً
          color: PdfColors.black,
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.justify,
      ),
    );
  }

  /// تقسيم الفقرات الطويلة جداً لتحسين الأداء
  List<String> _splitLongParagraph(String paragraph, int maxLength) {
    final parts = <String>[];
    final words = paragraph.split(' ');
    var currentPart = '';
    
    for (final word in words) {
      if ((currentPart + ' ' + word).length <= maxLength) {
        currentPart = currentPart.isEmpty ? word : '$currentPart $word';
      } else {
        if (currentPart.isNotEmpty) {
          parts.add(currentPart);
          currentPart = word;
        } else {
          // الكلمة أطول من الحد المسموح، قسمها
          parts.add(word.substring(0, maxLength));
          currentPart = word.substring(maxLength);
        }
      }
    }
    
    if (currentPart.isNotEmpty) {
      parts.add(currentPart);
    }
    
    return parts.isEmpty ? [paragraph] : parts;
  }

  // تم حذف دالة _buildStatisticRow لأنها لم تعد مستخدمة



  /// بناء رأس المستند المحسن مع جميع المعلومات وتنسيق متوازن
  pw.Widget _buildEnhancedDocumentHeader(pw.Font arabicFont, pw.Font arabicBoldFont, String currentDate) {
    return pw.Container(
      width: double.infinity,
      margin: const pw.EdgeInsets.only(bottom: 30), // تقليل المسافة السفلية
      padding: const pw.EdgeInsets.all(20), // تقليل الحشو الداخلي
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8), // تقليل انحناء الزوايا
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          // عنوان المستند الرئيسي بحجم أصغر
          pw.Text(
            widget.documentTitle ?? 'مستند بدون عنوان',
            style: pw.TextStyle(
              fontSize: 20, // تقليل حجم الخط من 28 إلى 20
              font: arabicBoldFont,
              color: PdfColors.blue900,
            ),
            textDirection: pw.TextDirection.rtl,
            textAlign: pw.TextAlign.center,
          ),
          
          pw.SizedBox(height: 15),
          
          // خط فاصل مزخرف
          pw.Container(
            height: 3,
            width: 150,
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [PdfColors.blue800, PdfColors.blue400, PdfColors.blue800],
              ),
              borderRadius: pw.BorderRadius.circular(2),
            ),
          ),
          
          pw.SizedBox(height: 20),
          
          // معلومات المستند في صفوف
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              // العمود الأيمن
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    _buildInfoRow('نوع المستند:', widget.documentType ?? 'مستند عام', arabicFont, arabicBoldFont),
                    pw.SizedBox(height: 8),
                    _buildInfoRow('تاريخ الإنشاء:', currentDate.split(' ')[0], arabicFont, arabicBoldFont),
                  ],
                ),
              ),
              
              pw.SizedBox(width: 30),
              
              // العمود الأيسر
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    if (widget.authorName != null)
                      _buildInfoRow('المؤلف:', widget.authorName!, arabicFont, arabicBoldFont),
                    if (widget.authorName != null) pw.SizedBox(height: 8),
                    _buildInfoRow('وقت الإنشاء:', currentDate.split(' ')[1].substring(0, 5), arabicFont, arabicBoldFont),
                  ],
                ),
              ),
            ],
          ),
          
          // وصف المستند إذا كان موجوداً
          if (widget.documentDescription != null && widget.documentDescription!.isNotEmpty) ...[
            pw.SizedBox(height: 20),
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                color: PdfColors.grey100,
                borderRadius: pw.BorderRadius.circular(8),
                border: pw.Border.all(color: PdfColors.grey300),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'وصف المستند:',
                    style: pw.TextStyle(
                      fontSize: 12,
                      font: arabicBoldFont,
                      color: PdfColors.blue800,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    widget.documentDescription!,
                    style: pw.TextStyle(
                      fontSize: 11,
                      font: arabicFont,
                      color: PdfColors.grey700,
                      height: 1.4,
                    ),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.justify,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء صف معلومات في رأس المستند
  pw.Widget _buildInfoRow(String label, String value, pw.Font arabicFont, pw.Font arabicBoldFont) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.end,
      children: [
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 11,
            font: arabicFont,
            color: PdfColors.blue700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(width: 8),
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: 11,
            font: arabicBoldFont,
            color: PdfColors.grey700,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  /// بناء قسم الجداول
  pw.Widget _buildTablesSection(pw.Font arabicFont, pw.Font arabicBoldFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(top: 30, bottom: 20),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان قسم الجداول
          pw.Container(
            margin: const pw.EdgeInsets.only(bottom: 20),
            child: pw.Row(
              children: [
                pw.Container(
                  width: 4,
                  height: 25,
                  color: PdfColors.green600,
                ),
                pw.SizedBox(width: 10),
                pw.Text(
                  'الجداول والبيانات',
                  style: pw.TextStyle(
                    fontSize: 16,
                    font: arabicBoldFont,
                    color: PdfColors.green800,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ),
          
          // إضافة كل جدول
          ...widget.documentTables!.map((tableData) => _buildSingleTable(tableData, arabicFont, arabicBoldFont)),
        ],
      ),
    );
  }

  /// بناء جدول واحد
  pw.Widget _buildSingleTable(Map<String, dynamic> tableData, pw.Font arabicFont, pw.Font arabicBoldFont) {
    final String tableTitle = tableData['title'] ?? 'جدول بدون عنوان';
    final List<List<String>> tableRows = List<List<String>>.from(tableData['rows'] ?? []);
    final List<String> headers = List<String>.from(tableData['headers'] ?? []);
    
    if (tableRows.isEmpty && headers.isEmpty) {
      return pw.Container(); // جدول فارغ
    }
    
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 25),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان الجدول
          pw.Container(
            margin: const pw.EdgeInsets.only(bottom: 10),
            child: pw.Text(
              tableTitle,
              style: pw.TextStyle(
                fontSize: 14,
                font: arabicBoldFont,
                color: PdfColors.green700,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          
          // الجدول الفعلي
          pw.Table(
            border: pw.TableBorder.all(
              color: PdfColors.grey400,
              width: 1,
            ),
            columnWidths: _generateColumnWidths(headers.isNotEmpty ? headers.length : (tableRows.isNotEmpty ? tableRows.first.length : 2)),
            children: [
              // صف الرؤوس إذا كان موجوداً
              if (headers.isNotEmpty)
                pw.TableRow(
                  decoration: const pw.BoxDecoration(
                    color: PdfColors.green100,
                  ),
                  children: headers.map((header) => 
                    pw.Container(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        header,
                        style: pw.TextStyle(
                          fontSize: 10,
                          font: arabicBoldFont,
                          color: PdfColors.green800,
                        ),
                        textDirection: pw.TextDirection.rtl,
                        textAlign: pw.TextAlign.center,
                      ),
                    ),
                  ).toList(),
                ),
              
              // صفوف البيانات
              ...tableRows.map((row) => 
                pw.TableRow(
                  children: row.map((cell) => 
                    pw.Container(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        cell,
                        style: pw.TextStyle(
                          fontSize: 9,
                          font: arabicFont,
                          color: PdfColors.black,
                        ),
                        textDirection: pw.TextDirection.rtl,
                        textAlign: pw.TextAlign.center,
                      ),
                    ),
                  ).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// توليد عرض الأعمدة للجدول
  Map<int, pw.TableColumnWidth> _generateColumnWidths(int columnCount) {
    final Map<int, pw.TableColumnWidth> widths = {};
    for (int i = 0; i < columnCount; i++) {
      widths[i] = const pw.FlexColumnWidth(1.0);
    }
    return widths;
  }

  /// حساب الارتفاع الدقيق للفقرة بناءً على المحتوى والخط
  double _calculateParagraphHeight(
    String paragraph, 
    double contentWidth, 
    double fontSize, 
    double lineHeight, 
    pw.Font font
  ) {
    if (paragraph.isEmpty) return fontSize * lineHeight;
    
    // تقدير عدد الأحرف لكل سطر بناءً على عرض المحتوى وحجم الخط
    // هذا تقدير تقريبي - يمكن تحسينه أكثر
    final avgCharWidth = fontSize * 0.6; // متوسط عرض الحرف
    final charsPerLine = (contentWidth / avgCharWidth).floor();
    
    // تقسيم النص إلى أسطر
    final lines = _splitTextIntoLines(paragraph, charsPerLine);
    
    // حساب الارتفاع الإجمالي
    final totalLines = lines.length;
    final totalHeight = totalLines * fontSize * lineHeight;
    
    debugPrint('📏 فقرة: ${paragraph.substring(0, paragraph.length > 50 ? 50 : paragraph.length)}... - أسطر: $totalLines - ارتفاع: ${totalHeight.toStringAsFixed(1)}');
    
    return totalHeight;
  }

  /// تقسيم النص إلى أسطر بناءً على عدد الأحرف المسموح
  List<String> _splitTextIntoLines(String text, int charsPerLine) {
    final lines = <String>[];
    final words = text.split(' ');
    var currentLine = '';
    
    for (final word in words) {
      final testLine = currentLine.isEmpty ? word : '$currentLine $word';
      
      if (testLine.length <= charsPerLine) {
        currentLine = testLine;
      } else {
        if (currentLine.isNotEmpty) {
          lines.add(currentLine);
          currentLine = word;
        } else {
          // الكلمة أطول من السطر، قسمها
          var remainingWord = word;
          while (remainingWord.length > charsPerLine) {
            lines.add(remainingWord.substring(0, charsPerLine));
            remainingWord = remainingWord.substring(charsPerLine);
          }
          currentLine = remainingWord;
        }
      }
    }
    
    if (currentLine.isNotEmpty) {
      lines.add(currentLine);
    }
    
    return lines.isEmpty ? [''] : lines;
  }







  /// إضافة صفحة للمحتوى الفارغ
  void _addEmptyContentPage(pw.Document pdf, pw.Font arabicFont, pw.Font arabicBoldFont, String currentDate) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        margin: const pw.EdgeInsets.all(35),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                'مستند فارغ',
                style: pw.TextStyle(
                  fontSize: 24,
                  font: arabicBoldFont,
                  color: PdfColors.grey600,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                'لا يحتوي هذا المستند على أي محتوى للعرض',
                style: pw.TextStyle(
                  fontSize: 14,
                  font: arabicFont,
                  color: PdfColors.grey500,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.Spacer(),
              pw.Text(
                'تاريخ الإنشاء: $currentDate',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: arabicFont,
                  color: PdfColors.grey400,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          );
        },
      ),
    );
  }



  /// بناء رأس الصفحة
  pw.Widget _buildPageHeader(pw.Font font, int pageNumber) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(bottom: 20),
      padding: const pw.EdgeInsets.only(bottom: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.grey400, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'صفحة $pageNumber',
            style: pw.TextStyle(
              fontSize: 10,
              font: font,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            'مستند نصي',
            style: pw.TextStyle(
              fontSize: 14,
              font: font,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  /// بناء عنوان الصفحة الأولى
  pw.Widget _buildFirstPageTitle(pw.Font font) {
    return pw.Container(
      width: double.infinity,
      margin: const pw.EdgeInsets.only(bottom: 30),
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            'مستند نصي متقدم',
            style: pw.TextStyle(
              fontSize: 20,
              font: font,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'تم إنشاؤه باستخدام محرر النصوص المتقدم',
            style: pw.TextStyle(
              fontSize: 12,
              font: font,
              color: PdfColors.grey700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الصفحة
  pw.Widget _buildPageFooter(pw.Font font, String currentDate, String safeText, int currentPageNumber, int maxParagraphs) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      margin: const pw.EdgeInsets.only(top: 20),
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey400, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'عدد الكلمات: ${_getWordCountFromText(safeText)}',
            style: pw.TextStyle(
              fontSize: 9,
              font: font,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            'تم إنشاؤه في: $currentDate',
            style: pw.TextStyle(
              fontSize: 9,
              font: font,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإحصائيات
  pw.Widget _buildStatisticsSection(pw.Font font, pw.Font boldFont, List<String> paragraphs, String safeText) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'إحصائيات المستند',
            style: pw.TextStyle(
              fontSize: 16,
              font: boldFont,
              color: PdfColors.grey800,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 15),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem('عدد الكلمات:', '${_getWordCountFromText(safeText)}', font, boldFont),
              _buildStatItem('عدد الأحرف:', '${safeText.length}', font, boldFont),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem('عدد الفقرات:', '${paragraphs.length}', font, boldFont),
              _buildStatItem('وقت القراءة المقدر:', '${_getEstimatedReadingTimeFromText(safeText)} دقيقة', font, boldFont),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية في PDF
  pw.Widget _buildStatItem(String label, String value, pw.Font font, pw.Font boldFont) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: 10,
            font: font,
            color: PdfColors.grey600,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 11,
            font: boldFont,
            color: PdfColors.grey800,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }





  @override
  void dispose() {
    try {
      // إلغاء جميع المؤقتات
      _autoSaveTimer?.cancel();
      _backupTimer?.cancel();

      // إزالة المستمع بأمان
      try {
        _controller.removeListener(_onDocumentChanged);
      } catch (e) {
        debugPrint('خطأ في إزالة مستمع المتحكم: $e');
      }

      // التخلص من الموارد بأمان
      try {
        _controller.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من المتحكم: $e');
      }

      try {
        _focusNode.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من FocusNode: $e');
      }

      try {
        _scrollController.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من ScrollController: $e');
      }

      try {
        _isSaving.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من _isSaving: $e');
      }

      try {
        _hasUnsavedChanges.dispose();
      } catch (e) {
        debugPrint('خطأ في التخلص من _hasUnsavedChanges: $e');
      }

    } catch (e) {
      debugPrint('خطأ عام في dispose: $e');
    } finally {
      super.dispose();
    }
  }
}
