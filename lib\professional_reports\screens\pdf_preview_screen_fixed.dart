/// شاشة معاينة تقرير PDF محسنة
/// 
/// تعرض التقرير بصيغة PDF مع إمكانية الطباعة والمشاركة
library;

import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';

/// شاشة معاينة PDF محسنة
class PdfPreviewScreen extends StatefulWidget {
  /// بيانات PDF
  final Uint8List pdfData;
  
  /// اسم الملف
  final String fileName;

  const PdfPreviewScreen({
    super.key,
    required this.pdfData,
    required this.fileName,
  });

  @override
  State<PdfPreviewScreen> createState() => _PdfPreviewScreenState();
}

class _PdfPreviewScreenState extends State<PdfPreviewScreen> {
  /// تحكم في عارض PDF
  final PdfViewerController _pdfViewerController = PdfViewerController();
  
  /// حالة التحميل
  bool _isLoading = true;
  
  /// رقم الصفحة الحالية
  int _currentPageNumber = 1;
  
  /// إجمالي عدد الصفحات
  int _totalPages = 0;
  
  /// مستوى التكبير الحالي
  double _currentZoomLevel = 1.0;
  
  /// حالة ملء الشاشة
  bool _isFullScreen = false;

  @override
  void dispose() {
    _pdfViewerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isFullScreen ? null : _buildAppBar(),
      body: Stack(
        children: [
          // الخلفية
          Container(
            color: Colors.grey[100],
            child: Column(
              children: [
                // شريط المعلومات (يخفى في وضع ملء الشاشة)
                if (!_isFullScreen) _buildInfoBar(),
                
                // عارض PDF
                Expanded(
                  child: Container(
                    margin: _isFullScreen ? EdgeInsets.zero : const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: _isFullScreen ? null : BorderRadius.circular(8),
                      boxShadow: _isFullScreen ? null : [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: _isFullScreen ? BorderRadius.zero : BorderRadius.circular(8),
                      child: Stack(
                        children: [
                          // عارض PDF المحسن
                          SfPdfViewer.memory(
                            widget.pdfData,
                            controller: _pdfViewerController,
                            enableDoubleTapZooming: true,
                            enableTextSelection: true,
                            canShowScrollHead: false, // سنضيف شريط تحكم مخصص
                            canShowScrollStatus: false,
                            canShowPaginationDialog: true,
                            onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                              setState(() {
                                _totalPages = details.document.pages.count;
                                _isLoading = false;
                              });
                            },
                            onPageChanged: (PdfPageChangedDetails details) {
                              setState(() {
                                _currentPageNumber = details.newPageNumber;
                              });
                            },
                            onZoomLevelChanged: (PdfZoomDetails details) {
                              setState(() {
                                _currentZoomLevel = details.newZoomLevel;
                              });
                            },
                          ),
                          
                          // مؤشر التحميل
                          if (_isLoading)
                            Container(
                              color: Colors.white.withValues(alpha: 0.8),
                              child: const Center(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(),
                                    SizedBox(height: 16),
                                    Text(
                                      'جاري تحميل التقرير...',
                                      style: TextStyle(fontSize: 16),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          
                          // شريط التحكم العائم
                          if (!_isLoading) _buildFloatingControls(),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // شريط الأزرار السفلي (يخفى في وضع ملء الشاشة)
                if (!_isFullScreen) _buildBottomBar(),
              ],
            ),
          ),
          
          // زر الخروج من وضع ملء الشاشة
          if (_isFullScreen)
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              right: 10,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.fullscreen_exit, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _isFullScreen = false;
                    });
                  },
                  tooltip: 'الخروج من ملء الشاشة',
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'معاينة التقرير',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.blue,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        // زر ملء الشاشة
        IconButton(
          icon: const Icon(Icons.fullscreen, color: Colors.white),
          onPressed: () {
            setState(() {
              _isFullScreen = true;
            });
          },
          tooltip: 'ملء الشاشة',
        ),
        // زر المشاركة
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: () => _shareReport(),
          tooltip: 'مشاركة التقرير',
        ),
        // زر الطباعة
        IconButton(
          icon: const Icon(Icons.print, color: Colors.white),
          onPressed: () => _printReport(),
          tooltip: 'طباعة التقرير',
        ),
        // زر الحفظ
        IconButton(
          icon: const Icon(Icons.download, color: Colors.white),
          onPressed: () => _saveReport(),
          tooltip: 'حفظ التقرير',
        ),
      ],
    );
  }

  /// بناء شريط المعلومات
  Widget _buildInfoBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(
          bottom: BorderSide(color: Colors.blue[100]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.picture_as_pdf, color: Colors.blue[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.fileName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.blue[700],
              ),
            ),
          ),
          if (_totalPages > 0) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$_currentPageNumber من $_totalPages',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[700],
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Text(
            '${(widget.pdfData.length / 1024).toStringAsFixed(1)} KB',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أدوات التحكم العائمة
  Widget _buildFloatingControls() {
    return Positioned(
      bottom: 20,
      right: 20,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التكبير والتصغير
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // زر التكبير
                IconButton(
                  icon: const Icon(Icons.zoom_in, color: Colors.white),
                  onPressed: () {
                    _pdfViewerController.zoomLevel = _currentZoomLevel + 0.25;
                  },
                  tooltip: 'تكبير',
                ),
                // عرض مستوى التكبير
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text(
                    '${(_currentZoomLevel * 100).toInt()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                // زر التصغير
                IconButton(
                  icon: const Icon(Icons.zoom_out, color: Colors.white),
                  onPressed: () {
                    if (_currentZoomLevel > 0.5) {
                      _pdfViewerController.zoomLevel = _currentZoomLevel - 0.25;
                    }
                  },
                  tooltip: 'تصغير',
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 10),
          
          // أزرار التنقل بين الصفحات
          if (_totalPages > 1)
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // الصفحة السابقة
                  IconButton(
                    icon: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
                    onPressed: _currentPageNumber > 1 ? () {
                      _pdfViewerController.previousPage();
                    } : null,
                    tooltip: 'الصفحة السابقة',
                  ),
                  // رقم الصفحة
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Text(
                      '$_currentPageNumber',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // الصفحة التالية
                  IconButton(
                    icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                    onPressed: _currentPageNumber < _totalPages ? () {
                      _pdfViewerController.nextPage();
                    } : null,
                    tooltip: 'الصفحة التالية',
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الطباعة الرئيسي
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _printReport,
              icon: const Icon(Icons.print, color: Colors.white),
              label: const Text(
                'طباعة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر الحفظ
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _saveReport,
              icon: Icon(Icons.download, color: Colors.blue[700]),
              label: Text(
                'حفظ',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue[700],
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.blue[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر المشاركة
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _shareReport,
              icon: Icon(Icons.share, color: Colors.green[700]),
              label: Text(
                'مشاركة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.green[700],
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.green[300]!),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// طباعة التقرير
  Future<void> _printReport() async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحضير الطباعة...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );
      
      await Printing.layoutPdf(
        onLayout: (format) async => widget.pdfData,
        name: widget.fileName,
      );
      
      // إغلاق مؤشر التحميل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
      Get.snackbar(
        'خطأ',
        'فشل في طباعة التقرير: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// حفظ التقرير
  Future<void> _saveReport() async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري حفظ التقرير...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );
      
      // الحصول على مجلد التنزيلات
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${widget.fileName}');
      
      // كتابة البيانات إلى الملف
      await file.writeAsBytes(widget.pdfData);
      
      // إغلاق مؤشر التحميل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
      Get.snackbar(
        'نجح الحفظ',
        'تم حفظ التقرير بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
        duration: const Duration(seconds: 3),
        messageText: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تم حفظ التقرير بنجاح',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              'المسار: ${file.path}',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ],
        ),
      );
      
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
      Get.snackbar(
        'خطأ',
        'فشل في حفظ التقرير: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// مشاركة التقرير
  Future<void> _shareReport() async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحضير المشاركة...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );
      
      // إنشاء ملف مؤقت
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/${widget.fileName}');
      
      // كتابة البيانات إلى الملف المؤقت
      await file.writeAsBytes(widget.pdfData);
      
      // إغلاق مؤشر التحميل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
      // مشاركة الملف
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'تقرير المستخدمين',
        subject: widget.fileName,
      );
      
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة التقرير: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }
}