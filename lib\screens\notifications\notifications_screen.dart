import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/notifications_controller.dart';
import '../../models/notification_models.dart';
import '../../services/notifications_service.dart';
// import '../../widgets/app_drawer.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/empty_state.dart';

/// شاشة الإشعارات
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // تهيئة إشعارات النظام (مرة واحدة فقط)
    NotificationsService.initialize(context);
    // الحصول على متحكم الإشعارات
    final controller = Get.find<NotificationsController>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        actions: [
          // زر تحديد الكل كمقروء
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            tooltip: 'تحديد الكل كمقروء',
            onPressed: () => _markAllAsRead(context, controller),
          ),
          // زر حذف المقروءة
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            tooltip: 'حذف المقروءة',
            onPressed: () => _deleteAllRead(context, controller),
          ),
          // زر تحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: () => controller.refresh(),
          ),
        ],
      ),
      // drawer: const AppDrawer(),
      body: Column(
        children: [
          // شريط البحث والمرشحات
          _buildSearchAndFilters(context, controller),
          
          // قائمة الإشعارات
          Expanded(
            child: Obx(() {
              // عرض مؤشر التحميل
              if (controller.isLoading) {
                return const LoadingIndicator();
              }
              
              // عرض رسالة الخطأ
              // if (controller.error.isNotEmpty) {
              //   return ErrorMessage(
              //     message: controller.error,
              //     onRetry: () => controller.refresh(),
              //   );
              // }
              
              // عرض حالة فارغة
              if (controller.filteredNotifications.isEmpty) {
                return const EmptyState(
                  icon: Icons.notifications_off,
                  title: 'لا توجد إشعارات',
                  message: 'لم يتم العثور على إشعارات تطابق المرشحات الحالية',
                );
              }
              
              // عرض قائمة الإشعارات
              return RefreshIndicator(
                onRefresh: () => controller.refresh(),
                child: ListView.builder(
                  itemCount: controller.filteredNotifications.length,
                  itemBuilder: (context, index) {
                    final notification = controller.filteredNotifications[index];
                    return _buildNotificationItem(context, notification, controller);
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
  
  /// بناء شريط البحث والمرشحات
  Widget _buildSearchAndFilters(BuildContext context, NotificationsController controller) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: const InputDecoration(
              hintText: 'بحث في الإشعارات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => controller.setSearchQuery(value),
          ),
          
          const SizedBox(height: 8),
          
          // شريط المرشحات
          Row(
            children: [
              // مرشح النوع
              Expanded(
                child: Obx(() => DropdownButtonFormField<String?>(
                  decoration: const InputDecoration(
                    labelText: 'النوع',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  value: controller.typeFilter,
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('جميع الأنواع'),
                    ),
                    ...NotificationTypes.values.map((type) => DropdownMenuItem<String?>(
                      value: type,
                      child: Text(_getNotificationTypeLabel(type)),
                    )),
                  ],
                  onChanged: (value) => controller.setTypeFilter(value),
                )),
              ),
              
              const SizedBox(width: 8),
              
              // مرشح غير المقروءة فقط
              Obx(() => FilterChip(
                label: const Text('غير المقروءة فقط'),
                selected: controller.showUnreadOnly,
                onSelected: (value) => controller.setUnreadFilter(value),
              )),
              
              const SizedBox(width: 8),
              
              // زر مسح المرشحات
              IconButton(
                icon: const Icon(Icons.clear_all),
                tooltip: 'مسح المرشحات',
                onPressed: () => controller.clearFilters(),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// بناء عنصر إشعار
  Widget _buildNotificationItem(
    BuildContext context, 
    NotificationModel notification, 
    NotificationsController controller
  ) {
    return Dismissible(
      key: Key('notification_${notification.id}'),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: Colors.blue,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(Icons.mark_email_read, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // حذف الإشعار
          return await _confirmDelete(context, notification, controller);
        } else {
          // تحديد الإشعار كمقروء
          if (!notification.isRead) {
            await controller.markAsRead(notification.id);
          }
          return false;
        }
      },
      child: Card(
        elevation: notification.isRead ? 1 : 3,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        color: notification.isRead ? null : Colors.blue.shade50,
        child: ListTile(
          leading: _buildNotificationIcon(notification.type),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(notification.content),
              const SizedBox(height: 4),
              Text(
                _formatDateTime(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          isThreeLine: true,
          trailing: !notification.isRead
              ? IconButton(
                  icon: const Icon(Icons.mark_email_read, color: Colors.blue),
                  tooltip: 'تحديد كمقروء',
                  onPressed: () => controller.markAsRead(notification.id),
                )
              : null,
          onTap: () => _onNotificationTap(context, notification, controller),
        ),
      ),
    );
  }
  
  /// عند النقر على الإشعار
  void _onNotificationTap(
    BuildContext context, 
    NotificationModel notification, 
    NotificationsController controller
  ) async {
    // تحديد الإشعار كمقروء إذا لم يكن مقروءاً
    if (!notification.isRead) {
      await controller.markAsRead(notification.id);
    }
    
    // التنقل إلى الشاشة المناسبة حسب نوع الإشعار
    switch (notification.type) {
      case NotificationTypes.taskAssigned:
      case NotificationTypes.taskUpdated:
      case NotificationTypes.taskCompleted:
        if (notification.referenceId != null) {
          Get.toNamed('/tasks/${notification.referenceId}');
        }
        break;
      case NotificationTypes.commentAdded:
        if (notification.referenceId != null) {
          Get.toNamed('/tasks/${notification.referenceId}', arguments: {'openComments': true});
        }
        break;
      case NotificationTypes.messageReceived:
        if (notification.referenceId != null) {
          Get.toNamed('/chat/${notification.referenceId}');
        }
        break;
      default:
        // عرض تفاصيل الإشعار
        _showNotificationDetails(context, notification);
        break;
    }
  }
  
  /// عرض تفاصيل الإشعار
  void _showNotificationDetails(BuildContext context, NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.content),
            const SizedBox(height: 16),
            Text(
              'تاريخ الإنشاء: ${_formatDateTime(notification.createdAt)}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            if (notification.referenceId != null) Text(
              'معرف المرجع: ${notification.referenceId}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            child: const Text('إغلاق'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
  
  /// تأكيد حذف الإشعار
  Future<bool> _confirmDelete(
    BuildContext context, 
    NotificationModel notification, 
    NotificationsController controller
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعار'),
        content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          TextButton(
            child: const Text('حذف'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await controller.deleteNotification(notification.id);
      return true;
    }
    
    return false;
  }
  
  /// تحديد جميع الإشعارات كمقروءة
  void _markAllAsRead(BuildContext context, NotificationsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديد الكل كمقروء'),
        content: const Text('هل تريد تحديد جميع الإشعارات كمقروءة؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('تحديد الكل'),
            onPressed: () {
              controller.markAllAsRead();
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
  
  /// حذف جميع الإشعارات المقروءة
  void _deleteAllRead(BuildContext context, NotificationsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الإشعارات المقروءة'),
        content: const Text('هل تريد حذف جميع الإشعارات المقروءة؟'),
        actions: [
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: const Text('حذف'),
            onPressed: () {
              controller.deleteAllRead();
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
  
  /// الحصول على أيقونة الإشعار حسب النوع
  Widget _buildNotificationIcon(String type) {
    IconData iconData;
    Color iconColor;
    
    switch (type) {
      case NotificationTypes.taskAssigned:
        iconData = Icons.assignment_ind;
        iconColor = Colors.orange;
        break;
      case NotificationTypes.taskUpdated:
        iconData = Icons.update;
        iconColor = Colors.blue;
        break;
      case NotificationTypes.taskCompleted:
        iconData = Icons.task_alt;
        iconColor = Colors.green;
        break;
      case NotificationTypes.commentAdded:
        iconData = Icons.comment;
        iconColor = Colors.purple;
        break;
      case NotificationTypes.messageReceived:
        iconData = Icons.message;
        iconColor = Colors.teal;
        break;
      case NotificationTypes.systemAlert:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.notifications;
        iconColor = Colors.grey;
        break;
    }
    
    return CircleAvatar(
      backgroundColor: iconColor.withOpacity(0.2),
      child: Icon(iconData, color: iconColor),
    );
  }
  
  /// الحصول على تسمية نوع الإشعار
  String _getNotificationTypeLabel(String type) {
    switch (type) {
      case NotificationTypes.taskAssigned:
        return 'تعيين مهمة';
      case NotificationTypes.taskUpdated:
        return 'تحديث مهمة';
      case NotificationTypes.taskCompleted:
        return 'إكمال مهمة';
      case NotificationTypes.commentAdded:
        return 'تعليق جديد';
      case NotificationTypes.messageReceived:
        return 'رسالة جديدة';
      case NotificationTypes.systemAlert:
        return 'تنبيه نظام';
      default:
        return 'غير معروف';
    }
  }
  
  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays == 0) {
      // اليوم
      return 'اليوم ${DateFormat.Hm().format(dateTime)}';
    } else if (difference.inDays == 1) {
      // الأمس
      return 'الأمس ${DateFormat.Hm().format(dateTime)}';
    } else if (difference.inDays < 7) {
      // خلال الأسبوع
      return '${_getDayName(dateTime.weekday)} ${DateFormat.Hm().format(dateTime)}';
    } else {
      // تاريخ كامل
      return DateFormat('yyyy/MM/dd HH:mm').format(dateTime);
    }
  }
  
  /// الحصول على اسم اليوم
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return '';
    }
  }
}