import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/report_controller.dart';
import '../../controllers/report_schedules_controller.dart';
import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';
import '../../models/report_schedule_models.dart' as schedule_models;
import '../../utils/responsive_helper.dart';


/// شاشة جدولة التقارير
///
/// تتيح للمستخدم إنشاء وإدارة جدولة التقارير
class ReportSchedulerScreen extends StatefulWidget {
  final int reportId;

  const ReportSchedulerScreen({
    super.key,
    required this.reportId,
  });

  @override
  State<ReportSchedulerScreen> createState() => _ReportSchedulerScreenState();
}

class _ReportSchedulerScreenState extends State<ReportSchedulerScreen> {
  final ReportController _reportController = Get.find<ReportController>();
  final ReportSchedulesController _schedulesController = Get.find<ReportSchedulesController>();
  final AuthController _authController = Get.find<AuthController>();

  EnhancedReport? _report;
  List<schedule_models.ReportSchedule> _schedules = [];

  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل التقرير
      final report = await _reportController.getReportById(widget.reportId.toString());
      if (report == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'التقرير غير موجود';
        });
        return;
      }

      // تحميل جدولات التقرير للتقرير المحدد
      await _schedulesController.loadAllSchedules();
      final schedules = _schedulesController.allSchedules
          .where((schedule) => schedule.reportId == widget.reportId)
          .toList();

      setState(() {
        _report = report;
        _schedules = schedules;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// عرض مربع حوار إنشاء جدولة جديدة
  void _showCreateScheduleDialog() {
    final formKey = GlobalKey<FormState>();
    String title = '';
    String frequency = 'daily';
    int? dayOfWeek;
    int? dayOfMonth;
    int hour = 9;
    int minute = 0;
    String recipients = '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء جدولة جديدة'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'عنوان الجدولة',
                    hintText: 'أدخل عنوان الجدولة',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال عنوان الجدولة';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    title = value;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'تكرار الجدولة',
                  ),
                  value: frequency,
                  items: const [
                    DropdownMenuItem(value: 'daily', child: Text('يومي')),
                    DropdownMenuItem(value: 'weekly', child: Text('أسبوعي')),
                    DropdownMenuItem(value: 'monthly', child: Text('شهري')),
                    DropdownMenuItem(value: 'quarterly', child: Text('ربع سنوي')),
                    DropdownMenuItem(value: 'yearly', child: Text('سنوي')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      frequency = value;
                    }
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'ساعة التنفيذ',
                          hintText: '0-23',
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: hour.toString(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'مطلوب';
                          }
                          final h = int.tryParse(value);
                          if (h == null || h < 0 || h > 23) {
                            return 'ساعة غير صالحة';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          hour = int.tryParse(value) ?? 0;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'دقيقة التنفيذ',
                          hintText: '0-59',
                        ),
                        keyboardType: TextInputType.number,
                        initialValue: minute.toString(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'مطلوب';
                          }
                          final m = int.tryParse(value);
                          if (m == null || m < 0 || m > 59) {
                            return 'دقيقة غير صالحة';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          minute = int.tryParse(value) ?? 0;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'المستلمون',
                    hintText: 'أدخل عناوين البريد الإلكتروني مفصولة بفواصل',
                  ),
                  maxLines: 2,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال عنوان بريد إلكتروني واحد على الأقل';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    recipients = value;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.of(context).pop();

                // تحويل التكرار إلى enum
                final frequencyEnum = _stringToFrequency(frequency);

                // حساب موعد التنفيذ التالي
                final nextRunTime = _calculateNextRunTime(frequencyEnum, hour, minute, dayOfWeek, dayOfMonth);

                // إنشاء جدولة التقرير
                final schedule = schedule_models.ReportSchedule(
                  id: 0, // سيتم تعيينه من قبل الخادم
                  title: title,
                  reportId: widget.reportId,
                  frequency: frequency, // استخدام النص مباشرة
                  dayOfWeek: dayOfWeek,
                  dayOfMonth: dayOfMonth,
                  hour: hour,
                  minute: minute,
                  recipients: recipients,
                  isActive: true,
                  createdAt: DateTime.now().millisecondsSinceEpoch,
                  nextExecutionAt: nextRunTime.millisecondsSinceEpoch,
                  createdBy: _authController.currentUser.value?.id ?? 0,
                );

                // حفظ الجدولة
                await _schedulesController.createSchedule(schedule);

                // تحديث القائمة
                _loadData();
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تفعيل/تعطيل الجدولة
  void _showToggleScheduleDialog(schedule_models.ReportSchedule schedule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(schedule.isActive ? 'تعطيل الجدولة' : 'تفعيل الجدولة'),
        content: Text(
          schedule.isActive
              ? 'هل أنت متأكد من تعطيل جدولة "${schedule.title}"؟'
              : 'هل أنت متأكد من تفعيل جدولة "${schedule.title}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store the message before async operation
              final message = schedule.isActive
                  ? 'تم تعطيل الجدولة بنجاح'
                  : 'تم تفعيل الجدولة بنجاح';

              // Store context reference before async operation
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              Navigator.of(context).pop();
              if (schedule.isActive) {
                await _schedulesController.pauseSchedule(schedule.id);
              } else {
                await _schedulesController.startSchedule(schedule.id);
              }
              _loadData();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(message),
                  ),
                );
              }
            },
            child: Text(schedule.isActive ? 'تعطيل' : 'تفعيل'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار حذف الجدولة
  void _showDeleteScheduleDialog(schedule_models.ReportSchedule schedule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الجدولة'),
        content: Text('هل أنت متأكد من حذف جدولة "${schedule.title}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Store context reference before async operation
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              Navigator.of(context).pop();
              await _schedulesController.deleteSchedule(schedule.id);
              _loadData();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الجدولة بنجاح'),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تحويل النص إلى enum التكرار
  ReportScheduleFrequency _stringToFrequency(String frequency) {
    switch (frequency) {
      case 'daily':
        return ReportScheduleFrequency.daily;
      case 'weekly':
        return ReportScheduleFrequency.weekly;
      case 'monthly':
        return ReportScheduleFrequency.monthly;
      case 'quarterly':
        return ReportScheduleFrequency.quarterly;
      case 'yearly':
        return ReportScheduleFrequency.yearly;
      default:
        return ReportScheduleFrequency.daily;
    }
  }

  /// حساب موعد التنفيذ التالي
  DateTime _calculateNextRunTime(ReportScheduleFrequency frequency, int hour, int minute, int? dayOfWeek, int? dayOfMonth) {
    final now = DateTime.now();
    DateTime nextRun = DateTime(now.year, now.month, now.day, hour, minute);

    // إذا كان الوقت قد مضى اليوم، ابدأ من الغد
    if (nextRun.isBefore(now)) {
      nextRun = nextRun.add(const Duration(days: 1));
    }

    switch (frequency) {
      case ReportScheduleFrequency.daily:
        return nextRun;
      case ReportScheduleFrequency.weekly:
        if (dayOfWeek != null) {
          while (nextRun.weekday != dayOfWeek) {
            nextRun = nextRun.add(const Duration(days: 1));
          }
        }
        return nextRun;
      case ReportScheduleFrequency.monthly:
        if (dayOfMonth != null) {
          nextRun = DateTime(nextRun.year, nextRun.month, dayOfMonth, hour, minute);
          if (nextRun.isBefore(now)) {
            nextRun = DateTime(nextRun.year, nextRun.month + 1, dayOfMonth, hour, minute);
          }
        }
        return nextRun;
      case ReportScheduleFrequency.quarterly:
        nextRun = DateTime(nextRun.year, nextRun.month + 3, nextRun.day, hour, minute);
        return nextRun;
      case ReportScheduleFrequency.yearly:
        nextRun = DateTime(nextRun.year + 1, nextRun.month, nextRun.day, hour, minute);
        return nextRun;
    }
  }

  /// الحصول على اسم تكرار الجدولة من النص
  String _getFrequencyDisplayName(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'يومي';
      case 'weekly':
        return 'أسبوعي';
      case 'monthly':
        return 'شهري';
      case 'quarterly':
        return 'ربع سنوي';
      case 'yearly':
        return 'سنوي';
      default:
        return frequency;
    }
  }

  /// تنسيق وقت التنفيذ التالي
  String _formatNextExecutionTime(int timestamp) {
    if (timestamp == 0) return 'غير محدد';
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_report?.title != null ? 'جدولة تقرير: ${_report!.title}' : 'جدولة التقرير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _report == null
                  ? const Center(child: Text('التقرير غير موجود'))
                  : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateScheduleDialog,
        tooltip: 'إنشاء جدولة جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent() {
    if (_schedules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.schedule,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد جدولات لهذا التقرير',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'انقر على زر "+" لإنشاء جدولة جديدة',
              style: AppStyles.bodyMedium,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ResponsiveHelper.isDesktop(context) || ResponsiveHelper.isTablet(context)
          ? _buildGridView()
          : _buildListView(),
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _schedules.length,
      itemBuilder: (context, index) {
        final schedule = _schedules[index];
        return _buildScheduleCard(schedule);
      },
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _schedules.length,
      itemBuilder: (context, index) {
        final schedule = _schedules[index];
        return _buildScheduleListItem(schedule);
      },
    );
  }

  Widget _buildScheduleCard(schedule_models.ReportSchedule schedule) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    schedule.title,
                    style: AppStyles.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Switch(
                  value: schedule.isActive,
                  onChanged: (value) {
                    _showToggleScheduleDialog(schedule);
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Spacer(),
            Row(
              children: [
                Icon(
                  Icons.repeat,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _getFrequencyDisplayName(schedule.frequency),
                  style: AppStyles.captionMedium,
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${schedule.hour.toString().padLeft(2, '0')}:${schedule.minute.toString().padLeft(2, '0')}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  'التنفيذ القادم: ${_formatNextExecutionTime(schedule.nextExecutionAt)}',
                  style: AppStyles.captionMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.delete, size: 20),
                  tooltip: 'حذف',
                  onPressed: () => _showDeleteScheduleDialog(schedule),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleListItem(schedule_models.ReportSchedule schedule) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(schedule.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_getFrequencyDisplayName(schedule.frequency)} - ${schedule.hour.toString().padLeft(2, '0')}:${schedule.minute.toString().padLeft(2, '0')}',
              style: AppStyles.captionSmall,
            ),
            Text(
              'التنفيذ القادم: ${_formatNextExecutionTime(schedule.nextExecutionAt)}',
              style: AppStyles.captionSmall,
            ),
          ],
        ),
        leading: CircleAvatar(
          backgroundColor: schedule.isActive ? Colors.green.withAlpha(51) : Colors.grey.withAlpha(51), // 0.2 * 255 = ~51
          child: Icon(
            Icons.schedule,
            color: schedule.isActive ? Colors.green : Colors.grey,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: schedule.isActive,
              onChanged: (value) {
                _showToggleScheduleDialog(schedule);
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف',
              onPressed: () => _showDeleteScheduleDialog(schedule),
            ),
          ],
        ),
      ),
    );
  }
}
