import 'user_model.dart';

/// نموذج القسم المتوافق مع ASP.NET Core API مع دعم التسلسل الهرمي
class Department {
  final int id;
  final String name;
  final String? description;
  final int? managerId;
  final bool isActive;
  final int createdAt; // Unix timestamp

  // خصائص التسلسل الهرمي
  final int? parentId;
  final int level;
  final int sortOrder;

  // Navigation properties
  final User? manager;
  final Department? parent;
  final List<Department> children;

  const Department({
    required this.id,
    required this.name,
    this.description,
    this.managerId,
    this.isActive = true,
    required this.createdAt,
    this.parentId,
    this.level = 0,
    this.sortOrder = 0,
    this.manager,
    this.parent,
    this.children = const [],
  });

  factory Department.fromJson(Map<String, dynamic> json) {
    return Department(
      id: json['id'] is int
          ? json['id']
          : int.tryParse(json['id']?.toString() ?? '0') ?? 0,
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      managerId: json['managerId'] != null ? int.tryParse(json['managerId'].toString()) : null,
      isActive: json['isActive'] is bool
          ? json['isActive']
          : (json['isActive'] != null ? json['isActive'].toString() == 'true' : true),
      createdAt: json['createdAt'] is int
          ? json['createdAt']
          : int.tryParse(json['createdAt']?.toString() ?? '0') ?? 0,
      parentId: json['parentId'] != null ? int.tryParse(json['parentId'].toString()) : null,
      level: json['level'] != null ? int.tryParse(json['level'].toString()) ?? 0 : 0,
      sortOrder: json['sortOrder'] != null ? int.tryParse(json['sortOrder'].toString()) ?? 0 : 0,
      manager: json['manager'] is Map<String, dynamic>
          ? User.fromJson(json['manager'] as Map<String, dynamic>)
          : null,
      parent: json['parent'] is Map<String, dynamic>
          ? Department.fromJson(json['parent'] as Map<String, dynamic>)
          : null,
      children: json['children'] is List
          ? (json['children'] as List)
              .whereType<Map<String, dynamic>>()
              .map((child) => Department.fromJson(child))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'managerId': managerId,
      'isActive': isActive,
      'createdAt': createdAt,
      'parentId': parentId,
      'level': level,
      'sortOrder': sortOrder,
    };
  }

  /// خصائص محسوبة
  bool get hasChildren => children.isNotEmpty;
  bool get isRoot => parentId == null;
  int get childrenCount => children.length;

  /// إنشاء نسخة محدثة من القسم
  Department copyWith({
    int? id,
    String? name,
    String? description,
    int? managerId,
    bool? isActive,
    int? createdAt,
    int? parentId,
    int? level,
    int? sortOrder,
    User? manager,
    Department? parent,
    List<Department>? children,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      managerId: managerId ?? this.managerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      parentId: parentId ?? this.parentId,
      level: level ?? this.level,
      sortOrder: sortOrder ?? this.sortOrder,
      manager: manager ?? this.manager,
      parent: parent ?? this.parent,
      children: children ?? this.children,
    );
  }



  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  @override
  String toString() {
    return 'Department(id: $id, name: $name, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Department && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء قسم جديد
class CreateDepartmentRequest {
  final String name;
  final String? description;
  final int? managerId;
  final bool isActive;

  const CreateDepartmentRequest({
    required this.name,
    this.description,
    this.managerId,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'managerId': managerId,
      'isActive': isActive,
    };
  }
}

/// نموذج طلب تحديث قسم
class UpdateDepartmentRequest {
  final String? name;
  final String? description;
  final int? managerId;
  final bool? isActive;

  const UpdateDepartmentRequest({
    this.name,
    this.description,
    this.managerId,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (name != null) json['name'] = name;
    if (description != null) json['description'] = description;
    if (managerId != null) json['managerId'] = managerId;
    if (isActive != null) json['isActive'] = isActive;
    return json;
  }
}

/// إحصائيات القسم
class DepartmentStats {
  final int departmentId;
  final String departmentName;
  final int totalEmployees;
  final int activeEmployees;
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final int overdueTasks;

  const DepartmentStats({
    required this.departmentId,
    required this.departmentName,
    required this.totalEmployees,
    required this.activeEmployees,
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.overdueTasks,
  });

  factory DepartmentStats.fromJson(Map<String, dynamic> json) {
    return DepartmentStats(
      departmentId: json['departmentId'] as int,
      departmentName: json['departmentName'] as String,
      totalEmployees: json['totalEmployees'] as int,
      activeEmployees: json['activeEmployees'] as int,
      totalTasks: json['totalTasks'] as int,
      completedTasks: json['completedTasks'] as int,
      pendingTasks: json['pendingTasks'] as int,
      overdueTasks: json['overdueTasks'] as int,
    );
  }

  /// نسبة إنجاز المهام
  double get completionRate {
    if (totalTasks == 0) return 0.0;
    return (completedTasks / totalTasks) * 100;
  }

  /// نسبة المهام المتأخرة
  double get overdueRate {
    if (totalTasks == 0) return 0.0;
    return (overdueTasks / totalTasks) * 100;
  }

  /// نسبة الموظفين النشطين
  double get activeEmployeeRate {
    if (totalEmployees == 0) return 0.0;
    return (activeEmployees / totalEmployees) * 100;
  }
}
