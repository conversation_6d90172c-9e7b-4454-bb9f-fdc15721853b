import 'task_models.dart';

/// نموذج المهمة الفرعية - متطابق مع ASP.NET Core API
class Subtask {
  final int id;
  final int taskId;
  final String title;
  final bool isCompleted;
  final int createdAt;
  final int? completedAt;

  // Navigation properties
  final Task? task;

  const Subtask({
    required this.id,
    required this.taskId,
    required this.title,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
    this.task,
  });

  factory Subtask.fromJson(Map<String, dynamic> json) {
    return Subtask(
      id: (json['id'] ?? json['Id']) as int,
      taskId: (json['taskId'] ?? json['TaskId']) as int,
      title: (json['title'] ?? json['Title']) as String,
      isCompleted: (json['isCompleted'] ?? json['IsCompleted']) as bool? ?? false,
      createdAt: (json['createdAt'] ?? json['CreatedAt']) as int,
      completedAt: (json['completedAt'] ?? json['CompletedAt']) as int?,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'title': title,
      'isCompleted': isCompleted,
      'createdAt': createdAt,
      'completedAt': completedAt,
    };
  }

  Subtask copyWith({
    int? id,
    int? taskId,
    String? title,
    bool? isCompleted,
    int? createdAt,
    int? completedAt,
    Task? task,
  }) {
    return Subtask(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      task: task ?? this.task,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ الإكمال كـ DateTime
  DateTime? get completedAtDateTime => completedAt != null
      ? DateTime.fromMillisecondsSinceEpoch(completedAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Subtask(id: $id, title: $title, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subtask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب إنشاء مهمة فرعية
class CreateSubtaskRequest {
  final int taskId;
  final String title;

  const CreateSubtaskRequest({
    required this.taskId,
    required this.title,
  });

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'title': title,
    };
  }
}

/// نموذج طلب تحديث مهمة فرعية
class UpdateSubtaskRequest {
  final String? title;
  final bool? isCompleted;

  const UpdateSubtaskRequest({
    this.title,
    this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (isCompleted != null) json['isCompleted'] = isCompleted;
    return json;
  }
}

/// نموذج طلب تبديل حالة إكمال المهمة الفرعية
class ToggleSubtaskCompletionRequest {
  final bool isCompleted;

  const ToggleSubtaskCompletionRequest({
    required this.isCompleted,
  });

  Map<String, dynamic> toJson() {
    return {
      'isCompleted': isCompleted,
    };
  }
}

/// نموذج استجابة قائمة المهام الفرعية
class SubtasksResponse {
  final List<Subtask> subtasks;
  final int totalCount;
  final int completedCount;
  final int pendingCount;

  const SubtasksResponse({
    required this.subtasks,
    required this.totalCount,
    required this.completedCount,
    required this.pendingCount,
  });

  factory SubtasksResponse.fromJson(Map<String, dynamic> json) {
    return SubtasksResponse(
      subtasks: (json['subtasks'] as List)
          .map((s) => Subtask.fromJson(s as Map<String, dynamic>))
          .toList(),
      totalCount: json['totalCount'] as int,
      completedCount: json['completedCount'] as int,
      pendingCount: json['pendingCount'] as int,
    );
  }

  /// نسبة الإكمال
  double get completionPercentage => 
      totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;

  /// التحقق من اكتمال جميع المهام الفرعية
  bool get allCompleted => totalCount > 0 && completedCount == totalCount;

  /// التحقق من عدم وجود مهام فرعية مكتملة
  bool get noneCompleted => completedCount == 0;
}

/// نموذج إحصائيات المهام الفرعية
class SubtaskStats {
  final int taskId;
  final int totalSubtasks;
  final int completedSubtasks;
  final int pendingSubtasks;
  final double completionPercentage;
  final DateTime? lastUpdated;

  const SubtaskStats({
    required this.taskId,
    required this.totalSubtasks,
    required this.completedSubtasks,
    required this.pendingSubtasks,
    required this.completionPercentage,
    this.lastUpdated,
  });

  factory SubtaskStats.fromJson(Map<String, dynamic> json) {
    return SubtaskStats(
      taskId: json['taskId'] as int,
      totalSubtasks: json['totalSubtasks'] as int,
      completedSubtasks: json['completedSubtasks'] as int,
      pendingSubtasks: json['pendingSubtasks'] as int,
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastUpdated'] as int) * 1000)
          : null,
    );
  }

  /// التحقق من اكتمال جميع المهام الفرعية
  bool get isFullyCompleted => totalSubtasks > 0 && completedSubtasks == totalSubtasks;

  /// التحقق من عدم البدء في أي مهمة فرعية
  bool get isNotStarted => completedSubtasks == 0;

  /// التحقق من وجود تقدم جزئي
  bool get isInProgress => completedSubtasks > 0 && completedSubtasks < totalSubtasks;
}
