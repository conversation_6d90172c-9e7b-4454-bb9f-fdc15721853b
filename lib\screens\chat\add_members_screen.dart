import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/chat_group_models.dart';
import '../../models/user_model.dart';
import '../../models/group_member_models.dart';
import '../../services/api/users_api_service.dart';
import '../../services/api/group_members_api_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/app_colors.dart';

/// شاشة إضافة أعضاء للمجموعة
class AddMembersScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const AddMembersScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<AddMembersScreen> createState() => _AddMembersScreenState();
}

class _AddMembersScreenState extends State<AddMembersScreen> {
  final TextEditingController _searchController = TextEditingController();
  final UsersApiService _usersApiService = UsersApiService();
  final GroupMembersApiService _groupMembersApiService = GroupMembersApiService();
  final AuthController _authController = Get.find<AuthController>();

  final RxList<User> _availableUsers = <User>[].obs;
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxList<GroupMember> _currentMembers = <GroupMember>[].obs;
  final RxList<int> _selectedUserIds = <int>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isAddingMembers = false.obs;
  final RxString _searchQuery = ''.obs;

  @override
  void initState() {
    super.initState();
    _loadData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل البيانات المطلوبة
  Future<void> _loadData() async {
    _isLoading.value = true;
    try {
      // تحميل الأعضاء الحاليين
      await _loadCurrentMembers();

      // تحميل جميع المستخدمين
      await _loadAvailableUsers();
    } catch (e) {
      _showErrorSnackbar('خطأ في تحميل البيانات: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل الأعضاء الحاليين في المجموعة
  Future<void> _loadCurrentMembers() async {
    try {
      final members = await _groupMembersApiService.getMembersByGroup(widget.chatGroup.id);
      _currentMembers.value = members;
    } catch (e) {
      debugPrint('خطأ في تحميل الأعضاء الحاليين: $e');
    }
  }

  /// تحميل المستخدمين المتاحين للإضافة
  Future<void> _loadAvailableUsers() async {
    try {
      final allUsers = await _usersApiService.getAllUsers();

      // استبعاد الأعضاء الحاليين والمستخدم الحالي
      final currentMemberIds = _currentMembers.map((m) => m.userId).toSet();
      final currentUserId = _authController.currentUser.value?.id;

      final availableUsers = allUsers.where((user) =>
        !currentMemberIds.contains(user.id) &&
        user.id != currentUserId &&
        user.isActive &&
        !user.isDeleted
      ).toList();

      _availableUsers.value = availableUsers;
      _filteredUsers.value = availableUsers;
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين المتاحين: $e');
    }
  }

  /// معالجة تغيير نص البحث
  void _onSearchChanged() {
    _searchQuery.value = _searchController.text;
    _filterUsers();
  }

  /// تصفية المستخدمين حسب البحث
  void _filterUsers() {
    if (_searchQuery.value.isEmpty) {
      _filteredUsers.value = _availableUsers;
    } else {
      final query = _searchQuery.value.toLowerCase();
      _filteredUsers.value = _availableUsers.where((user) =>
        user.name.toLowerCase().contains(query) ||
        user.email.toLowerCase().contains(query) ||
        (user.username?.toLowerCase().contains(query) ?? false)
      ).toList();
    }
  }

  /// تبديل تحديد المستخدم
  void _toggleUserSelection(int userId) {
    if (_selectedUserIds.contains(userId)) {
      _selectedUserIds.remove(userId);
    } else {
      _selectedUserIds.add(userId);
    }
  }

  /// إضافة الأعضاء المحددين
  Future<void> _addSelectedMembers() async {
    if (_selectedUserIds.isEmpty) {
      _showWarningSnackbar('يرجى تحديد عضو واحد على الأقل');
      return;
    }

    _isAddingMembers.value = true;
    try {
      int successCount = 0;
      int failCount = 0;

      for (final userId in _selectedUserIds) {
        try {
          await _groupMembersApiService.addMemberToGroup(
            widget.chatGroup.id,
            userId,
            'member', // الدور الافتراضي
          );
          successCount++;
        } catch (e) {
          failCount++;
          debugPrint('خطأ في إضافة المستخدم $userId: $e');
        }
      }

      // عرض نتيجة العملية
      if (successCount > 0) {
        _showSuccessSnackbar('تم إضافة $successCount عضو بنجاح');

        // إعادة تحميل البيانات
        await _loadData();
        _selectedUserIds.clear();

        // العودة إذا تم إضافة جميع الأعضاء بنجاح
        if (failCount == 0) {
          Get.back(result: true);
        }
      }

      if (failCount > 0) {
        _showErrorSnackbar('فشل في إضافة $failCount عضو');
      }
    } catch (e) {
      _showErrorSnackbar('خطأ في إضافة الأعضاء: $e');
    } finally {
      _isAddingMembers.value = false;
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      icon: const Icon(Icons.check_circle, color: Colors.green),
    );
  }

  /// عرض رسالة تحذير
  void _showWarningSnackbar(String message) {
    Get.snackbar(
      'تنبيه',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.shade100,
      colorText: Colors.orange.shade800,
      icon: const Icon(Icons.warning, color: Colors.orange),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String message) {
    Get.snackbar(
      'خطأ',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      icon: const Icon(Icons.error, color: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إضافة أعضاء - ${widget.chatGroup.name}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          Obx(() => _selectedUserIds.isNotEmpty
              ? TextButton.icon(
                  onPressed: _isAddingMembers.value ? null : _addSelectedMembers,
                  icon: _isAddingMembers.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.add, color: Colors.white),
                  label: Text(
                    'إضافة (${_selectedUserIds.length})',
                    style: const TextStyle(color: Colors.white),
                  ),
                )
              : const SizedBox.shrink()),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث عن مستخدمين...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Obx(() => _searchQuery.value.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          _searchQuery.value = '';
                          _filterUsers();
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : const SizedBox.shrink()),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // قائمة المستخدمين
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل المستخدمين...'),
                    ],
                  ),
                );
              }

              if (_filteredUsers.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.person_search,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _searchQuery.value.isNotEmpty
                            ? 'لا توجد نتائج للبحث'
                            : 'لا توجد مستخدمين متاحين للإضافة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      if (_searchQuery.value.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'جرب البحث بكلمات مختلفة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: _filteredUsers.length,
                itemBuilder: (context, index) {
                  final user = _filteredUsers[index];
                  final isSelected = _selectedUserIds.contains(user.id);

                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isSelected
                            ? AppColors.primary
                            : Colors.grey.shade300,
                        backgroundImage: user.profileImage != null
                            ? NetworkImage(user.profileImage!)
                            : null,
                        child: user.profileImage == null
                            ? Text(
                                user.name.substring(0, 1).toUpperCase(),
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.grey.shade700,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      title: Text(
                        user.name,
                        style: TextStyle(
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(user.email),
                          if (user.username != null)
                            Text(
                              '@${user.username}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                        ],
                      ),
                      trailing: Checkbox(
                        value: isSelected,
                        onChanged: (_) => _toggleUserSelection(user.id),
                        activeColor: AppColors.primary,
                      ),
                      onTap: () => _toggleUserSelection(user.id),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
