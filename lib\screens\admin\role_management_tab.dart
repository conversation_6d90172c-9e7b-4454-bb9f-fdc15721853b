import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/custom_role_model.dart' as models;
import '../../models/custom_role_model.dart' show CustomRoleRepository;
import 'role_card_widget.dart';
import 'role_details.dart';
import '../../models/custom_role_model.dart';

/// تعداد أنواع الصلاحيات
enum PermissionType {
  view,
  create,
  edit,
  delete,
  approve,
  export,
  import,
  admin,
  assign,
  transfer,
  comment,
  attach,
  viewAll,
  viewOwn,
  interfaceAccess,
  download,
  upload,
  share,
  print,
  ganttChart,
  powerBI,
  calendar,
}

/// تعداد مجالات الصلاحيات
enum PermissionScope {
  tasks,
  users,
  departments,
  messages,
  reports,
  settings,
  backups,
  notifications,
  logs,
  dashboard,
  analytics,
  interfaces,
  attachments,
  files,
  ganttChart,
  powerBI,
  calendar,
  database,
  roles,
  archive,
}

/// نموذج الصلاحية للأدوار المخصصة
class Permission {
  final String id;
  final String customRoleId;
  final PermissionType type;
  final PermissionScope scope;
  final bool isGranted;
  final DateTime createdAt;
  final String? description;

  const Permission({
    required this.id,
    required this.customRoleId,
    required this.type,
    required this.scope,
    required this.isGranted,
    required this.createdAt,
    this.description,
  });
}

/// مستودع صلاحيات الأدوار المخصصة (مؤقت للاختبار)
class CustomPermissionRepository {
  Future<List<Permission>> getCustomRolePermissions(String roleId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return []; // إرجاع قائمة فارغة مؤقتاً
  }

  Future<void> grantCustomRolePermission(
    String roleId,
    PermissionType type,
    PermissionScope scope, {
    String? description,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
    debugPrint('تم منح صلاحية للدور $roleId: $type - $scope');
  }

  Future<void> revokeCustomRolePermission(
    String roleId,
    PermissionType type,
    PermissionScope scope, {
    String? description,
  }) async {
    await Future.delayed(const Duration(milliseconds: 200));
    debugPrint('تم إلغاء صلاحية للدور $roleId: $type - $scope');
  }
}

/// تبويب إدارة الأدوار
///
/// يتيح للمدير إنشاء وتعديل وحذف الأدوار المخصصة
class RoleManagementTab extends StatefulWidget {
  const RoleManagementTab({super.key});

  @override
  State<RoleManagementTab> createState() => _RoleManagementTabState();
}

class _RoleManagementTabState extends State<RoleManagementTab>
    with SingleTickerProviderStateMixin {
  final CustomPermissionRepository _permissionRepository =
      CustomPermissionRepository();

  late TabController _tabController;
  List<models.CustomRole> _roles = [];
  models.CustomRole? _selectedRole;
  bool _isLoading = true;
  String? _errorMessage;

  // خرائط لتخزين حالة الصلاحيات
  final Map<String, bool> _permissionStatus = <String, bool>{};
  final Map<String, bool> _interfacePermissions = <String, bool>{};

  // قائمة الواجهات مع أسمائها العربية
  final Map<String, String> _interfaceNames = {
    'tasks': 'المهام',
    'dashboard': 'لوحة المعلومات',
    'messages': 'الرسائل',
    'notifications': 'الإشعارات',
    'departments': 'الأقسام',
    'users': 'المستخدمين',
    'reports': 'التقارير',
    'settings': 'الإعدادات',
    'admin': 'الإدارة',
    'calendar': 'التقويم',
    'power_bi': 'تقارير Power BI',
    'database': 'إدارة قاعدة البيانات',
    'user-dashboard': 'لوحة تحكم المستخدم',
    'gantt_chart': 'مخطط جانت',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRoles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الأدوار
  Future<void> _loadRoles() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final roles = await CustomRoleRepository().getAllRoles();
      setState(() {
        _roles = roles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل الأدوار: $e';
        _isLoading = false;
      });
    }
  }

  /// تحميل صلاحيات الدور
  Future<void> _loadRolePermissions(String roleId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل صلاحيات الدور
      final permissions =
          await _permissionRepository.getCustomRolePermissions(roleId);

      // إعادة تعيين الخرائط
      _permissionStatus.clear();
      _interfacePermissions.clear();

      // تهيئة حالة الصلاحيات
      for (var type in PermissionType.values) {
        for (var scope in PermissionScope.values) {
          final key = '${type.index}_${scope.index}';
          final permission = permissions.firstWhere(
            (p) =>
                p.type == type && p.scope == scope && p.customRoleId == roleId,
            orElse: () => Permission(
              id: '',
              customRoleId: roleId,
              type: type,
              scope: scope,
              isGranted: false,
              createdAt: DateTime.now(),
            ),
          );

          _permissionStatus[key] = permission.isGranted;

          // إذا كانت صلاحية واجهة، نخزنها في قائمة منفصلة
          if (scope == PermissionScope.interfaces &&
              type == PermissionType.view) {
            // استخدام الوصف كمفتاح للواجهة
            if (permission.description != null) {
              _interfacePermissions[permission.description!] =
                  permission.isGranted;
            }
          }
        }
      }

      // تهيئة صلاحيات الواجهات غير الموجودة
      for (final interface in _interfaceNames.keys) {
        if (!_interfacePermissions.containsKey(interface)) {
          _interfacePermissions[interface] = false;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل صلاحيات الدور: $e';
        _isLoading = false;
      });
    }
  }

  /// حفظ صلاحيات الدور
  // تم حذف دالة _saveRolePermissions لأنها غير مستخدمة

  /// إنشاء دور جديد
  Future<void> _createRole() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('إنشاء دور جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الدور',
                hintText: 'أدخل اسم الدور',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الدور',
                hintText: 'أدخل وصف الدور',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يجب إدخال اسم الدور',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final role = models.CustomRole.create(
          name: nameController.text,
          description: descriptionController.text,
        );

        await CustomRoleRepository().createRole(role);
        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم إنشاء الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء إنشاء الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تعديل دور
  Future<void> _editRole(models.CustomRole role) async {
    final TextEditingController nameController =
        TextEditingController(text: role.name);
    final TextEditingController descriptionController =
        TextEditingController(text: role.description);

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تعديل الدور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الدور',
                hintText: 'أدخل اسم الدور',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الدور',
                hintText: 'أدخل وصف الدور',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يجب إدخال اسم الدور',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final updatedRole = role.copyWith(
          name: nameController.text,
          description: descriptionController.text,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        await CustomRoleRepository().updateRole(updatedRole);
        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم تعديل الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تعديل الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// حذف دور
  Future<void> _deleteRole(models.CustomRole role) async {
    if (role.isSystem) {
      Get.snackbar(
        'خطأ',
        'لا يمكن حذف أدوار النظام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('حذف الدور'),
        content: Text('هل أنت متأكد من حذف الدور "${role.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await CustomRoleRepository().deleteRole(role.id);

        // إذا كان الدور المحذوف هو المحدد حاليًا، نقوم بإلغاء تحديده
        if (_selectedRole?.id == role.id) {
          setState(() {
            _selectedRole = null;
          });
        }

        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم حذف الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // قائمة الأدوار
        SizedBox(
          width: 300,
          child: Card(
            margin: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الأدوار',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        tooltip: 'إنشاء دور جديد',
                        onPressed: _createRole,
                      ),
                    ],
                  ),
                ),
                const Divider(),
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _errorMessage != null
                          ? Center(
                              child: Text(_errorMessage!,
                                  style: const TextStyle(color: Colors.red)))
                          : _roles.isEmpty
                              ? const Center(child: Text('لا توجد أدوار'))
                              : ListView.builder(
                                  itemCount: _roles.length,
                                  itemBuilder: (context, index) {
                                    final role = _roles[index];
                                    final isSelected =
                                        _selectedRole?.id == role.id;
                                    return RoleCardWidget(
                                      role: role,
                                      isSelected: isSelected,
                                      onTap: () {
                                        setState(() {
                                          _selectedRole = role;
                                        });
                                        _loadRolePermissions(role.id.toString());
                                      },
                                      onEdit: () => _editRole(role),
                                      onDelete: () => _deleteRole(role),
                                    );
                                  },
                                ),
                ),
              ],
            ),
          ),
        ),

        // تفاصيل الدور وصلاحياته
        Expanded(
          child: _selectedRole == null
              ? const Center(
                  child: Text('اختر دور لإدارة صلاحياته'),
                )
              : Card(
                  margin: const EdgeInsets.all(8),
                  child: RoleDetails(role: _selectedRole!), // تأكد أن RoleDetails يستقبل النوع الصحيح
                ),
        ),
      ],
    );
  }
    }