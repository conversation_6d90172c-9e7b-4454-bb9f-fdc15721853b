import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:flutter_application_2/models/dashboard_model.dart'
    as simple_model;

import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../models/task_status_enum.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/chart_utils.dart';

/// نافذة تفاصيل المخطط
///
/// تعرض المخطط بحجم أكبر وتتيح تخصيص إعداداته
class ChartDetailDialog extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final simple_model.SimpleDashboardWidget widget;

  /// إعدادات المخطط
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(Map<String, dynamic> settings)? onSettingsUpdated;

  const ChartDetailDialog({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
  });

  @override
  State<ChartDetailDialog> createState() => _ChartDetailDialogState();
}

class _ChartDetailDialogState extends State<ChartDetailDialog>
    with SingleTickerProviderStateMixin {
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController =
      Get.find<DepartmentController>();

  late TabController _tabController;
  late Map<String, dynamic> _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _settings = Map<String, dynamic>.from(widget.settings);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  void _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // تحميل البيانات حسب نوع المخطط
    try {
      await _taskController.loadAllTasks();
      await _userController.loadAllUsers();
      // await _departmentController.loadDepartments(); // تم تعطيله مؤقتاً
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// تحديث الإعدادات
  void _updateSettings() {
    if (widget.onSettingsUpdated != null) {
      widget.onSettingsUpdated!(_settings);
    }

    Get.back();

    Get.snackbar(
      'تم التحديث',
      'تم تحديث إعدادات المخطط بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  /// تصدير المخطط
  void _exportChart(String format) async {
    try {
      // إظهار مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // الحصول على بيانات المخطط
      // إغلاق مؤشر التحميل
      Get.back();

      // إظهار رسالة نجاح مؤقتة
      Get.snackbar(
        'تصدير المخطط',
        'سيتم إضافة ميزة التصدير قريباً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير المخطط: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// الحصول على بيانات المخطط
  Future<Map<String, dynamic>> _getChartData() async {
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return await _getTaskStatusChartData();
      case 'taskProgressChart':
        return await _getTaskProgressChartData();
      case 'userPerformanceChart':
        return await _getUserPerformanceChartData();
      case 'departmentPerformanceChart':
        return await _getDepartmentPerformanceChartData();
      default:
        return {};
    }
  }

  /// الحصول على بيانات مخطط حالة المهام
  Future<Map<String, dynamic>> _getTaskStatusChartData() async {
    // تجميع المهام حسب الحالة
    final tasks = _taskController.allTasks;

    // تصفية المهام حسب التاريخ إذا كان محددًا في الإعدادات
    final startDate = _settings['startDate'] as DateTime?;
    final endDate = _settings['endDate'] as DateTime?;

    // تصفية المهام حسب التاريخ
    final filteredTasks = tasks.where((task) {
      if (startDate == null || endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(startDate) && taskDate.isBefore(endDate);
    }).toList();

    final Map<String, int> statusCounts = {};

    for (final task in filteredTasks) {
      final statusId = task.status;
      final status = TaskStatus.fromString(statusId);
      final statusName = status.displayNameAr;
      statusCounts[statusName] = (statusCounts[statusName] ?? 0) + 1;
    }

    // تحويل البيانات إلى الصيغة المطلوبة
    final Map<String, double> data = {};
    statusCounts.forEach((key, value) {
      data[key] = value.toDouble();
    });

    return data;
  }



//  SEARCH
  /// الحصول على بيانات مخطط تقدم المهام
  Future<Map<String, dynamic>> _getTaskProgressChartData() async {
    // تجميع المهام حسب تقدم الإنجاز
    final tasks = _taskController.allTasks;

    // تصفية المهام حسب التاريخ إذا كان محددًا في الإعدادات
    final startDate = _settings['startDate'] as DateTime?;
    final endDate = _settings['endDate'] as DateTime?;

    // تصفية المهام حسب التاريخ
    final filteredTasks = tasks.where((task) {
      if (startDate == null || endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(startDate) && taskDate.isBefore(endDate);
    }).toList();

    final Map<String, int> progressCounts = {
      'Not Started': 0,
      'In Progress': 0,
      'Completed': 0,
    };

    for (final task in filteredTasks) {
      if (task.completionPercentage == 0) {
        progressCounts['Not Started'] = (progressCounts['Not Started'] ?? 0) + 1;
      } else if (task.completionPercentage > 0 && task.completionPercentage < 100) {
        progressCounts['In Progress'] = (progressCounts['In Progress'] ?? 0) + 1;
      } else if (task.completionPercentage == 100) {
        progressCounts['Completed'] = (progressCounts['Completed'] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى الصيغة المطلوبة
    final Map<String, double> data = {};
    progressCounts.forEach((key, value) {
      data[key] = value.toDouble();
    });

    return data;
  }

  /// الحصول على بيانات مخطط أداء المستخدمين
  Future<Map<String, dynamic>> _getUserPerformanceChartData() async {
    // تجميع المهام حسب المستخدم
    final tasks = _taskController.allTasks;
    final users = _userController.users;

    // تصفية المهام حسب التاريخ إذا كان محددًا في الإعدادات
    final startDate = _settings['startDate'] as DateTime?;
    final endDate = _settings['endDate'] as DateTime?;

    // تصفية المهام حسب التاريخ
    final filteredTasks = tasks.where((task) {
      if (startDate == null || endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(startDate) && taskDate.isBefore(endDate);
    }).toList();

    final Map<String, Map<String, int>> tasksByUser = {};

    for (final user in users) {
      tasksByUser[user.name] = {
        'total': 0,
        'completed': 0,
      };
    }

    for (final task in filteredTasks) {
      final assigneeId = task.assigneeId;
      if (assigneeId != null) {
        final user = users.firstWhereOrNull((u) => u.id == assigneeId);
        if (user != null) {
          tasksByUser[user.name]!['total'] =
              (tasksByUser[user.name]!['total'] ?? 0) + 1;

          if (task.status == TaskStatus.completed.stringValue) {
            tasksByUser[user.name]!['completed'] =
                (tasksByUser[user.name]!['completed'] ?? 0) + 1;
          }
        }
      }
    }

    // تحويل البيانات إلى الصيغة المطلوبة
    final Map<String, double> completionRates = {};
    tasksByUser.forEach((userName, counts) {
      if (counts['total']! > 0) {
        completionRates[userName] =
            (counts['completed']! / counts['total']!) * 100;
      } else {
        completionRates[userName] = 0;
      }
    });

    return completionRates;
  }

  /// الحصول على بيانات مخطط أداء الأقسام
  Future<Map<String, dynamic>> _getDepartmentPerformanceChartData() async {
    // تجميع المهام حسب القسم
    final tasks = _taskController.allTasks;
    final departments = _departmentController.allDepartments;

    // تصفية المهام حسب التاريخ إذا كان محددًا في الإعدادات
    final startDate = _settings['startDate'] as DateTime?;
    final endDate = _settings['endDate'] as DateTime?;

    // تصفية المهام حسب التاريخ
    final filteredTasks = tasks.where((task) {
      if (startDate == null || endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(startDate) && taskDate.isBefore(endDate);
    }).toList();

    final Map<String, Map<String, int>> tasksByDepartment = {};

    for (final department in departments) {
      tasksByDepartment[department.name] = {
        'total': 0,
        'completed': 0,
      };
    }

    for (final task in filteredTasks) {
      final departmentId = task.departmentId;
      final department =
          departments.firstWhereOrNull((d) => d.id == departmentId);
      if (department != null) {
        tasksByDepartment[department.name]!['total'] =
            (tasksByDepartment[department.name]!['total'] ?? 0) + 1;

        if (task.status == TaskStatus.completed.stringValue) {
          tasksByDepartment[department.name]!['completed'] =
              (tasksByDepartment[department.name]!['completed'] ?? 0) + 1;
        }
      }
    }

    // تحويل البيانات إلى الصيغة المطلوبة
    final Map<String, double> completionRates = {};
    tasksByDepartment.forEach((departmentName, counts) {
      if (counts['total']! > 0) {
        completionRates[departmentName] =
            (counts['completed']! / counts['total']!) * 100;
      } else {
        completionRates[departmentName] = 0;
      }
    });

    return completionRates;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان النافذة
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[800]
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.widget.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ),
                  // زر التصدير
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.download),
                    tooltip: 'تصدير المخطط',
                    onSelected: (value) {
                      _exportChart(value);
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'pdf',
                        child: Row(
                          children: [
                            Icon(Icons.picture_as_pdf, color: Colors.red),
                            SizedBox(width: 8),
                            Text('تصدير كـ PDF'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'excel',
                        child: Row(
                          children: [
                            Icon(Icons.table_chart, color: Colors.green),
                            SizedBox(width: 8),
                            Text('تصدير كـ Excel'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'csv',
                        child: Row(
                          children: [
                            Icon(Icons.description, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('تصدير كـ CSV'),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // زر الإغلاق
                  IconButton(
                    icon: const Icon(Icons.close),
                    tooltip: 'إغلاق',
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // علامات التبويب
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[850]
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'المخطط'),
                  Tab(text: 'الإعدادات'),
                ],
                indicator: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[700]
                      : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                labelColor: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
                unselectedLabelColor:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[400]
                        : Colors.grey[700],
              ),
            ),

            const SizedBox(height: 16),

            // محتوى علامات التبويب
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // علامة تبويب المخطط
                  _buildChartTab(),

                  // علامة تبويب الإعدادات
                  _buildSettingsTab(),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _updateSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.blue[700]
                            : Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                  child: const Text('حفظ التغييرات'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء علامة تبويب المخطط
  Widget _buildChartTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return FutureBuilder<Map<String, dynamic>>(
      future: _getChartData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('خطأ: ${snapshot.error}'),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text('لا توجد بيانات'),
          );
        }

        // عرض المخطط المناسب حسب النوع مع مؤشرات تقدم محسنة وألوان محسنة
        switch (widget.widget.type) {
          case 'taskStatusChart':
            return Column(
              children: [
                LinearProgressIndicator(
                  value: snapshot.data!.values.fold(0.0, (a, b) => a + b) /
                      (snapshot.data!.values.length * 100),
                  minHeight: 8,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                const SizedBox(height: 12),
                Expanded(child: _buildPieChart(snapshot.data!)),
              ],
            );
          case 'taskProgressChart':
            return Column(
              children: [
                LinearProgressIndicator(
                  value: snapshot.data!.values.fold(0.0, (a, b) => a + b) /
                      (snapshot.data!.values.length * 100),
                  minHeight: 8,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
                ),
                const SizedBox(height: 12),
                Expanded(child: _buildLineChart(snapshot.data!)),
              ],
            );
          case 'userPerformanceChart':
          case 'departmentPerformanceChart':
            return Column(
              children: [
                LinearProgressIndicator(
                  value: snapshot.data!.values.fold(0.0, (a, b) => a + b) /
                      (snapshot.data!.values.length * 100),
                  minHeight: 8,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
                ),
                const SizedBox(height: 12),
                Expanded(child: _buildBarChart(snapshot.data!)),
              ],
            );
          default:
            return const Center(
              child: Text('نوع مخطط غير مدعوم'),
            );
        }
      },
    );
  }

  /// بناء علامة تبويب الإعدادات
  Widget _buildSettingsTab() {
    // عرض الإعدادات المناسبة حسب نوع المخطط
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return _buildPieChartSettings();
      case 'taskProgressChart':
        return _buildLineChartSettings();
      case 'userPerformanceChart':
      case 'departmentPerformanceChart':
        return _buildBarChartSettings();
      default:
        return const Center(
          child: Text('نوع مخطط غير مدعوم'),
        );
    }
  }

  /// بناء إعدادات المخطط الدائري
  Widget _buildPieChartSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // نوع المخطط
        const Text(
          'نوع المخطط',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // خيارات أنواع المخططات مع معاينة
        _buildChartTypeSelector(),

        const SizedBox(height: 24),

        // خيارات العرض
        const Text(
          'خيارات العرض',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // إظهار المفتاح
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار المفتاح'),
            subtitle: const Text('عرض مفتاح الألوان بجانب المخطط'),
            value: _settings['showLegend'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showLegend'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // إظهار القيم
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار القيم'),
            subtitle: const Text('عرض القيم العددية على المخطط'),
            value: _settings['showValues'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showValues'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // إظهار النسب المئوية
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار النسب المئوية'),
            subtitle: const Text('عرض النسب المئوية على المخطط'),
            value: _settings['showPercentages'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showPercentages'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // حجم الفراغ المركزي (للمخطط الحلقي)
        if (_settings['chartType'] == 'donut') ...[
          const SizedBox(height: 8),
          const Text('حجم الفراغ المركزي'),
          Slider(
            value: (_settings['centerSpaceRadius'] ?? 40).toDouble(),
            min: 0,
            max: 80,
            divisions: 8,
            label: '${_settings['centerSpaceRadius'] ?? 40}',
            onChanged: (value) {
              setState(() {
                _settings['centerSpaceRadius'] = value.toInt();
              });
            },
          ),
        ],

        const SizedBox(height: 24),

        // خيارات الألوان
        const Text(
          'خيارات الألوان',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // نمط الألوان
        DropdownButtonFormField<String>(
          value: _settings['colorScheme'] ?? 'default',
          decoration: const InputDecoration(
            labelText: 'نمط الألوان',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: [
            DropdownMenuItem(
              value: 'default',
              child: Text('الألوان الافتراضية'),
            ),
            DropdownMenuItem(
              value: 'pastel',
              child: Text('ألوان هادئة'),
            ),
            DropdownMenuItem(
              value: 'bright',
              child: Text('ألوان زاهية'),
            ),
            DropdownMenuItem(
              value: 'dark',
              child: Text('ألوان داكنة'),
            ),
            DropdownMenuItem(
              value: 'monochrome_blue',
              child: Text('تدرجات زرقاء'),
            ),
            DropdownMenuItem(
              value: 'monochrome_green',
              child: Text('تدرجات خضراء'),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _settings['colorScheme'] = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // حجم المخطط
        const Text('حجم المخطط'),
        Row(
          children: [
            const Text('صغير'),
            Expanded(
              child: Slider(
                value: (_settings['chartSize'] ?? 200).toDouble(),
                min: 150,
                max: 300,
                divisions: 5,
                label: '${_settings['chartSize'] ?? 200}',
                onChanged: (value) {
                  setState(() {
                    _settings['chartSize'] = value.toInt();
                  });
                },
              ),
            ),
            const Text('كبير'),
          ],
        ),
      ],
    );
  }

  /// بناء محدد نوع المخطط مع معاينة
  Widget _buildChartTypeSelector() {
    // تحويل نوع المخطط الحالي من سلسلة نصية إلى ChartType
    final String currentTypeStr = _settings['chartType'] ?? 'pie';
    final ChartType currentType = _getChartTypeFromString(currentTypeStr);

    // تحديد أنواع المخططات المتاحة
    final List<ChartType> availableTypes = [
      ChartType.pie,
      ChartType.donut,
    ];

    // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: availableTypes.map((type) {
          final isSelected = currentType == type;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: ChoiceChip(
              label: Icon(
                ChartTypeUtils.getChartTypeIcon(type),
                size: 24,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    // تحويل ChartType إلى سلسلة نصية للتخزين في الإعدادات
                    _settings['chartType'] = _getStringFromChartType(type);
                  });
                }
              },
              backgroundColor: Colors.grey.shade200,
              selectedColor: AppColors.primary,
              tooltip: ChartTypeUtils.getChartTypeLabel(type),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// تحويل نوع المخطط من سلسلة نصية إلى ChartType
  ChartType _getChartTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'pie':
        return ChartType.pie;
      case 'donut':
        return ChartType.donut;
      case 'bar':
        return ChartType.bar;
      case 'line':
        return ChartType.line;
      case 'area':
        return ChartType.area;
      default:
        return ChartType.pie;
    }
  }

  /// تحويل نوع المخطط من ChartType إلى سلسلة نصية
  String _getStringFromChartType(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'pie';
      case ChartType.donut:
        return 'donut';
      case ChartType.bar:
        return 'bar';
      case ChartType.line:
        return 'line';
      case ChartType.area:
        return 'area';
      default:
        return 'pie';
    }
  }

  /// بناء إعدادات المخطط الخطي
  Widget _buildLineChartSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // نوع المخطط
        const Text(
          'نوع المخطط',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // خيارات أنواع المخططات مع معاينة
        _buildLineChartTypeSelector(),

        const SizedBox(height: 24),

        // خيارات العرض
        const Text(
          'خيارات العرض',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // إظهار الشبكة
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار الشبكة'),
            subtitle: const Text('عرض خطوط الشبكة في الخلفية'),
            value: _settings['showGrid'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showGrid'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // إظهار النقاط
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار النقاط'),
            subtitle: const Text('عرض نقاط البيانات على المخطط'),
            value: _settings['showDots'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showDots'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // إظهار المنطقة تحت الخط
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار المنطقة تحت الخط'),
            subtitle: const Text('تلوين المنطقة تحت خط البيانات'),
            value: _settings['showBelowArea'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showBelowArea'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 24),

        // نطاق الوقت
        const Text(
          'نطاق الوقت',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // خيارات نطاق الوقت
        Row(
          children: [
            _buildTimeRangeOption(
              value: 'week',
              title: 'أسبوع',
              isSelected: _settings['timeRange'] == 'week',
            ),
            _buildTimeRangeOption(
              value: 'month',
              title: 'شهر',
              isSelected: _settings['timeRange'] == 'month',
            ),
            _buildTimeRangeOption(
              value: 'quarter',
              title: 'ربع سنة',
              isSelected: _settings['timeRange'] == 'quarter',
            ),
            _buildTimeRangeOption(
              value: 'year',
              title: 'سنة',
              isSelected: _settings['timeRange'] == 'year',
            ),
          ],
        ),

        const SizedBox(height: 24),

        // خيارات الألوان
        const Text(
          'خيارات الألوان',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // نمط الألوان
        DropdownButtonFormField<String>(
          value: _settings['colorScheme'] ?? 'default',
          decoration: const InputDecoration(
            labelText: 'نمط الألوان',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: [
            DropdownMenuItem(
              value: 'default',
              child: Text('الألوان الافتراضية'),
            ),
            DropdownMenuItem(
              value: 'pastel',
              child: Text('ألوان هادئة'),
            ),
            DropdownMenuItem(
              value: 'bright',
              child: Text('ألوان زاهية'),
            ),
            DropdownMenuItem(
              value: 'monochrome_blue',
              child: Text('تدرجات زرقاء'),
            ),
            DropdownMenuItem(
              value: 'monochrome_green',
              child: Text('تدرجات خضراء'),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _settings['colorScheme'] = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // حجم المخطط
        const Text('حجم المخطط'),
        Row(
          children: [
            const Text('صغير'),
            Expanded(
              child: Slider(
                value: (_settings['chartSize'] ?? 200).toDouble(),
                min: 150,
                max: 300,
                divisions: 5,
                label: '${_settings['chartSize'] ?? 200}',
                onChanged: (value) {
                  setState(() {
                    _settings['chartSize'] = value.toInt();
                  });
                },
              ),
            ),
            const Text('كبير'),
          ],
        ),
      ],
    );
  }

  /// بناء خيار نطاق الوقت
  Widget _buildTimeRangeOption({
    required String value,
    required String title,
    required bool isSelected,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _settings['timeRange'] = value;
          });
        },
        child: Card(
          elevation: isSelected ? 4 : 0,
          color: isSelected
              ? Theme.of(context).brightness == Brightness.dark
                  ? Colors.blue[800]
                  : Colors.blue[50]
              : Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[850]
                  : Colors.grey[100],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Colors.grey.withAlpha(80),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Theme.of(context).primaryColor : null,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء محدد نوع المخطط الخطي
  Widget _buildLineChartTypeSelector() {
    // تحويل نوع المخطط الحالي من سلسلة نصية إلى ChartType
    final String currentTypeStr = _settings['lineChartType'] ?? 'line';
    final ChartType currentType = _getChartTypeFromString(currentTypeStr);

    // تحديد أنواع المخططات المتاحة
    final List<ChartType> availableTypes = [
      ChartType.line,
      ChartType.area,
      ChartType.bar,
    ];

    // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: availableTypes.map((type) {
          final isSelected = currentType == type;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: ChoiceChip(
              label: Icon(
                ChartTypeUtils.getChartTypeIcon(type),
                size: 24,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    // تحويل ChartType إلى سلسلة نصية للتخزين في الإعدادات
                    _settings['lineChartType'] = _getStringFromChartType(type);
                  });
                }
              },
              backgroundColor: Colors.grey.shade200,
              selectedColor: AppColors.primary,
              tooltip: ChartTypeUtils.getChartTypeLabel(type),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// بناء إعدادات المخطط الشريطي
  Widget _buildBarChartSettings() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // نوع المخطط
        const Text(
          'نوع المخطط',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // خيارات أنواع المخططات مع معاينة
        _buildBarChartTypeSelector(),

        const SizedBox(height: 24),

        // خيارات العرض
        const Text(
          'خيارات العرض',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // إظهار الشبكة
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار الشبكة'),
            subtitle: const Text('عرض خطوط الشبكة في الخلفية'),
            value: _settings['showGrid'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showGrid'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // إظهار القيم
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('إظهار القيم'),
            subtitle: const Text('عرض القيم العددية على المخطط'),
            value: _settings['showValues'] ?? true,
            onChanged: (value) {
              setState(() {
                _settings['showValues'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 8),

        // اتجاه الأعمدة
        Card(
          elevation: 0,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[850]
              : Colors.grey[100],
          child: SwitchListTile(
            title: const Text('أعمدة أفقية'),
            subtitle: const Text('عرض الأعمدة بشكل أفقي بدلاً من عمودي'),
            value: _settings['horizontalBars'] ?? false,
            onChanged: (value) {
              setState(() {
                _settings['horizontalBars'] = value;
              });
            },
          ),
        ),

        const SizedBox(height: 24),

        // خيارات الترتيب
        const Text(
          'خيارات الترتيب',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // ترتيب حسب
        DropdownButtonFormField<String>(
          value: _settings['sortBy'] ?? 'completed',
          decoration: const InputDecoration(
            labelText: 'ترتيب حسب',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(
              value: 'completed',
              child: Text('المهام المكتملة'),
            ),
            DropdownMenuItem(
              value: 'total',
              child: Text('إجمالي المهام'),
            ),
            DropdownMenuItem(
              value: 'percentage',
              child: Text('نسبة الإنجاز'),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _settings['sortBy'] = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // اتجاه الترتيب
        Row(
          children: [
            Expanded(
              child: _buildSortDirectionOption(
                value: 'asc',
                title: 'تصاعدي',
                icon: Icons.arrow_upward,
                isSelected: _settings['sortDirection'] == 'asc',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSortDirectionOption(
                value: 'desc',
                title: 'تنازلي',
                icon: Icons.arrow_downward,
                isSelected: _settings['sortDirection'] != 'asc',
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // خيارات الألوان
        const Text(
          'خيارات الألوان',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // نمط الألوان
        DropdownButtonFormField<String>(
          value: _settings['colorScheme'] ?? 'default',
          decoration: const InputDecoration(
            labelText: 'نمط الألوان',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          items: const [
            DropdownMenuItem(
              value: 'default',
              child: Text('الألوان الافتراضية'),
            ),
            DropdownMenuItem(
              value: 'pastel',
              child: Text('ألوان هادئة'),
            ),
            DropdownMenuItem(
              value: 'bright',
              child: Text('ألوان زاهية'),
            ),
            DropdownMenuItem(
              value: 'monochrome_blue',
              child: Text('تدرجات زرقاء'),
            ),
            DropdownMenuItem(
              value: 'monochrome_green',
              child: Text('تدرجات خضراء'),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _settings['colorScheme'] = value;
            });
          },
        ),

        const SizedBox(height: 16),

        // حجم المخطط
        const Text('حجم المخطط'),
        Row(
          children: [
            const Text('صغير'),
            Expanded(
              child: Slider(
                value: (_settings['chartSize'] ?? 200).toDouble(),
                min: 150,
                max: 300,
                divisions: 5,
                label: '${_settings['chartSize'] ?? 200}',
                onChanged: (value) {
                  setState(() {
                    _settings['chartSize'] = value.toInt();
                  });
                },
              ),
            ),
            const Text('كبير'),
          ],
        ),
      ],
    );
  }

  /// بناء خيار اتجاه الترتيب
  Widget _buildSortDirectionOption({
    required String value,
    required String title,
    required IconData icon,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _settings['sortDirection'] = value;
        });
      },
      child: Card(
        elevation: isSelected ? 4 : 0,
        color: isSelected
            ? Theme.of(context).brightness == Brightness.dark
                ? Colors.blue[800]
                : Colors.blue[50]
            : Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[850]
                : Colors.grey[100],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.grey.withAlpha(80),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 24,
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[400]
                        : Colors.grey[700],
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Theme.of(context).primaryColor : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء محدد نوع المخطط الشريطي
  Widget _buildBarChartTypeSelector() {
    // تحويل نوع المخطط الحالي من سلسلة نصية إلى ChartType
    final String currentTypeStr = _settings['barChartType'] ?? 'vertical';

    // تعيين نوع المخطط المناسب
    ChartType currentType;
    switch (currentTypeStr) {
      case 'vertical':
        currentType = ChartType.bar;
        break;
      case 'horizontal':
        currentType =
            ChartType.bar; // يمكن إضافة نوع خاص للأعمدة الأفقية إذا كان متاحاً
        break;
      case 'stacked':
        currentType =
            ChartType.bar; // يمكن إضافة نوع خاص للأعمدة المكدسة إذا كان متاحاً
        break;
      default:
        currentType = ChartType.bar;
    }

    // تحديد أنواع المخططات المتاحة
    final List<ChartType> availableTypes = [
      ChartType.bar,
      // يمكن إضافة أنواع أخرى إذا كانت متاحة
    ];

    return Column(
      children: [
        // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: availableTypes.map((type) {
              final isSelected = currentType == type;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2.0),
                child: ChoiceChip(
                  label: Icon(
                    ChartTypeUtils.getChartTypeIcon(type),
                    size: 24,
                    color: isSelected ? Colors.white : Colors.grey.shade700,
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        // نحتفظ بنفس النوع الفرعي
                        _settings['barChartType'] = currentTypeStr;
                      });
                    }
                  },
                  backgroundColor: Colors.grey.shade200,
                  selectedColor: AppColors.primary,
                  tooltip: ChartTypeUtils.getChartTypeLabel(type),
                ),
              );
            }).toList(),
          ),
        ),

        // إضافة خيارات إضافية للأعمدة (عمودي/أفقي/مكدس)
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildBarOrientationOption('vertical', 'عمودي', Icons.bar_chart),
            _buildBarOrientationOption(
                'horizontal', 'أفقي', Icons.align_horizontal_left),
            _buildBarOrientationOption(
                'stacked', 'مكدس', Icons.stacked_bar_chart),
          ],
        ),
      ],
    );
  }

  /// بناء خيار اتجاه المخطط الشريطي
  Widget _buildBarOrientationOption(String type, String title, IconData icon) {
    final isSelected = _settings['barChartType'] == type;

    return InkWell(
      onTap: () {
        setState(() {
          _settings['barChartType'] = type;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withValues(alpha: 26)
              : Colors.transparent, // 0.1 * 255 = 26
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Colors.blue
                : Colors.grey.withValues(alpha: 77), // 0.3 * 255 = 77
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.blue : Colors.grey,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.blue : Colors.grey,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط دائري
  Widget _buildPieChart(Map<String, dynamic> data) {
    final List<_PieData> chartData =
        data.entries.map((e) => _PieData(e.key, e.value)).toList();
    return SfCircularChart(
      legend: Legend(isVisible: true, position: LegendPosition.bottom),
      series: <PieSeries<_PieData, String>>[
        PieSeries<_PieData, String>(
          dataSource: chartData,
          xValueMapper: (_PieData d, _) => d.label,
          yValueMapper: (_PieData d, _) => d.value,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
        ),
      ],
    );
  }

  /// بناء مخطط خطي
  Widget _buildLineChart(Map<String, dynamic> data) {
    final List<_LineData> total = (data['series']?[0]['data'] as List<Map<String, dynamic>>?)
            ?.map<_LineData>((Map<String, dynamic> e) => _LineData((e['x'] as num).toInt(), (e['y'] as num).toInt()))
            .toList() ??
        [];
    final List<_LineData> completed = (data['series']?[1]['data'] as List<Map<String, dynamic>>?)
            ?.map<_LineData>((Map<String, dynamic> e) => _LineData((e['x'] as num).toInt(), (e['y'] as num).toInt()))
            .toList() ??
        [];
    return SfCartesianChart(
      primaryXAxis: DateTimeAxis(
        intervalType: DateTimeIntervalType.days,
      ),
      legend: Legend(isVisible: true, position: LegendPosition.bottom),
      series: <CartesianSeries<dynamic, dynamic>>[
        LineSeries<_LineData, DateTime>(
          name: 'إجمالي المهام',
          dataSource: total,
          xValueMapper: (_LineData d, _) =>
              DateTime.fromMillisecondsSinceEpoch(d.x),
          yValueMapper: (_LineData d, _) => d.y,
        ),
        LineSeries<_LineData, DateTime>(
          name: 'المهام المكتملة',
          dataSource: completed,
          xValueMapper: (_LineData d, _) =>
              DateTime.fromMillisecondsSinceEpoch(d.x),
          yValueMapper: (_LineData d, _) => d.y,
        ),
      ],
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart(Map<String, dynamic> data) {
    final List<_BarData> chartData =
        data.entries.map((MapEntry<String, dynamic> e) => _BarData(e.key, (e.value as num).toDouble())).toList();
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(),
      legend: Legend(isVisible: false),
      series: <CartesianSeries<dynamic, dynamic>>[
        BarSeries<_BarData, String>(
          dataSource: chartData,
          xValueMapper: (_BarData d, _) => d.label,
          yValueMapper: (_BarData d, _) => d.value,
          dataLabelSettings: const DataLabelSettings(isVisible: true),
        ),
      ],
    );
  }
}

// نماذج بيانات للمخططات
class _PieData {
  final String label;
  final double value;
  _PieData(this.label, this.value);
}

class _LineData {
  final int x;
  final int y;
  _LineData(this.x, this.y);
}

class _BarData {
  final String label;
  final double value;
  _BarData(this.label, this.value);
}
