import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/role_model.dart';
import '../models/permission_models.dart';
import '../models/department_model.dart';
import '../models/system_models.dart';
import '../models/activity_log_models.dart';
import '../models/system_setting_models.dart' as setting_models;
import '../services/api/unified_api_services.dart';
import '../services/unified_permission_service.dart';

/// متحكم الإدارة الموحد والمحسن
/// 
/// يوفر إدارة شاملة وديناميكية لجميع جوانب النظام الإداري
/// - إدارة المستخدمين والأدوار والصلاحيات
/// - إدارة الأقسام والشاشات والإجراءات
/// - إدارة النظام والنسخ الاحتياطية والتقارير
/// - معالجة محسنة للأخطاء وإدارة الحالة
class AdminController extends GetxController {
  static AdminController get instance => Get.find<AdminController>();
  
  // ===== الخدمات =====
  final UsersApiService _usersApiService = UsersApiService();
  final PermissionsApiService _permissionsApiService = PermissionsApiService();
  final ActivityLogsApiService _activityLogsApiService = ActivityLogsApiService();
  final BackupsApiService _backupsApiService = BackupsApiService();
  final SystemSettingsApiService _systemSettingsApiService = SystemSettingsApiService();
  final ApiService _apiService = ApiService();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  // ===== البيانات الأساسية =====
  
  // المستخدمون والأدوار والصلاحيات
  final RxList<User> _users = <User>[].obs;
  final RxList<Role> _roles = <Role>[].obs;
  final RxList<Permission> _permissions = <Permission>[].obs;
  final RxList<Department> _departments = <Department>[].obs;
  
  // النظام والسجلات
  final RxList<SystemLog> _systemLogs = <SystemLog>[].obs;
  final RxList<ActivityLog> _activityLogs = <ActivityLog>[].obs;
  final RxList<Backup> _backups = <Backup>[].obs;
  final RxList<setting_models.SystemSetting> _systemSettings = <setting_models.SystemSetting>[].obs;

  // ===== حالة التحميل والأخطاء =====
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingUsers = false.obs;
  final RxBool _isLoadingRoles = false.obs;
  final RxBool _isLoadingPermissions = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // ===== المتغيرات المساعدة =====
  final Rx<User?> _selectedUser = Rx<User?>(null);
  final Rx<Role?> _selectedRole = Rx<Role?>(null);
  final RxMap<String, dynamic> _statistics = <String, dynamic>{}.obs;

  // ===== Getters للبيانات =====
  List<User> get users => _users;
  List<Role> get roles => _roles;
  List<Permission> get permissions => _permissions;
  List<Department> get departments => _departments;
  List<SystemLog> get systemLogs => _systemLogs;
  List<ActivityLog> get activityLogs => _activityLogs;
  List<Backup> get backups => _backups;
  List<setting_models.SystemSetting> get systemSettings => _systemSettings;

  // ===== Getters للحالة =====
  bool get isLoading => _isLoading.value;
  bool get isLoadingUsers => _isLoadingUsers.value;
  bool get isLoadingRoles => _isLoadingRoles.value;
  bool get isLoadingPermissions => _isLoadingPermissions.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  User? get selectedUser => _selectedUser.value;
  Role? get selectedRole => _selectedRole.value;
  Map<String, dynamic> get statistics => _statistics;

  // ===== Getters للإحصائيات =====
  int get totalUsers => _users.length;
  int get activeUsers => _users.where((u) => u.isActive).length;
  int get totalRoles => _roles.length;
  int get activeRoles => _roles.where((r) => r.isActive).length;
  int get totalPermissions => _permissions.length;

  @override
  void onInit() {
    super.onInit();
    debugPrint('🚀 تم تهيئة AdminController');
    _initializeData();
  }

  /// تهيئة البيانات الأساسية
  Future<void> _initializeData() async {
    try {
      await Future.wait([
        loadUsers(),
        loadRoles(),
        loadPermissions(),
        loadDepartments(),
      ]);
      debugPrint('✅ تم تحميل جميع البيانات الأساسية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة البيانات: $e');
    }
  }

  // ===== إدارة المستخدمين =====

  /// تحميل جميع المستخدمين
  Future<void> loadUsers() async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل المستخدمين...');
      final users = await _usersApiService.getAllUsers();
      _users.assignAll(users);
      _updateStatistics();
      debugPrint('✅ تم تحميل ${users.length} مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل المستخدمين: $e';
      debugPrint('❌ خطأ في تحميل المستخدمين: $e');
      _users.clear();
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(User user) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري إنشاء مستخدم جديد...');
      final newUser = await _usersApiService.createUser(user);
      _users.add(newUser);
      _updateStatistics();
      _successMessage.value = 'تم إنشاء المستخدم بنجاح';
      debugPrint('✅ تم إنشاء مستخدم جديد: ${newUser.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المستخدم: $e';
      debugPrint('❌ خطأ في إنشاء المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحديث المستخدم...');
      final updatedUser = await _usersApiService.updateUser(user.id, user);
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = updatedUser;
        _updateStatistics();
        _successMessage.value = 'تم تحديث المستخدم بنجاح';
        debugPrint('✅ تم تحديث المستخدم: ${updatedUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المستخدم: $e';
      debugPrint('❌ خطأ في تحديث المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    _isLoadingUsers.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري حذف المستخدم...');
      final success = await _usersApiService.deleteUser(userId);
      if (success) {
        _users.removeWhere((u) => u.id == userId);
        _updateStatistics();
        _successMessage.value = 'تم حذف المستخدم بنجاح';
        debugPrint('✅ تم حذف المستخدم: $userId');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المستخدم: $e';
      debugPrint('❌ خطأ في حذف المستخدم: $e');
      return false;
    } finally {
      _isLoadingUsers.value = false;
    }
  }

  // ===== إدارة الأدوار =====

  /// تحميل جميع الأدوار
  Future<void> loadRoles() async {
    _isLoadingRoles.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل الأدوار...');
      final response = await _apiService.get('/api/Roles');
      final data = jsonDecode(response.body) as List;
      final roles = data.map((json) => Role.fromJson(json)).toList();
      _roles.assignAll(roles);
      _updateStatistics();
      debugPrint('✅ تم تحميل ${roles.length} دور');
    } catch (e) {
      _error.value = 'خطأ في تحميل الأدوار: $e';
      debugPrint('❌ خطأ في تحميل الأدوار: $e');
      _roles.clear();
    } finally {
      _isLoadingRoles.value = false;
    }
  }

  // ===== إدارة الصلاحيات =====

  /// تحميل جميع الصلاحيات
  Future<void> loadPermissions() async {
    _isLoadingPermissions.value = true;
    _error.value = '';

    try {
      debugPrint('🔄 جاري تحميل الصلاحيات...');
      final permissions = await _permissionsApiService.getAllPermissions();
      _permissions.assignAll(permissions);
      _updateStatistics();
      debugPrint('✅ تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('❌ خطأ في تحميل الصلاحيات: $e');
      _permissions.clear();
    } finally {
      _isLoadingPermissions.value = false;
    }
  }

  // ===== إدارة الأقسام =====

  /// تحميل جميع الأقسام
  Future<void> loadDepartments() async {
    try {
      debugPrint('🔄 جاري تحميل الأقسام...');
      final response = await _apiService.get('/api/Departments');
      final data = jsonDecode(response.body) as List;
      final departments = data.map((json) => Department.fromJson(json)).toList();
      _departments.assignAll(departments);
      debugPrint('✅ تم تحميل ${departments.length} قسم');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
      _departments.clear();
    }
  }

  /// تحديث الإحصائيات
  void _updateStatistics() {
    _statistics.assignAll({
      'totalUsers': totalUsers,
      'activeUsers': activeUsers,
      'totalRoles': totalRoles,
      'activeRoles': activeRoles,
      'totalPermissions': totalPermissions,
      'lastUpdated': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// مسح الأخطاء والرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// تحديد المستخدم المحدد
  void selectUser(User? user) {
    _selectedUser.value = user;
  }

  /// تحديد الدور المحدد
  void selectRole(Role? role) {
    _selectedRole.value = role;
  }

  /// التحقق من وجود بيانات أساسية
  bool get hasEssentialData => _users.isNotEmpty || _systemSettings.isNotEmpty;

  /// التحقق من وجود أخطاء
  bool get hasError => _error.value.isNotEmpty;

  /// التحقق من وجود رسائل نجاح
  bool get hasSuccessMessage => _successMessage.value.isNotEmpty;

  /// تحميل سجلات النظام
  Future<void> loadSystemLogs() async {
    try {
      debugPrint('🔄 جاري تحميل سجلات النظام...');
      final response = await _apiService.get('/api/SystemLogs');
      final data = jsonDecode(response.body) as List;
      final logs = data.map((json) => SystemLog.fromJson(json)).toList();
      _systemLogs.assignAll(logs);
      debugPrint('✅ تم تحميل ${logs.length} سجل نظام');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجلات النظام: $e');
      _systemLogs.clear();
    }
  }

  /// تحميل سجلات النشاط
  Future<void> loadActivityLogs() async {
    try {
      debugPrint('🔄 جاري تحميل سجلات النشاط...');
      final logs = await _activityLogsApiService.getAllActivityLogs();
      _activityLogs.assignAll(logs);
      debugPrint('✅ تم تحميل ${logs.length} سجل نشاط');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجلات النشاط: $e');
      _activityLogs.clear();
    }
  }

  /// تحميل النسخ الاحتياطية
  Future<void> loadBackups() async {
    try {
      debugPrint('🔄 جاري تحميل النسخ الاحتياطية...');
      final backups = await _backupsApiService.getAllBackups();
      _backups.assignAll(backups);
      debugPrint('✅ تم تحميل ${backups.length} نسخة احتياطية');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل النسخ الاحتياطية: $e');
      _backups.clear();
    }
  }

  /// تحميل إعدادات النظام
  Future<void> loadSystemSettings() async {
    try {
      debugPrint('🔄 جاري تحميل إعدادات النظام...');
      final settings = await _systemSettingsApiService.getAllSettings();
      _systemSettings.assignAll(settings);
      debugPrint('✅ تم تحميل ${settings.length} إعداد نظام');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات النظام: $e');
      _systemSettings.clear();
    }
  }

  /// تحديث جميع البيانات
  Future<void> refreshAllData() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      await Future.wait([
        loadUsers(),
        loadRoles(),
        loadPermissions(),
        loadDepartments(),
        loadSystemLogs(),
        loadActivityLogs(),
        loadBackups(),
        loadSystemSettings(),
      ]);
      _updateStatistics();
      _successMessage.value = 'تم تحديث جميع البيانات بنجاح';
      debugPrint('✅ تم تحديث جميع البيانات الإدارية');
    } catch (e) {
      _error.value = 'خطأ في تحديث البيانات: $e';
      debugPrint('❌ خطأ في تحديث البيانات الإدارية: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void onClose() {
    debugPrint('🔄 تم إغلاق AdminController');
    super.onClose();
  }
}
