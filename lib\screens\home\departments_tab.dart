import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_styles.dart';
// import '../../controllers/auth_controller.dart';
import '../../models/department_model.dart';
import '../../repositories/department_repository.dart';
import '../../services/api/departments_api_service.dart';
import '../departments/department_detail_screen.dart';
import 'department_hierarchy_widget.dart';

/// تبويب الأقسام
/// يعرض قائمة بالأقسام المتاحة في النظام
class DepartmentsTab extends StatefulWidget {
  const DepartmentsTab({super.key});

  @override
  State<DepartmentsTab> createState() => _DepartmentsTabState();
}

class _DepartmentsTabState extends State<DepartmentsTab> {
  final _departmentsApiService = DepartmentsApiService();
  final _departmentRepository = DepartmentRepository();

  List<Department> _departments = [];
  String? _errorMessage;

  // متغير لتخزين ملخص إحصائيات جميع الأقسام
  Map<String, dynamic>? _allDepartmentsStats;
  bool _isLoadingStats = false;

  List<Department> _hierarchy = [];
  bool _isLoadingHierarchy = false;
  String? _hierarchyError;

  @override
  void initState() {
    super.initState();
    loadDepartments();
    _loadAllDepartmentsStats();
    _loadDepartmentHierarchy();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// تحميل الأقسام من قاعدة البيانات
  Future<void> loadDepartments() async {
    setState(() {
      _errorMessage = null;
    });

    try {
      // تحميل جميع الأقسام بدون تحقق من الصلاحيات
      _departments = await _departmentRepository.getAllDepartments();
      /*
      // تحميل الأقسام حسب صلاحيات المستخدم
      if (_authController.isSuperAdmin) {
        _departments = await _departmentRepository.getAllDepartments();
      } else if (_authController.isAdmin) {
        _departments = await _departmentRepository.getAllDepartments();
      } else if (_authController.isManager || _authController.isSupervisor) {
        if (_authController.currentUser.value?.departmentId != null) {
          final department = await _departmentRepository.getDepartmentById(
              _authController.currentUser.value!.departmentId!);
          _departments = department != null ? [department] : [];
        } else {
          _departments = [];
        }
      } else {
        _departments = await _departmentRepository.getActiveDepartments();
      }
      */
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء تحميل الأقسام: $e';
    } finally {
      setState(() {});
    }
  }

  /// جلب ملخص إحصائيات جميع الأقسام من API
  Future<void> _loadAllDepartmentsStats() async {
    setState(() {
      _isLoadingStats = true;
    });
    try {
      final stats = await _departmentsApiService.getAllDepartmentsStatistics();
      setState(() {
        _allDepartmentsStats = stats;
      });
    } catch (e) {
      // في حال الخطأ، تجاهل الإحصائيات ولا توقف التطبيق
      setState(() {
        _allDepartmentsStats = null;
      });
    } finally {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }

  /// تحميل التسلسل الهرمي للأقسام
  Future<void> _loadDepartmentHierarchy() async {
    setState(() {
      _isLoadingHierarchy = true;
      _hierarchyError = null;
    });
    try {
      final hierarchy = await _departmentsApiService.getDepartmentHierarchy();
      
      // Debug: طباعة التسلسل الهرمي للتحقق من التكرار
      debugPrint('=== DEBUG: التسلسل الهرمي المستلم ===');
      for (int i = 0; i < hierarchy.length; i++) {
        final dept = hierarchy[i];
        debugPrint('[$i] القسم الرئيسي: ${dept.name} (ID: ${dept.id})');
        for (int j = 0; j < dept.children.length; j++) {
          final child = dept.children[j];
          debugPrint('  [$j] القسم الفرعي: ${child.name} (ID: ${child.id}, Parent: ${child.parentId})');
        }
      }
      debugPrint('=== نهاية DEBUG ===');
      
      setState(() {
        _hierarchy = hierarchy;
      });
    } catch (e) {
      setState(() {
        _hierarchyError = 'حدث خطأ أثناء تحميل التسلسل الهرمي: $e';
      });
    } finally {
      setState(() {
        _isLoadingHierarchy = false;
      });
    }
  }

  /// عرض تفاصيل القسم
  void _showDepartmentDetails(Department department) {
    // السماح بالوصول لجميع المستخدمين بدون تحقق من الصلاحيات
    Get.to(() => DepartmentDetailScreen(departmentId: department.id, department: department))
        ?.then((_) {
      loadDepartments();
    });
  }

  /// إنشاء قسم جديد
  void _createNewDepartment() {
    // السماح لجميع المستخدمين بإنشاء قسم جديد بدون تحقق من الصلاحيات
    _showCreateDepartmentDialog();
    /*
    if (!_authController.isSuperAdmin && !_authController.isAdmin) {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لإنشاء قسم جديد. هذه الصلاحية متاحة للمديرين العامين ومديري النظام فقط.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }
    _showCreateDepartmentDialog();
    */
  }

  /// عرض نموذج إنشاء قسم جديد
  void _showCreateDepartmentDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء قسم جديد'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'اسم القسم',
                  prefixIcon: const Icon(Icons.business),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'وصف القسم',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال اسم القسم',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }

              // إنشاء قسم جديد
              final newDepartment = Department(
                id: 0, // سيتم تعيين المعرف من قبل الخادم
                name: nameController.text.trim(),
                description: descriptionController.text.trim(),
                isActive: true,
                createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000, // Unix timestamp
              );

              try {
                await _departmentRepository.createDepartment(newDepartment);
                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم إنشاء القسم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green.shade100,
                  colorText: Colors.green.shade800,
                );
                loadDepartments();
              } catch (e) {
                Get.snackbar(
                  'خطأ',
                  'حدث خطأ أثناء إنشاء القسم: $e',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  /// إضافة قسم فرعي
  Future<void> _addSubDepartment(Department parent) async {
    final result = await _showDepartmentDialog(parentDepartment: parent);
    if (result == true) {
      _loadDepartmentHierarchy();
      loadDepartments();
    }
  }

  /// تعديل قسم
  Future<void> _editDepartment(Department department) async {
    final result = await _showDepartmentDialog(department: department);
    if (result == true) {
      _loadDepartmentHierarchy();
      loadDepartments();
    }
  }

  /// حذف قسم
  Future<void> _deleteDepartment(Department department) async {
    // يمكنك استخدام منطق التأكيد المناسب هنا
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القسم "${department.name}"؟'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('إلغاء')),
          ElevatedButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('حذف')),
        ],
      ),
    );
    if (confirmed != true) return;
    try {
      final success = await _departmentsApiService.deleteDepartment(department.id);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حذف القسم بنجاح')));
        _loadDepartmentHierarchy();
        loadDepartments();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('فشل في حذف القسم')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ: $e')));
    }
  }

  /// حوار إضافة/تعديل قسم (مبسط)
  Future<bool?> _showDepartmentDialog({Department? department, Department? parentDepartment}) async {
    final nameController = TextEditingController(text: department?.name ?? '');
    final descriptionController = TextEditingController(text: department?.description ?? '');
    bool isActive = department?.isActive ?? true;
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(department == null ? 'إضافة قسم جديد' : 'تعديل القسم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (parentDepartment != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'سيتم إضافة هذا القسم كقسم فرعي تحت "${parentDepartment.name}"',
                          style: TextStyle(color: Colors.blue.shade700),
                        ),
                      ),
                    ],
                  ),
                ),
              TextField(
                controller: nameController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'اسم القسم *',
                  prefixIcon: const Icon(Icons.business),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'وصف القسم',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              StatefulBuilder(
                builder: (context, setState) {
                  return SwitchListTile(
                    title: const Text('نشط'),
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () async {
              final name = nameController.text.trim();
              if (name.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('اسم القسم مطلوب')));
                return;
              }
              try {
                if (department == null) {
                  // إضافة قسم جديد
                  final newDepartment = Department(
                    id: 0,
                    name: name,
                    description: descriptionController.text.trim(),
                    parentId: parentDepartment?.id,
                    level: (parentDepartment?.level ?? -1) + 1,
                    isActive: isActive,
                    createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  );
                  await _departmentsApiService.createDepartment(newDepartment);
                  // تحديث الشجرة مباشرة بعد الإضافة
                  _loadDepartmentHierarchy();
                } else {
                  // تعديل قسم موجود
                  final updatedDepartment = department.copyWith(
                    name: name,
                    description: descriptionController.text.trim(),
                    isActive: isActive,
                  );
                  await _departmentsApiService.updateDepartment(updatedDepartment.id, updatedDepartment);
                  // تحديث الشجرة مباشرة بعد التعديل
                  _loadDepartmentHierarchy();
                }
                Navigator.of(context).pop(true);
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ: $e')));
              }
            },
            child: Text(department == null ? 'إضافة' : 'تحديث'),
          ),
        ],
      ),
    );
  }

  /// حوار دمج قسم مع قسم آخر
  void _showMergeDepartmentDialog(BuildContext context, Department fromDepartment) async {
    final departmentsApi = DepartmentsApiService();
    List<Department> departments = [];
    int? selectedDepartmentId;
    bool isLoading = true;
    String? error;

    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            if (isLoading) {
              departmentsApi.getAllDepartments().then((list) {
                setState(() {
                  departments = list;
                  isLoading = false;
                });
              }).catchError((e) {
                setState(() {
                  error = 'خطأ في تحميل الأقسام';
                  isLoading = false;
                });
              });
            }
            return AlertDialog(
              title: const Text('دمج مع قسم آخر'),
              content: isLoading
                  ? const SizedBox(height: 80, child: Center(child: CircularProgressIndicator()))
                  : error != null
                      ? Text(error!)
                      : DropdownButtonFormField<int>(
                          value: selectedDepartmentId,
                          items: departments
                              .where((d) => d.id != fromDepartment.id)
                              .map((dept) => DropdownMenuItem(
                                    value: dept.id,
                                    child: Text(dept.name),
                                  ))
                              .toList(),
                          onChanged: (val) => setState(() => selectedDepartmentId = val),
                          decoration: const InputDecoration(
                            labelText: 'اختر القسم الهدف',
                            prefixIcon: Icon(Icons.business),
                          ),
                        ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: !isLoading && selectedDepartmentId != null
                      ? () async {
                          Navigator.of(context).pop();
                          // تنفيذ الدمج
                          Get.dialog(const Center(child: CircularProgressIndicator()), barrierDismissible: false);
                          final success = await departmentsApi.mergeDepartments(fromDepartment.id, selectedDepartmentId!);
                          Get.back();
                          if (success) {
                            Get.snackbar('تم الدمج', 'تم دمج القسمين بنجاح', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green.shade100, colorText: Colors.green.shade800);
                            loadDepartments();
                            _loadDepartmentHierarchy();
                          } else {
                            Get.snackbar('خطأ', 'فشل دمج الأقسام', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red.shade100, colorText: Colors.red.shade800);
                          }
                        }
                      : null,
                  child: const Text('دمج'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// حوار نقل القسم (اختيار القسم الأب الجديد)
  void _showMoveDepartmentDialog(BuildContext context, Department department) async {
    final allDepartments = _hierarchy
        .expand((d) => [d, ...d.children])
        .where((d) => d.id != department.id)
        .toSet() // إزالة التكرار إن وجد
        .toList();

    // تأكد أن parentId الحالي موجود مرة واحدة فقط في القائمة، وإلا اجعله null
    final parentIdCount = allDepartments.where((d) => d.id == department.parentId).length;
    int? selectedParentId = parentIdCount == 1 ? department.parentId : null;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('نقل القسم: ${department.name}'),
          content: DropdownButtonFormField<int?> (
            value: selectedParentId,
            items: [
              const DropdownMenuItem<int?>(value: null, child: Text('بدون أب (قسم رئيسي)')),
              ...allDepartments.map((d) => DropdownMenuItem<int?>(
                    value: d.id,
                    child: Text(d.name),
                  )),
            ],
            onChanged: (value) {
              selectedParentId = value;
            },
            decoration: const InputDecoration(labelText: 'اختر القسم الأب الجديد'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final api = DepartmentsApiService();
                final result = await api.moveDepartment(department.id, selectedParentId);
                if (result) {
                  _loadDepartmentHierarchy();
                  Get.snackbar('تم النقل', 'تم نقل القسم بنجاح', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.green.shade50, colorText: Colors.green.shade800);
                } else {
                  Get.snackbar('خطأ', 'فشل نقل القسم', snackPosition: SnackPosition.BOTTOM, backgroundColor: Colors.red.shade100, colorText: Colors.red.shade800);
                }
              },
              child: const Text('نقل'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأقسام'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadDepartmentHierarchy();
              loadDepartments();
              _loadAllDepartmentsStats();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'departments_fab',
        onPressed: () => _showDepartmentDialog(),
        icon: const Icon(Icons.add),
        label: const Text('إضافة قسم'),
      ),
      body: Column(
        children: [
          _buildStatsBar(),
          Expanded(
            child: _isLoadingHierarchy
                ? const Center(child: CircularProgressIndicator())
                : _hierarchyError != null
                    ? Center(child: Text(_hierarchyError!))
                    : _hierarchy.isEmpty
                        ? _buildEmptyWidget()
                        : Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                            child: Card(
                              elevation: 2,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              child: ListView(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(12.0),
                                    child: Text(
                                      'جميع الأقسام (تسلسل هرمي)',
                                      style: AppStyles.headingMedium,
                                    ),
                                  ),
                                  DepartmentHierarchyWidget(
                                    hierarchy: _hierarchy,
                                    canEdit: true,
                                    onAddSubDepartment: _addSubDepartment,
                                    onEditDepartment: _editDepartment,
                                    onDeleteDepartment: _deleteDepartment,
                                    onDepartmentTap: _showDepartmentDetails,
                                    onDepartmentMerge: _showMergeDepartmentDialog,
                                    onDepartmentMove: _showMoveDepartmentDialog,
                                  ),
                                ],
                              ),
                            ),
                          ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط ملخص الإحصائيات لجميع الأقسام
  Widget _buildStatsBar() {
    if (_isLoadingStats) {
      return const LinearProgressIndicator();
    }
    if (_allDepartmentsStats == null) {
      return const SizedBox(height: 8);
    }
    // استخراج القيم من ملخص الإحصائيات
    final totalDepartments = _allDepartmentsStats!["totalDepartments"] ?? _departments.length;
    final totalTasks = _allDepartmentsStats!["totalTasks"] ?? 0;
    final completedTasks = _allDepartmentsStats!["completedTasks"] ?? 0;
    final overdueTasks = _allDepartmentsStats!["overdueTasks"] ?? 0;
    final avgCompletion = _allDepartmentsStats!["avgCompletion"] ?? 0.0;

    return Card(
      margin: const EdgeInsets.all(12),
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('الأقسام', totalDepartments.toString(), Icons.business, Colors.blue),
            _buildStatItem('المهام', totalTasks.toString(), Icons.task, Colors.orange),
            _buildStatItem('مكتملة', completedTasks.toString(), Icons.check_circle, Colors.green),
            _buildStatItem('متأخرة', overdueTasks.toString(), Icons.warning, Colors.red),
            _buildStatItem('متوسط الإنجاز', '${avgCompletion.toStringAsFixed(1)}%', Icons.percent, Colors.purple),
          ],
        ),
      ),
    );
  }

  /// عنصر إحصائي صغير
  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(value, style: TextStyle(fontWeight: FontWeight.bold, color: color)),
        Text(title, style: const TextStyle(fontSize: 11, color: Colors.black54)),
      ],
    );
  }

  /// بناء واجهة عرض القائمة الفارغة
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.business,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أقسام',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد أقسام متاحة حالياً. قم بإنشاء قسم جديد باستخدام زر الإضافة',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _createNewDepartment,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء قسم جديد'),
          ),
        ],
      ),
    );
  }
}