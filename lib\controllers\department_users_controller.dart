import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/department_model.dart';
import '../services/api/departments_api_service.dart';
import '../services/api/users_api_service.dart';

/// كونترولر إدارة المستخدمين بين الأقسام
class DepartmentUsersController extends GetxController {
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();
  final UsersApiService _usersApiService = UsersApiService();

  // البيانات الأساسية
  final RxList<User> _departmentUsers = <User>[].obs;
  final RxList<User> _availableUsers = <User>[].obs;
  final RxList<Department> _departments = <Department>[].obs;
  final RxList<int> _selectedUserIds = <int>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxBool _isTransferring = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;

  // البحث والفلترة
  final RxString _searchQuery = ''.obs;
  final RxString _departmentSearchQuery = ''.obs;
  final RxList<User> _filteredAvailableUsers = <User>[].obs;
  final RxList<Department> _filteredDepartments = <Department>[].obs;

  // القسم المحدد حالياً
  final Rx<Department?> _selectedDepartment = Rx<Department?>(null);

  // Getters
  List<User> get departmentUsers => _departmentUsers;
  List<User> get availableUsers => _searchQuery.value.isEmpty
      ? _availableUsers
      : _filteredAvailableUsers;
  List<Department> get departments => _departmentSearchQuery.value.isEmpty
      ? _departments
      : _filteredDepartments;
  List<int> get selectedUserIds => _selectedUserIds;
  bool get isLoading => _isLoading.value;
  bool get isTransferring => _isTransferring.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  Department? get selectedDepartment => _selectedDepartment.value;
  String get searchQuery => _searchQuery.value;
  String get departmentSearchQuery => _departmentSearchQuery.value;

  @override
  void onInit() {
    super.onInit();
    loadDepartments();
  }

  /// تحميل جميع الأقسام
  Future<void> loadDepartments() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final departments = await _departmentsApiService.getAllDepartments();
      _departments.assignAll(departments);
      _updateFilteredLists();

      debugPrint('تم تحميل ${departments.length} قسم');
    } catch (e) {
      _error.value = 'خطأ في تحميل الأقسام: $e';
      debugPrint('خطأ في تحميل الأقسام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديد القسم وتحميل مستخدميه
  Future<void> selectDepartment(Department department) async {
    _selectedDepartment.value = department;
    await loadDepartmentUsers(department.id);
  }

  /// تحميل مستخدمي قسم محدد
  Future<void> loadDepartmentUsers(int departmentId) async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final users = await _departmentsApiService.getDepartmentEmployees(departmentId);
      _departmentUsers.assignAll(users.map((json) => User.fromJson(json)).toList());
      
      debugPrint('تم تحميل ${users.length} مستخدم للقسم $departmentId');
    } catch (e) {
      _error.value = 'خطأ في تحميل مستخدمي القسم: $e';
      debugPrint('خطأ في تحميل مستخدمي القسم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المستخدمين المتاحين (غير المعينين لأي قسم فقط)
  Future<void> loadAvailableUsers() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      final allUsers = await _usersApiService.getAllUsers();

      // فلترة المستخدمين غير المحددين في أي قسم فقط
      final unassignedUsers = allUsers.where((user) =>
        user.departmentId == null && user.isActive && !user.isDeleted
      ).toList();

      _availableUsers.assignAll(unassignedUsers);
      _updateFilteredLists();

      debugPrint('تم تحميل ${unassignedUsers.length} مستخدم غير محدد في أي قسم من أصل ${allUsers.length} مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل المستخدمين المتاحين: $e';
      debugPrint('خطأ في تحميل المستخدمين المتاحين: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// نقل مستخدم إلى قسم آخر
  Future<bool> transferUser(int userId, int toDepartmentId) async {
    try {
      _isTransferring.value = true;
      _error.value = '';
      _successMessage.value = '';

      final result = await _departmentsApiService.transferEmployee(userId, toDepartmentId);
      if (result) {
        _successMessage.value = 'تم نقل المستخدم بنجاح';
        // عرض رسالة نجاح
        Get.snackbar(
          'نجح',
          _successMessage.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        // تحديث القوائم المحلية
        await _refreshAfterTransfer();
        debugPrint('تم نقل المستخدم $userId إلى القسم $toDepartmentId بنجاح');
        return true;
      } else {
        _error.value = 'فشل في نقل المستخدم';
        // عرض رسالة خطأ
        Get.snackbar(
          'خطأ',
          _error.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في نقل المستخدم: $e';
      debugPrint('خطأ في نقل المستخدم: $e');
      return false;
    } finally {
      _isTransferring.value = false;
    }
  }

  /// تعيين مستخدمين متعددين لقسم
  Future<bool> assignUsersToCurrentDepartment(List<int> userIds) async {
    if (_selectedDepartment.value == null) {
      _error.value = 'يجب تحديد قسم أولاً';
      return false;
    }

    try {
      _isTransferring.value = true;
      _error.value = '';
      _successMessage.value = '';

      final result = await _departmentsApiService.assignUsersToDepartment(
        _selectedDepartment.value!.id,
        userIds,
      );
      
      if (result['success'] == true) {
        _successMessage.value = result['message'] ?? 'تم تعيين المستخدمين بنجاح';

        // عرض رسالة نجاح
        Get.snackbar(
          'نجح',
          _successMessage.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // تحديث القوائم المحلية
        await _refreshAfterTransfer();

        // مسح التحديد
        _selectedUserIds.clear();

        debugPrint('تم تعيين ${userIds.length} مستخدم للقسم ${_selectedDepartment.value!.id} بنجاح');
        return true;
      } else {
        _error.value = result['message'] ?? 'فشل في تعيين المستخدمين';

        // عرض رسالة خطأ
        Get.snackbar(
          'خطأ',
          _error.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في تعيين المستخدمين: $e';
      debugPrint('خطأ في تعيين المستخدمين: $e');
      return false;
    } finally {
      _isTransferring.value = false;
    }
  }

  /// تحديث القوائم بعد عملية النقل
  Future<void> _refreshAfterTransfer() async {
    // تحديث قائمة المستخدمين المتاحين (غير المحددين في أي قسم)
    await loadAvailableUsers();

    // تحديث مستخدمي القسم المحدد إذا كان هناك قسم محدد
    if (_selectedDepartment.value != null) {
      await loadDepartmentUsers(_selectedDepartment.value!.id);
    }

    // تحديث قائمة الأقسام للحصول على أحدث البيانات
    await loadDepartments();

    debugPrint('✅ تم تحديث جميع القوائم بعد عملية النقل/التعيين');
  }

  /// إضافة/إزالة مستخدم من التحديد
  void toggleUserSelection(int userId) {
    if (_selectedUserIds.contains(userId)) {
      _selectedUserIds.remove(userId);
    } else {
      _selectedUserIds.add(userId);
    }
  }

  /// تحديد جميع المستخدمين
  void selectAllUsers() {
    _selectedUserIds.assignAll(_availableUsers.map((user) => user.id).toList());
  }

  /// إلغاء تحديد جميع المستخدمين
  void clearSelection() {
    _selectedUserIds.clear();
  }

  /// التحقق من تحديد مستخدم
  bool isUserSelected(int userId) {
    return _selectedUserIds.contains(userId);
  }

  /// مسح الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }

  /// إعادة تحميل البيانات
  @override
  Future<void> refresh() async {
    await loadDepartments();
    if (_selectedDepartment.value != null) {
      await loadDepartmentUsers(_selectedDepartment.value!.id);
    }
    await loadAvailableUsers();
  }

  /// البحث في المستخدمين
  List<User> searchUsers(String query, List<User> users) {
    if (query.isEmpty) return users;
    
    return users.where((user) {
      return user.name.toLowerCase().contains(query.toLowerCase()) ||
             user.email.toLowerCase().contains(query.toLowerCase()) ||
             (user.username?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// فلترة المستخدمين حسب القسم
  List<User> filterUsersByDepartment(int? departmentId, List<User> users) {
    if (departmentId == null) return users;
    
    return users.where((user) => user.departmentId == departmentId).toList();
  }

  /// الحصول على اسم القسم
  String getDepartmentName(int? departmentId) {
    if (departmentId == null) return 'غير محدد';

    final department = _departments.firstWhereOrNull((d) => d.id == departmentId);
    return department?.name ?? 'قسم غير معروف';
  }

  /// تعيين استعلام البحث وتطبيق الفلترة
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _applySearchFilter();
  }

  /// تعيين استعلام البحث في الأقسام
  void setDepartmentSearchQuery(String query) {
    _departmentSearchQuery.value = query;
    _applyDepartmentSearchFilter();
  }

  /// تطبيق فلتر البحث
  void _applySearchFilter() {
    if (_searchQuery.value.isEmpty) {
      _filteredAvailableUsers.clear();
      return;
    }

    final filtered = searchUsers(_searchQuery.value, _availableUsers);
    _filteredAvailableUsers.assignAll(filtered);
  }

  /// تطبيق فلتر البحث في الأقسام
  void _applyDepartmentSearchFilter() {
    if (_departmentSearchQuery.value.isEmpty) {
      _filteredDepartments.clear();
      return;
    }

    final filtered = _departments.where((dept) {
      return dept.name.toLowerCase().contains(_departmentSearchQuery.value.toLowerCase()) ||
             (dept.description?.toLowerCase().contains(_departmentSearchQuery.value.toLowerCase()) ?? false);
    }).toList();

    _filteredDepartments.assignAll(filtered);
  }

  /// تحديث القوائم المفلترة عند تغيير البيانات
  void _updateFilteredLists() {
    if (_searchQuery.value.isNotEmpty) {
      _applySearchFilter();
    }
    if (_departmentSearchQuery.value.isNotEmpty) {
      _applyDepartmentSearchFilter();
    }
  }

  /// تصدير تقرير إدارة المستخدمين
  Map<String, dynamic> generateUserManagementReport() {
    final report = <String, dynamic>{
      'reportTitle': 'تقرير إدارة المستخدمين بين الأقسام',
      'generatedAt': DateTime.now().toIso8601String(),
      'totalDepartments': _departments.length,
      'totalUsers': _availableUsers.length,
      'departments': _departments.map((dept) {
        final deptUsers = _availableUsers.where((user) => user.departmentId == dept.id).toList();
        return {
          'id': dept.id,
          'name': dept.name,
          'description': dept.description,
          'isActive': dept.isActive,
          'usersCount': deptUsers.length,
          'users': deptUsers.map((user) => {
            'id': user.id,
            'name': user.name,
            'email': user.email,
            'role': user.roleName,
            'isActive': user.isActive,
            'isOnline': user.isOnline,
          }).toList(),
        };
      }).toList(),
      'unassignedUsers': _availableUsers.where((user) => user.departmentId == null).map((user) => {
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.roleName,
        'isActive': user.isActive,
        'isOnline': user.isOnline,
      }).toList(),
      'statistics': {
        'activeDepartments': _departments.where((d) => d.isActive).length,
        'inactiveDepartments': _departments.where((d) => !d.isActive).length,
        'activeUsers': _availableUsers.where((u) => u.isActive).length,
        'inactiveUsers': _availableUsers.where((u) => !u.isActive).length,
        'onlineUsers': _availableUsers.where((u) => u.isOnline).length,
        'offlineUsers': _availableUsers.where((u) => !u.isOnline).length,
        'unassignedUsers': _availableUsers.where((u) => u.departmentId == null).length,
      },
    };

    return report;
  }

  /// تصدير التقرير كـ JSON
  String exportReportAsJson() {
    final report = generateUserManagementReport();
    return jsonEncode(report);
  }

  /// نقل مجموعة من المستخدمين إلى قسم واحد
  Future<bool> transferMultipleUsers(List<int> userIds, int toDepartmentId) async {
    if (userIds.isEmpty) {
      _error.value = 'يجب تحديد مستخدم واحد على الأقل';
      return false;
    }

    try {
      _isTransferring.value = true;
      _error.value = '';
      _successMessage.value = '';

      int successCount = 0;
      int failCount = 0;
      List<String> errors = [];

      for (int userId in userIds) {
        try {
          final result = await _departmentsApiService.transferEmployee(userId, toDepartmentId);
          if (result) {
            successCount++;
          } else {
            failCount++;
            errors.add('المستخدم $userId: فشل في النقل');
          }
        } catch (e) {
          failCount++;
          errors.add('المستخدم $userId: $e');
        }
      }

      if (successCount > 0) {
        _successMessage.value = 'تم نقل $successCount مستخدم بنجاح';

        if (failCount == 0) {
          Get.snackbar(
            'نجح',
            _successMessage.value,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'نجح جزئياً',
            '$_successMessage، فشل في نقل $failCount مستخدم',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
          );
        }

        // تحديث القوائم المحلية
        await _refreshAfterTransfer();
        return true;
      } else {
        _error.value = 'فشل في نقل جميع المستخدمين: ${errors.join(', ')}';

        Get.snackbar(
          'خطأ',
          _error.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      _error.value = 'خطأ في النقل المجموعي: $e';
      debugPrint('خطأ في النقل المجموعي: $e');

      Get.snackbar(
        'خطأ',
        _error.value,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      _isTransferring.value = false;
    }
  }

  @override
  void onClose() {
    // تنظيف الموارد
    _departmentUsers.clear();
    _availableUsers.clear();
    _departments.clear();
    _selectedUserIds.clear();
    _filteredAvailableUsers.clear();
    _filteredDepartments.clear();
    super.onClose();
  }
}
