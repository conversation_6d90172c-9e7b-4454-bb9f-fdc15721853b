/// إعدادات API للتطبيق
/// متوافق مع ASP.NET Core API
class ApiConfig {
  /// عنوان URL الأساسي للخادم - متوافق مع webApi/Program.cs
  static const String baseUrl = 'https://localhost:7111';
  
  /// مهلة الاتصال بالثواني
  static const int connectionTimeout = 30;
  
  /// مهلة الاستقبال بالثواني
  static const int receiveTimeout = 30;
  
  /// إصدار API
  static const String apiVersion = 'v1';
  
  /// رؤوس HTTP الافتراضية
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json; charset=UTF-8',
    'Accept': 'application/json',
  };
  
  /// نقاط النهاية للـ API
  static const String authEndpoint = '/api/auth';
  static const String usersEndpoint = '/api/users';
  static const String tasksEndpoint = '/api/tasks';
  static const String departmentsEndpoint = '/api/departments';
  static const String reportsEndpoint = '/api/reports';
  static const String contributionReportsEndpoint = '/api/contribution-reports';
  static const String attachmentsEndpoint = '/api/attachments';
  static const String commentsEndpoint = '/api/comments';
  static const String notificationsEndpoint = '/api/notifications';
  static const String calendarEventsEndpoint = '/api/calendar-events';
  static const String chatGroupsEndpoint = '/api/chat-groups';
  static const String messagesEndpoint = '/api/messages';
  static const String archiveDocumentsEndpoint = '/api/archive-documents';
  static const String archiveCategoriesEndpoint = '/api/archive-categories';
  static const String taskDocumentsEndpoint = '/api/TaskDocuments';
  static const String systemLogsEndpoint = '/api/system-logs';
  static const String activityLogsEndpoint = '/api/activity-logs';
  static const String backupsEndpoint = '/api/backups';
  static const String systemSettingsEndpoint = '/api/system-settings';
  static const String userPermissionsEndpoint = '/api/user-permissions';
  static const String rolePermissionsEndpoint = '/api/role-permissions';
  static const String reportSchedulesEndpoint = '/api/report-schedules';
  static const String taskTypesEndpoint = '/api/task-types';
  static const String taskStatusEndpoint = '/api/task-status';
  static const String taskPriorityEndpoint = '/api/task-priority';
  static const String timeTrackingEndpoint = '/api/time-tracking';
  
  /// إعدادات البيئة
  static const bool isProduction = false;
  static const bool enableLogging = true;
  static const bool enableDebugMode = true;
  
  /// إعدادات التخزين المؤقت
  static const int cacheMaxAge = 300; // 5 دقائق
  static const int maxCacheSize = 50 * 1024 * 1024; // 50 MB
  
  /// إعدادات الملفات
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  static const List<String> allowedFileTypes = [
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
    'jpg', 'jpeg', 'png', 'gif', 'bmp',
    'txt', 'csv', 'json', 'xml',
    'zip', 'rar', '7z'
  ];
  
  /// إعدادات الصفحات
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  /// إعدادات إعادة المحاولة
  static const int maxRetryAttempts = 3;
  static const int retryDelay = 1000; // milliseconds
  
  /// الحصول على URL كامل لنقطة نهاية معينة
  static String getFullUrl(String endpoint) {
    return '$baseUrl$endpoint';
  }
  
  /// الحصول على رؤوس HTTP مع التوكن
  static Map<String, String> getHeadersWithToken(String? token) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
  
  /// التحقق من صحة نوع الملف
  static bool isFileTypeAllowed(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return allowedFileTypes.contains(extension);
  }
  
  /// التحقق من حجم الملف
  static bool isFileSizeAllowed(int fileSize) {
    return fileSize <= maxFileSize;
  }
  
  /// الحصول على معرف فريد للطلب
  static String generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
