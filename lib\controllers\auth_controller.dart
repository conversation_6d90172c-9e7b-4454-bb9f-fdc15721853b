import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/auth_models.dart';
import '../services/api/api_service.dart';
import '../services/api/auth_api_service.dart';
import '../services/storage_service.dart';
import '../routes/routes.dart';

class AuthController extends GetxController {
  final Rx<UserInfo?> currentUser = Rx<UserInfo?>(null);
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  late final AuthApiService _authService;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      // التأكد من تهيئة StorageService
      if (!Get.isRegistered<StorageService>()) {
        final storageService = StorageService.instance;
        Get.put(storageService, permanent: true);
      }

      // تهيئة AuthApiService
      _authService = AuthApiService();
      await _authService.initialize();

      // تحميل التوكن من التخزين المحلي وتحديث ApiService به
      final storageService = Get.find<StorageService>();
      final token = await storageService.getString('auth_token');
      if (token != null && token.isNotEmpty) {
        // تحديث التوكن في ApiService عبر إعادة التهيئة
        if (Get.isRegistered<ApiService>()) {
          final apiService = Get.find<ApiService>();
          await apiService.initialize();
        }
      }

      // محاولة تحميل المستخدم الحالي من التخزين المحلي
      await _loadCurrentUser();

      debugPrint('تم تهيئة AuthController بنجاح');
      update();
    } catch (e) {
      debugPrint('خطأ في تهيئة AuthController: $e');
      error.value = 'خطأ في تهيئة النظام:  ${e.toString()}';
    }
  }

  bool get isLoggedIn => currentUser.value != null;
  bool get isSuperAdmin => currentUser.value?.role == UserRole.superAdmin;
  bool get isAdmin => currentUser.value?.role == UserRole.admin;
  bool get isManager => currentUser.value?.role == UserRole.manager;
  bool get isSupervisor => currentUser.value?.role == UserRole.supervisor;

  /// الحصول على معرف المستخدم الحالي
  int? get currentUserId => currentUser.value?.id;

  /// الحصول على معرف المستخدم الحالي مع قيمة افتراضية
  int getCurrentUserIdOrDefault() => currentUser.value?.id ?? 1;

  /// التحقق من كون المستخدم مدير قسم (مدير أو أعلى)
  bool get isDepartmentManager => isManager || isAdmin || isSuperAdmin;

  /// التحقق من كون المستخدم مدير (أي نوع من المديرين)
  bool get isAnyAdmin => isSuperAdmin || isAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارية عامة (مدير النظام العام فقط)
  bool get hasSystemAdminRights => isSuperAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارة الإدارة (مدير النظام العام أو مدير إدارة)
  bool get hasDepartmentAdminRights => isSuperAdmin || isAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارية (مدير أو أعلى)
  bool get hasManagerRights =>
      currentUser.value?.role.isManagerOrAbove ?? false;

  /// التحقق من كون المستخدم يمكنه رؤية جميع الصفحات (مدير أو أعلى)
  bool get canSeeAllPages => hasManagerRights;

  /// تحميل المستخدم الحالي من التخزين المحلي
  Future<void> _loadCurrentUser() async {
    try {
      final storageService = Get.find<StorageService>();
      final token = await storageService.getString('auth_token');

      if (token != null && token.isNotEmpty) {
        final user = await _authService.getCurrentUser().timeout(const Duration(seconds: 10), onTimeout: () {
          throw Exception('انتهت مهلة تحميل بيانات المستخدم');
        });
        currentUser.value = user;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدم الحالي: $e');
      currentUser.value = null;
      error.value = 'تعذر تحميل بيانات المستخدم. يرجى إعادة تسجيل الدخول.';
    }
  }

  // Load current user from storage
  Future<void> loadCurrentUser() async {
    isLoading.value = true;
    try {
      final user = await _authService.getCurrentUser().timeout(const Duration(seconds: 10), onTimeout: () {
        throw Exception('انتهت مهلة تحميل بيانات المستخدم');
      });
      currentUser.value = user;
      error.value = '';
      Get.offAllNamed(AppRoutes.home); // التنقل إلى الشاشة الرئيسية
    } catch (e) {
      error.value = e.toString();
      currentUser.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  // Login user
  Future<bool> login(String email, String password) async {
    isLoading.value = true;
    error.value = '';
    update();

    try {
      // إنشاء طلب تسجيل الدخول
      final loginRequest = LoginRequest(
        usernameOrEmail: email,
        password: password,
      );

      // تسجيل الدخول واستلام بيانات المستخدم
      final authResponse = await _authService.login(loginRequest);

      // التحقق من نجاح تسجيل الدخول
      if (authResponse.success && authResponse.user != null) {
        currentUser.value = authResponse.user;

        // حفظ رمز الوصول في ApiService
        final apiService = Get.find<ApiService>();
        await apiService.saveAuthResponse(authResponse);

        // حفظ رمز الوصول في التخزين المحلي أيضًا
        final storageService = Get.find<StorageService>();
        if (authResponse.accessToken != null && authResponse.accessToken!.isNotEmpty) {
          await storageService.setString('auth_token', authResponse.accessToken!);
        }

        debugPrint(
            'تم تسجيل الدخول للمستخدم: ${currentUser.value!.name} (${currentUser.value!.id})');
        debugPrint('الدور: ${currentUser.value!.role}');
        debugPrint('تم حفظ رمز الوصول: ${authResponse.accessToken != null}');

        update();
        return true;
      } else {
        error.value = authResponse.message.isNotEmpty
            ? authResponse.message
            : 'بيانات الدخول غير صحيحة';
        update();
        return false;
      }
    } on ApiException catch (e) {
      // معالجة أخطاء API بشكل خاص
      error.value = e.message;
      debugPrint(
          'ApiException في تسجيل الدخول: ${e.message} (${e.statusCode})');
      return false;
    } catch (e) {
      // معالجة الأخطاء العامة
      String errorMessage = 'حدث خطأ أثناء تسجيل الدخول';
      if (e.toString().contains('ApiException:')) {
        final parts = e.toString().split('ApiException: ');
        if (parts.length > 1) {
          errorMessage = parts[1].split(' (Status:')[0];
        }
      } else if (e.toString().contains('Exception:')) {
        final parts = e.toString().split('Exception: ');
        if (parts.length > 1) {
          errorMessage = parts[1];
        }
      } else if (e.toString().contains('خطأ في تحليل')) {
        errorMessage = 'خطأ في تحليل البيانات من الخادم';
      }
      error.value = errorMessage;
      debugPrint('خطأ عام في تسجيل الدخول: $e');
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Register new user
  Future<bool> register(RegisterRequest request) async {
    isLoading.value = true;
    error.value = '';

    try {
      final authResponse = await _authService.register(request);

      if (authResponse.success && authResponse.user != null) {
        currentUser.value = authResponse.user;

        // حفظ رمز المصادقة إذا كان متوفراً
        if (authResponse.token.isNotEmpty) {
          final storageService = Get.find<StorageService>();
          await storageService.setString('auth_token', authResponse.token);
          debugPrint('تم حفظ رمز المصادقة بعد التسجيل');
        }

        update();
        return true;
      } else {
        error.value = authResponse.message.isNotEmpty
            ? authResponse.message
            : 'فشل في التسجيل';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Logout user
  Future<bool> logout() async {
    isLoading.value = true;

    try {
      await _authService.logout();

      // مسح البيانات المحلية
      currentUser.value = null;
      final storageService = Get.find<StorageService>();
      await storageService.remove('auth_token');

      update();
      return true;
    } catch (e) {
      error.value = e.toString();
      debugPrint('خطأ في تسجيل الخروج: $e');
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Clear error
  void clearError() {
    error.value = '';
    update();
  }

  // Set error message
  void setError(String errorMessage) {
    error.value = errorMessage;
    update();
  }

  // Update user profile
  Future<bool> updateUserProfile(Map<String, dynamic> userData) async {
    isLoading.value = true;
    error.value = '';

    try {
      // تحديث الملف الشخصي عبر API (سيتم تنفيذه لاحقاً)
      await loadCurrentUser(); // إعادة تحميل بيانات المستخدم
      final user = currentUser.value;
      if (user != null) {
        currentUser.value = user;
        update();
        return true;
      } else {
        error.value = 'فشل تحديث الملف الشخصي';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Change password
  Future<bool> changePassword(
      String currentPassword, String newPassword) async {
    isLoading.value = true;
    error.value = '';

    try {
      final changePasswordRequest = ChangePasswordRequest(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: newPassword,
      );

      await _authService.changePassword(changePasswordRequest);
      return true;
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Validate token
  Future<bool> validateToken() async {
    try {
      final storageService = Get.find<StorageService>();
      final token = await storageService.getString('auth_token');

      if (token == null || token.isEmpty) {
        return false;
      }

      final isValid = await _authService.validateToken(token);
      if (isValid) {
        // إذا كان الرمز صالحاً، نحمل بيانات المستخدم
        await loadCurrentUser();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الرمز: $e');
      return false;
    }
  }

  /// طلب إعادة تعيين كلمة المرور
  Future<bool> forgotPassword(String email) async {
    isLoading.value = true;
    error.value = '';

    try {
      await _authService.forgotPassword(email);
      return true;
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<bool> resetPassword(
      String token, String email, String newPassword) async {
    isLoading.value = true;
    error.value = '';

    try {
      final resetRequest = ResetPasswordRequest(
        token: token,
        email: email,
        newPassword: newPassword,
        confirmPassword: newPassword,
      );
      await _authService.resetPassword(resetRequest);
      return true;
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}
