// ignore_for_file: unnecessary_type_check

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'user_model.dart';
import 'task_models.dart';
import '../professional_reports/report_helpers.dart';

/// نموذج شامل لتقرير المهمة الواحدة
/// يحتوي على جميع البيانات اللازمة لتوليد التقرير
class TaskReportModel {
  final Task task; // المهمة الأساسية
  final List<TaskContributor> contributors; // المساهمون
  final List<TaskTransfer> transfers; // التحويلات
  final TaskReportStatistics statistics; // الإحصائيات

  // الأقسام الجديدة: متتبعات التقدم وسجلات الوقت
  final List<TaskProgressTracker> progressTrackers; // متتبعات التقدم
  final List<TimeTrackingEntry> timeTrackingEntries; // سجلات الوقت

  const TaskReportModel({
    required this.task,
    required this.contributors,
    required this.transfers,
    required this.statistics,
    this.progressTrackers = const [],
    this.timeTrackingEntries = const [],
  });

  /// إنشاء نموذج التقرير من المهمة الأساسية
  /// يستخدم الفئة المساعدة الموحدة لاستخراج المساهمين من جدول task_access_users
  /// إنشاء نموذج التقرير من المهمة مع معالجة آمنة للأخطاء
  factory TaskReportModel.fromTask(Task task) {
    try {
      final taskJson = task.toJson();
      
      // استخراج المساهمين باستخدام الفئة المساعدة الموحدة مع معالجة الأخطاء
      // استخدام البيانات الحقيقية فقط، بدون الطريقة البديلة
      List<TaskContributor> contributors;
      try {
        // محاولة استخراج المساهمين من accessUsers
        contributors = ReportHelpers.extractContributorsFromAccessUsers(task, taskJson);
        print('✅ تم استخراج ${contributors.length} مساهم من البيانات الحقيقية');
        
        // لا نضيف أي بيانات افتراضية - نستخدم البيانات الحقيقية فقط
        if (contributors.isEmpty) {
          print('ℹ️ لا توجد مساهمين في البيانات الحقيقية - لن نضيف بيانات افتراضية');
        }
      } catch (e) {
        print('⚠️ خطأ في استخراج المساهمين، استخدام قائمة فارغة: $e');
        contributors = [];
        
        // إضافة المنشئ والمكلف كمساهمين افتراضيين في حالة الخطأ
        if (task.creator != null) {
          contributors.add(TaskContributor(
            userId: task.creator!.id,
            userName: task.creator!.name,
            userEmail: task.creator!.email,
            role: 'المنشئ',
            commentsCount: 0,
            attachmentsCount: 0,
            activitiesCount: 0,
            totalContributions: 1,
            level: ContributorLevel.basic,
          ));
        }
        
        if (task.assignee != null && (task.creator == null || task.assignee!.id != task.creator!.id)) {
          contributors.add(TaskContributor(
            userId: task.assignee!.id,
            userName: task.assignee!.name,
            userEmail: task.assignee!.email,
            role: 'المكلف',
            commentsCount: 0,
            attachmentsCount: 0,
            activitiesCount: 0,
            totalContributions: 1,
            level: ContributorLevel.basic,
          ));
        }
        
        print('✅ تم إضافة ${contributors.length} مساهم بالطريقة البديلة بعد الخطأ');
      }
      
      // استخراج التحويلات مع معالجة الأخطاء
      List<TaskTransfer> transfers;
      try {
        transfers = _extractTransfers(task, taskJson);
      } catch (e) {
        print('⚠️ خطأ في استخراج التحويلات، استخدام قائمة فارغة: $e');
        transfers = [];
      }
      
      // حساب الإحصائيات مع معالجة الأخطاء
      TaskReportStatistics statistics;
      try {
        statistics = TaskReportStatistics.calculate(task, contributors, transfers);
      } catch (e) {
        print('⚠️ خطأ في حساب الإحصائيات، استخدام إحصائيات افتراضية: $e');
        statistics = TaskReportStatistics.empty();
      }
      
      // استخراج متتبعات التقدم (حاليًا فارغة)
      final progressTrackers = <TaskProgressTracker>[];
      
      // استخراج سجلات الوقت (حاليًا فارغة)
      final timeTrackingEntries = <TimeTrackingEntry>[];
      
      return TaskReportModel(
        task: task,
        contributors: contributors,
        transfers: transfers,
        statistics: statistics,
        progressTrackers: progressTrackers,
        timeTrackingEntries: timeTrackingEntries,
      );
      
    } catch (e) {
      print('❌ خطأ عام في إنشاء TaskReportModel.fromTask: $e');
      // إنشاء نموذج أساسي جداً في حالة الفشل الكامل
      return TaskReportModel(
        task: task,
        contributors: [],
        transfers: [],
        statistics: TaskReportStatistics.empty(),
        progressTrackers: [],
        timeTrackingEntries: [],
      );
    }
  }

  /// إنشاء نموذج التقرير من بيانات JSON (reportData)
  factory TaskReportModel.fromJson(Map<String, dynamic> json) {
    // المهمة الأساسية
    final task = json.containsKey('task') && json['task'] != null
        ? Task.fromJson(json['task'] as Map<String, dynamic>)
        : Task.fromJson(json); // fallback إذا لم يوجد حقل task

    // المساهمون - استخدام الفئة المساعدة الموحدة
    List<TaskContributor> contributors;
    if (json.containsKey('contributors') && json['contributors'] is List) {
      // إذا كانت البيانات تحتوي على مساهمين جاهزين
      contributors = (json['contributors'] as List).map((e) {
        if (e is Map<String, dynamic>) {
          if (e.containsKey('totalContributions') || e.containsKey('commentsCount')) {
            return TaskContributor.fromReportData(e);
          } else {
            return TaskContributor.fromAccessUser(e, task);
          }
        }
        return null;
      }).whereType<TaskContributor>().toList();
    } else {
      // استخدام الفئة المساعدة لاستخراج المساهمين من accessUsers
      contributors = ReportHelpers.extractContributorsFromAccessUsers(task, json);
    }

    // التحويلات
    final transfers = (json['transfers'] as List?)?.map((e) =>
      e is Map<String, dynamic> ? TaskTransfer.fromGenericData(e) : null
    ).whereType<TaskTransfer>().toList() ?? [];

    // الإحصائيات
    final statistics = json['statistics'] is Map<String, dynamic>
        ? TaskReportStatistics.fromJson(json['statistics'] as Map<String, dynamic>, task, contributors, transfers)
        : TaskReportStatistics.calculate(task, contributors, transfers);

    // متتبعات التقدم
    final progressTrackers = (json['progressTrackers'] as List?)?.map((e) {
      if (e is Map<String, dynamic>) {
        return TaskProgressTracker(
          id: e['id'] ?? 0,
          taskId: e['taskId'] ?? 0,
          progress: (e['progressPercentage'] ?? 0).toDouble(),
          updatedAt: DateTime.tryParse(e['updatedAt']?.toString() ?? '') ?? DateTime.now(),
          updatedBy: e['updatedBy'] ?? 0,
          notes: e['notes']?.toString() ?? '',
          date: DateTime.tryParse(e['date']?.toString() ?? '') ?? DateTime.now(),
        );
      }
      return null;
    }).whereType<TaskProgressTracker>().toList() ?? [];

    // سجلات الوقت
    final timeTrackingEntries = (json['timeTrackingEntries'] as List?)?.map((e) {
      if (e is Map<String, dynamic>) {
        return TimeTrackingEntry(
          id: e['id'] ?? 0,
          taskId: e['taskId'] ?? 0,
          userId: e['userId'] ?? 0,
          userName: e['userName']?.toString() ?? '',
          startTime: DateTime.tryParse(e['startTime']?.toString() ?? '') ?? DateTime.now(),
          endTime: DateTime.tryParse(e['endTime']?.toString() ?? '') ?? DateTime.now(),
          durationMinutes: e['durationMinutes'] is int ? e['durationMinutes'] : int.tryParse(e['durationMinutes']?.toString() ?? '') ?? 0,
          description: e['description']?.toString() ?? '',
        );
      }
      return null;
    }).whereType<TimeTrackingEntry>().toList() ?? [];

    return TaskReportModel(
      task: task,
      contributors: contributors,
      transfers: transfers,
      statistics: statistics,
      progressTrackers: progressTrackers,
      timeTrackingEntries: timeTrackingEntries,
    );
  }

  // تم نقل منطق استخراج المساهمين إلى ReportHelpers.extractContributorsFromAccessUsers
  // لتجنب تكرار الكود وتوحيد المنطق

  /// استخراج التحويلات من البيانات المتاحة
  static List<TaskTransfer> _extractTransfers(Task task, Map<String, dynamic> taskJson) {
    try {
      final transfers = <TaskTransfer>[];
      final seenTransfers = <String>{};
  
      // 1. استخراج من taskHistories (جدول task_history)
      if (taskJson.containsKey('taskHistories') && taskJson['taskHistories'] is List) {
        final histories = taskJson['taskHistories'] as List;
        
        for (var historyJson in histories) {
          if (historyJson is Map<String, dynamic>) {
            try {
              final transfer = TaskTransfer.fromHistory(historyJson);
              if (transfer != null) {
                final transferKey = transfer.uniqueKey;
                if (!seenTransfers.contains(transferKey)) {
                  seenTransfers.add(transferKey);
                  transfers.add(transfer);
                }
              }
            } catch (e) {
              print('⚠️ خطأ في استخراج تحويل من سجل التاريخ: $e');
            }
          }
        }
      }
  
      // 2. استخراج من حقول أخرى
      final transferFields = ['transfers', 'taskTransfers', 'assignments', 'reassignments'];
      for (String field in transferFields) {
        if (taskJson.containsKey(field) && taskJson[field] is List) {
          final fieldData = taskJson[field] as List;
          for (var item in fieldData) {
            if (item is Map<String, dynamic>) {
              try {
                final transfer = TaskTransfer.fromGenericData(item);
                if (transfer != null) {
                  final transferKey = transfer.uniqueKey;
                  if (!seenTransfers.contains(transferKey)) {
                    seenTransfers.add(transferKey);
                    transfers.add(transfer);
                  }
                }
              } catch (e) {
                print('⚠️ خطأ في استخراج تحويل من بيانات عامة: $e');
              }
            }
          }
        }
      }
  
      // لا نضيف أي تحويلات افتراضية - نستخدم البيانات الحقيقية فقط
      if (transfers.isEmpty) {
        if (kDebugMode) {
          print('ℹ️ لا توجد تحويلات في البيانات الحقيقية - لن نضيف بيانات افتراضية');
        }
      }
  
      // ترتيب التحويلات حسب التاريخ (الأقدم أولاً)
      try {
        transfers.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في ترتيب التحويلات: $e');
        }
      }
  
      return transfers;
    } catch (e) {
      print('❌ خطأ في استخراج التحويلات: $e');
      if (kDebugMode) {
        print('❌ تفاصيل الخطأ: ${e.toString()}');
      }
      
      // إرجاع قائمة فارغة في حالة الخطأ
      return [];
    }
  }

  /// قائمة المساهمين المستخرجة مباشرة من accessUsers (بنفس طريقة عرض التعليقات والمرفقات والمهام الفرعية)
  /// تعيد قائمة من TaskContributor مأخوذة من task.accessUsers أو من toJson()['accessUsers']، وتدعم null-safety
  List<TaskContributor> get accessUsersList {
    final taskJson = task.toJson();
    final List<TaskContributor> result = [];
    if (taskJson.containsKey('accessUsers') && taskJson['accessUsers'] is List) {
      final accessUsers = taskJson['accessUsers'] as List;
      for (var userJson in accessUsers) {
        if (userJson is Map<String, dynamic>) {
          final contributor = TaskContributor.fromAccessUser(userJson, task);
          result.add(contributor);
        }
      }
    }
    return result;
  }
}

/// نموذج المساهم في المهمة
class TaskContributor {
  final int userId;
  final String userName;
  final String userEmail;
  final String role; // المنشئ، المكلف، معلق، رافع ملفات
  final int commentsCount; // عدد التعليقات
  final int attachmentsCount; // عدد المرفقات
  final int activitiesCount; // عدد الأنشطة
  final int totalContributions; // إجمالي المساهمات
  final ContributorLevel level; // مستوى النشاط
  double _percentage = 0.0; // النسبة المئوية

  TaskContributor({
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.role,
    required this.commentsCount,
    required this.attachmentsCount,
    required this.activitiesCount,
    required this.totalContributions,
    required this.level,
    double percentage = 0.0,
  }) {
    _percentage = percentage;
  }

  /// النسبة المئوية للمساهمة
  double get percentage => _percentage;

  /// تعيين النسبة المئوية مباشرة
  void setPercentage(double value) {
    _percentage = value;
  }


  /// إنشاء مساهم من بيانات المستخدم المرتبط (accessUsers)
  factory TaskContributor.fromAccessUser(Map<String, dynamic> userJson, Task task) {
    try {
      // استخراج معرف المستخدم مع معالجة محسنة للقيم null
      int userId;
      if (userJson['id'] is int) {
        userId = userJson['id'];
      } else if (userJson['id'] != null) {
        userId = int.tryParse(userJson['id'].toString()) ?? 0;
      } else {
        userId = 0;
      }
      
      final userName = userJson['name']?.toString() ?? 'مستخدم غير معروف';
      final userEmail = userJson['email']?.toString() ?? '';
      
      // حساب المساهمات بمعالجة آمنة للقيم null
      int commentsCount = 0;
      int attachmentsCount = 0;
      
      try {
        commentsCount = task.comments.where((c) {
          if (c.user == null) return false;
          return c.user!.id == userId;
        }).length;
      } catch (e) {
        print('⚠️ خطأ في حساب عدد التعليقات: $e');
      }
      
      try {
        attachmentsCount = task.attachments.where((a) {
          if (a.uploadedByUser == null) return false;
          return a.uploadedByUser!.id == userId;
        }).length;
      } catch (e) {
        print('⚠️ خطأ في حساب عدد المرفقات: $e');
      }
      
      int activitiesCount = 0; // سيتم حسابها من taskHistories إذا كانت متاحة
      
      // تحديد الدور
      String role = userJson['role']?.toString() ?? 'مساهم';
      int roleBonus = 0;
      if (userId == task.creatorId) {
        role = 'المنشئ';
        roleBonus = 2;
      } else if (userId == task.assigneeId) {
        role = 'المكلف';
        roleBonus = 1;
      }
      
      final totalContributions = commentsCount + attachmentsCount + activitiesCount + roleBonus;
      final level = ContributorLevel.fromContributions(totalContributions);
      
      return TaskContributor(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        role: role,
        commentsCount: commentsCount,
        attachmentsCount: attachmentsCount,
        activitiesCount: activitiesCount,
        totalContributions: totalContributions,
        level: level,
      );
    } catch (e) {
      print('❌ خطأ في إنشاء مساهم من بيانات المستخدم المرتبط: $e');
      
      // إرجاع مساهم افتراضي في حالة الخطأ
      return TaskContributor(
        userId: 0,
        userName: 'مستخدم غير معروف',
        userEmail: '',
        role: 'مساهم',
        commentsCount: 0,
        attachmentsCount: 0,
        activitiesCount: 0,
        totalContributions: 0,
        level: ContributorLevel.basic,
      );
    }
  }

  /// إنشاء مساهم من البيانات التجريبية المباشرة
  factory TaskContributor.fromReportData(Map<String, dynamic> contributorJson) {
    // معالجة آمنة للقيم null
    int userId;
    if (contributorJson['userId'] is int) {
      userId = contributorJson['userId'];
    } else if (contributorJson['id'] is int) {
      userId = contributorJson['id'];
    } else if (contributorJson['userId'] != null) {
      userId = int.tryParse(contributorJson['userId'].toString()) ?? 0;
    } else if (contributorJson['id'] != null) {
      userId = int.tryParse(contributorJson['id'].toString()) ?? 0;
    } else {
      userId = 0;
    }
    
    final userName = contributorJson['userName']?.toString() ?? 'مستخدم غير معروف';
    final userEmail = contributorJson['userEmail']?.toString() ?? '';
    final role = contributorJson['role']?.toString() ?? 'مساهم';
    
    int commentsCount = 0;
    if (contributorJson['commentsCount'] is int) {
      commentsCount = contributorJson['commentsCount'];
    } else if (contributorJson['commentsCount'] != null) {
      commentsCount = int.tryParse(contributorJson['commentsCount'].toString()) ?? 0;
    }
    
    int attachmentsCount = 0;
    if (contributorJson['attachmentsCount'] is int) {
      attachmentsCount = contributorJson['attachmentsCount'];
    } else if (contributorJson['attachmentsCount'] != null) {
      attachmentsCount = int.tryParse(contributorJson['attachmentsCount'].toString()) ?? 0;
    }
    
    int totalContributions;
    if (contributorJson['totalContributions'] is int) {
      totalContributions = contributorJson['totalContributions'];
    } else if (contributorJson['totalContributions'] != null) {
      totalContributions = int.tryParse(contributorJson['totalContributions'].toString()) ?? (commentsCount + attachmentsCount);
    } else {
      totalContributions = commentsCount + attachmentsCount;
    }
    
    double percentage = 0.0;
    if (contributorJson['percentage'] is double) {
      percentage = contributorJson['percentage'];
    } else if (contributorJson['percentage'] is int) {
      percentage = (contributorJson['percentage'] as int).toDouble();
    } else if (contributorJson['percentage'] != null) {
      percentage = double.tryParse(contributorJson['percentage'].toString()) ?? 0.0;
    }
    
    final contributor = TaskContributor(
      userId: userId,
      userName: userName,
      userEmail: userEmail,
      role: role,
      commentsCount: commentsCount,
      attachmentsCount: attachmentsCount,
      activitiesCount: 0,
      totalContributions: totalContributions,
      level: ContributorLevel.fromContributions(totalContributions),
    );
    
    // تعيين النسبة المئوية إذا كانت متوفرة
    if (percentage > 0) {
      contributor.setPercentage(percentage);
    }
    
    return contributor;
  }

  /// إنشاء مساهم من المنشئ - تم إيقافها لعدم استخدام بيانات افتراضية
  @Deprecated('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط')
  factory TaskContributor.fromCreator(User creator, Task task) {
    // معالجة آمنة للقيم null
    int commentsCount = 0;
    int attachmentsCount = 0;
    
    try {
      commentsCount = task.comments.where((c) {
        if (c.user == null) return false;
        return c.user!.id == creator.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد تعليقات المنشئ: $e');
    }
    
    try {
      attachmentsCount = task.attachments.where((a) {
        if (a.uploadedByUser == null) return false;
        return a.uploadedByUser!.id == creator.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد مرفقات المنشئ: $e');
    }
    
    final totalContributions = commentsCount + attachmentsCount + 2; // نقاط إضافية للمنشئ
    
    return TaskContributor(
      userId: creator.id,
      userName: creator.name,
      userEmail: creator.email,
      role: 'المنشئ',
      commentsCount: commentsCount,
      attachmentsCount: attachmentsCount,
      activitiesCount: 0,
      totalContributions: totalContributions,
      level: ContributorLevel.fromContributions(totalContributions),
    );
  }

  /// إنشاء مساهم من المكلف - تم إيقافها لعدم استخدام بيانات افتراضية
  @Deprecated('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط')
  factory TaskContributor.fromAssignee(User assignee, Task task) {
    // معالجة آمنة للقيم null
    int commentsCount = 0;
    int attachmentsCount = 0;
    
    try {
      commentsCount = task.comments.where((c) {
        if (c.user == null) return false;
        return c.user!.id == assignee.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد تعليقات المكلف: $e');
    }
    
    try {
      attachmentsCount = task.attachments.where((a) {
        if (a.uploadedByUser == null) return false;
        return a.uploadedByUser!.id == assignee.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد مرفقات المكلف: $e');
    }
    
    final totalContributions = commentsCount + attachmentsCount + 1; // نقطة إضافية للمكلف
    
    return TaskContributor(
      userId: assignee.id,
      userName: assignee.name,
      userEmail: assignee.email,
      role: 'المكلف',
      commentsCount: commentsCount,
      attachmentsCount: attachmentsCount,
      activitiesCount: 0,
      totalContributions: totalContributions,
      level: ContributorLevel.fromContributions(totalContributions),
    );
  }

  /// إنشاء مساهم من المعلق - تم إيقافها لعدم استخدام بيانات افتراضية
  @Deprecated('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط')
  factory TaskContributor.fromCommenter(User commenter, Task task) {
    // معالجة آمنة للقيم null
    int commentsCount = 0;
    
    try {
      commentsCount = task.comments.where((c) {
        if (c.user == null) return false;
        return c.user!.id == commenter.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد تعليقات المعلق: $e');
    }
    
    return TaskContributor(
      userId: commenter.id,
      userName: commenter.name,
      userEmail: commenter.email,
      role: 'معلق',
      commentsCount: commentsCount,
      attachmentsCount: 0,
      activitiesCount: 0,
      totalContributions: commentsCount,
      level: ContributorLevel.fromContributions(commentsCount),
    );
  }

  /// إنشاء مساهم من رافع الملفات - تم إيقافها لعدم استخدام بيانات افتراضية
  @Deprecated('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط')
  factory TaskContributor.fromUploader(User uploader, Task task) {
    // معالجة آمنة للقيم null
    int attachmentsCount = 0;
    
    try {
      attachmentsCount = task.attachments.where((a) {
        if (a.uploadedByUser == null) return false;
        return a.uploadedByUser!.id == uploader.id;
      }).length;
    } catch (e) {
      print('⚠️ خطأ في حساب عدد مرفقات رافع الملفات: $e');
    }
    
    return TaskContributor(
      userId: uploader.id,
      userName: uploader.name,
      userEmail: uploader.email,
      role: 'رافع ملفات',
      commentsCount: 0,
      attachmentsCount: attachmentsCount,
      activitiesCount: 0,
      totalContributions: attachmentsCount,
      level: ContributorLevel.fromContributions(attachmentsCount),
    );
  }
}

/// مستويات نشاط المساهمين
enum ContributorLevel {
  basic('مساهم أساسي'),
  medium('مساهم متوسط'),
  active('مساهم نشط'),
  veryActive('مساهم نشط جداً');

  const ContributorLevel(this.displayName);
  final String displayName;

  /// تحديد المستوى بناءً على عدد المساهمات
  static ContributorLevel fromContributions(int contributions) {
    if (contributions >= 10) return ContributorLevel.veryActive;
    if (contributions >= 5) return ContributorLevel.active;
    if (contributions >= 2) return ContributorLevel.medium;
    return ContributorLevel.basic;
  }
}

/// نموذج تحويل المهمة
class TaskTransfer {
  final String fromUser; // المحول من
  final String toUser; // المحول إلى
  final int timestamp; // تاريخ التحويل (Unix timestamp)
  final String reason; // سبب التحويل
  final String executor; // منفذ التحويل
  final TransferType type; // نوع التحويل

  const TaskTransfer({
    required this.fromUser,
    required this.toUser,
    required this.timestamp,
    required this.reason,
    required this.executor,
    required this.type,
  });

  /// مفتاح فريد للتحويل لتجنب التكرار
  String get uniqueKey => '${fromUser}_${toUser}_$timestamp';

  /// إنشاء تحويل من بيانات التاريخ (taskHistories)
  static TaskTransfer? fromHistory(Map<String, dynamic> historyJson) {
    try {
      final action = (historyJson['action'] ?? '').toString().toLowerCase();
      
      // التحقق من أن الإجراء متعلق بالتحويل - تحسين الشرط ليشمل المزيد من الإجراءات
      if (!_isTransferAction(action) && 
          !action.contains('تحويل') && 
          !action.contains('تكليف') && 
          !action.contains('إسناد') && 
          action != 'created' && 
          action != 'transferred') {
        return null;
      }
      
      // استخراج المستخدمين من oldValue و newValue
      String fromUser = '';
      String toUser = '';
      
      // محاولة استخراج البيانات من oldValue و newValue
      if (historyJson.containsKey('oldValue') && historyJson.containsKey('newValue')) {
        // استخدام القيم كما هي
        fromUser = historyJson['oldValue']?.toString() ?? '';
        toUser = historyJson['newValue']?.toString() ?? '';
        
        // التحقق مما إذا كانت القيم عبارة عن JSON strings
        if (fromUser.startsWith('{') && toUser.startsWith('{')) {
          try {
            // محاولة تحليل JSON
            Map<String, dynamic> oldValueJson = jsonDecode(fromUser);
            Map<String, dynamic> newValueJson = jsonDecode(toUser);
            
            // استخراج معرفات المستخدمين
            final previousAssigneeId = oldValueJson['previousAssigneeId'] ?? oldValueJson['assigneeId'] ?? '';
            final newAssigneeId = newValueJson['newAssigneeId'] ?? newValueJson['assigneeId'] ?? '';
            
            // استخدام معرفات المستخدمين كأسماء مؤقتة
            fromUser = previousAssigneeId.toString();
            toUser = newAssigneeId.toString();
          } catch (jsonError) {
            print('⚠️ خطأ في تحليل JSON من oldValue/newValue: $jsonError');
            // استخدام القيم كما هي - لا تغيير
          }
        }
      } else if (historyJson.containsKey('details') && historyJson['details'] != null) {
        final details = historyJson['details']?.toString() ?? '';
        
        // محاولة استخراج المستخدمين من حقل details
        if (details.contains('->')) {
          final parts = details.split('->');
          if (parts.length == 2) {
            fromUser = parts[0].trim();
            toUser = parts[1].trim();
          }
        } else if (details.startsWith('{')) {
          try {
            // محاولة تحليل JSON
            Map<String, dynamic> detailsJson = jsonDecode(details);
            
            // استخراج معرفات المستخدمين
            final previousAssigneeId = detailsJson['previousAssigneeId']?.toString() ?? '';
            final newAssigneeId = detailsJson['newAssigneeId']?.toString() ?? '';
            
            if (previousAssigneeId.isNotEmpty && newAssigneeId.isNotEmpty) {
              fromUser = previousAssigneeId;
              toUser = newAssigneeId;
            }
          } catch (jsonError) {
            print('⚠️ خطأ في تحليل JSON من details: $jsonError');
          }
        }
      }
      
      // التحقق من صحة البيانات
      if (fromUser.isEmpty || toUser.isEmpty) {
        print('⚠️ لم يتم استخراج المستخدمين من بيانات التاريخ');
        return null;
      }
      
      // معالجة محسنة للتاريخ
      int timestamp = 0;
      try {
        if (historyJson['timestamp'] is int) {
          timestamp = historyJson['timestamp'];
        } else if (historyJson['changedAt'] is int) {
          timestamp = historyJson['changedAt'];
        } else if (historyJson['timestamp'] != null) {
          timestamp = int.tryParse(historyJson['timestamp'].toString()) ?? 0;
        } else if (historyJson['changedAt'] != null) {
          timestamp = int.tryParse(historyJson['changedAt'].toString()) ?? 0;
        }
      } catch (e) {
        print('⚠️ خطأ في استخراج التاريخ: $e');
      }
      
      // استخراج سبب التحويل
      String reason = 'تحويل مهمة';
      try {
        if (historyJson['details'] != null) {
          final details = historyJson['details'].toString();
          
          if (details.contains('note') && details.startsWith('{')) {
            try {
              Map<String, dynamic> detailsJson = jsonDecode(details);
              reason = detailsJson['note']?.toString() ?? 'تحويل مهمة';
            } catch (jsonError) {
              reason = historyJson['actionDescription']?.toString() ?? historyJson['details']?.toString() ?? 'تحويل مهمة';
            }
          } else {
            reason = historyJson['actionDescription']?.toString() ?? historyJson['details']?.toString() ?? 'تحويل مهمة';
          }
        } else {
          reason = historyJson['actionDescription']?.toString() ?? 'تحويل مهمة';
        }
      } catch (e) {
        print('⚠️ خطأ في استخراج سبب التحويل: $e');
      }
      
      // استخراج منفذ التحويل
      String executor = 'النظام';
      try {
        executor = _extractExecutor(historyJson);
      } catch (e) {
        print('⚠️ خطأ في استخراج منفذ التحويل: $e');
      }
      
      // تحديد نوع التحويل
      TransferType type = TransferType.manual;
      try {
        type = TransferType.fromAction(action);
      } catch (e) {
        print('⚠️ خطأ في تحديد نوع التحويل: $e');
      }
      
      return TaskTransfer(
        fromUser: fromUser,
        toUser: toUser,
        timestamp: timestamp,
        reason: reason,
        executor: executor,
        type: type,
      );
    } catch (e) {
      print('❌ خطأ في إنشاء تحويل من بيانات التاريخ: $e');
      print('❌ تفاصيل الخطأ: ${e.toString()}');
      
      // إرجاع null في حالة الخطأ
      return null;
    }
  }

  /// إنشاء تحويل من بيانات عامة
  static TaskTransfer? fromGenericData(Map<String, dynamic> data) {
    try {
      // محاولة استخراج المستخدمين من البيانات
      String fromUser = '';
      String toUser = '';
      
      // التحقق مما إذا كانت البيانات تحتوي على معرفات المستخدمين
      if (data.containsKey('previousAssigneeId') && data.containsKey('newAssigneeId')) {
        fromUser = data['previousAssigneeId'].toString();
        toUser = data['newAssigneeId'].toString();
      } else if (data.containsKey('fromUser') && data.containsKey('toUser')) {
        fromUser = data['fromUser'].toString();
        toUser = data['toUser'].toString();
      } else {
        // محاولة استخراج المستخدمين من حقول أخرى
        fromUser = (data['fromUser'] ?? data['fromUserName'] ?? data['from'] ?? data['oldValue'] ?? '').toString();
        toUser = (data['toUser'] ?? data['toUserName'] ?? data['to'] ?? data['newValue'] ?? '').toString();
      }
      
      // التحقق من صحة البيانات
      if (fromUser.isEmpty || toUser.isEmpty || fromUser == toUser) {
        // محاولة استخراج البيانات من details إذا كان موجودًا وكان JSON string
        if (data.containsKey('details') && data['details'].toString().startsWith('{')) {
          try {
            Map<String, dynamic> detailsJson = jsonDecode(data['details'].toString());
            fromUser = detailsJson['previousAssigneeId']?.toString() ?? '';
            toUser = detailsJson['newAssigneeId']?.toString() ?? '';
          } catch (jsonError) {
            print('⚠️ خطأ في تحليل JSON من details: $jsonError');
          }
        }
      }
      
      // التحقق مرة أخرى من صحة البيانات
      if (fromUser.isEmpty || toUser.isEmpty || fromUser == toUser) return null;
      
      // معالجة محسنة للتاريخ
      int timestamp;
      if (data['timestamp'] is int) {
        timestamp = data['timestamp'];
      } else if (data['date'] is int) {
        timestamp = data['date'];
      } else if (data['transferDate'] is int) {
        timestamp = data['transferDate'];
      } else if (data['timestamp'] != null) {
        timestamp = int.tryParse(data['timestamp'].toString()) ?? 0;
      } else if (data['date'] != null) {
        timestamp = int.tryParse(data['date'].toString()) ?? 0;
      } else if (data['transferDate'] != null) {
        timestamp = int.tryParse(data['transferDate'].toString()) ?? 0;
      } else {
        timestamp = 0;
      }
      
      // استخراج سبب التحويل
      String reason;
      if (data.containsKey('note')) {
        reason = data['note'].toString();
      } else if (data.containsKey('details') && data['details'].toString().startsWith('{')) {
        try {
          Map<String, dynamic> detailsJson = jsonDecode(data['details'].toString());
          reason = detailsJson['note']?.toString() ?? 'تحويل مهمة';
        } catch (jsonError) {
          reason = (data['reason'] ?? data['description'] ?? data['notes'] ?? 'تحويل مهمة').toString();
        }
      } else {
        reason = (data['reason'] ?? data['description'] ?? data['notes'] ?? 'تحويل مهمة').toString();
      }
      
      final executor = (data['executor'] ?? data['changedBy'] ?? data['user'] ?? 'النظام').toString();
      
      return TaskTransfer(
        fromUser: fromUser,
        toUser: toUser,
        timestamp: timestamp,
        reason: reason,
        executor: executor,
        type: TransferType.manual,
      );
    } catch (e) {
      print('❌ خطأ في إنشاء تحويل من بيانات عامة: $e');
      print('❌ تفاصيل الخطأ: ${e.toString()}');
      print('❌ بيانات التحويل: ${data.toString()}');
      return null;
    }
  }

  /// إنشاء التحويل الأولي - تم إيقافها لعدم استخدام بيانات افتراضية
  @Deprecated('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط')
  static TaskTransfer initial(Task task) {
    // لا نرجع أي بيانات افتراضية
    throw UnsupportedError('لا نستخدم بيانات افتراضية - استخدم البيانات الحقيقية فقط');
  }

  /// التحقق من أن الإجراء متعلق بالتحويل
  static bool _isTransferAction(String action) {
    final actionLower = action.toLowerCase();
    return actionLower.contains('assign') || 
           actionLower.contains('transfer') || 
           actionLower.contains('reassign') || 
           actionLower.contains('تحويل') || 
           actionLower.contains('تكليف') || 
           actionLower.contains('إسناد') || 
           actionLower == 'assigned' || 
           actionLower == 'created' || 
           actionLower == 'إنشاء';
  }

  /// استخراج منفذ التحويل مع معالجة آمنة للقيم null
  static String _extractExecutor(Map<String, dynamic> historyJson) {
    try {
      // محاولة استخراج المنفذ من changedByNavigation
      if (historyJson.containsKey('changedByNavigation') && historyJson['changedByNavigation'] != null) {
        final changedBy = historyJson['changedByNavigation'];
        if (changedBy is Map) {
          final name = changedBy['name'];
          if (name != null) {
            return name.toString();
          }
        }
      }
      
      // محاولة استخراج المنفذ من user
      if (historyJson.containsKey('user') && historyJson['user'] != null) {
        final user = historyJson['user'];
        if (user is Map) {
          final name = user['name'];
          if (name != null) {
            return name.toString();
          }
        }
      }
      
      // محاولة استخراج المنفذ من changedBy
      if (historyJson.containsKey('changedBy') && historyJson['changedBy'] != null) {
        return historyJson['changedBy'].toString();
      }
      
      // محاولة استخراج المنفذ من executor
      if (historyJson.containsKey('executor') && historyJson['executor'] != null) {
        return historyJson['executor'].toString();
      }
      
      // إرجاع قيمة افتراضية
      return 'النظام';
    } catch (e) {
      print('⚠️ خطأ في استخراج منفذ التحويل: $e');
      return 'النظام';
    }
  }
}

/// أنواع التحويلات
enum TransferType {
  initial('تكليف أولي'),
  manual('تحويل يدوي'),
  automatic('تحويل تلقائي'),
  reassignment('إعادة تكليف');

  const TransferType(this.displayName);
  final String displayName;

  /// تحديد نوع التحويل من الإجراء
  static TransferType fromAction(String action) {
    if (action == 'created') return TransferType.initial;
    if (action.contains('reassign')) return TransferType.reassignment;
    if (action.contains('auto')) return TransferType.automatic;
    return TransferType.manual;
  }
}

/// إحصائيات تقرير المهمة
class TaskReportStatistics {
  final int totalContributors; // إجمالي المساهمين
  final int totalTransfers; // إجمالي التحويلات
  final int totalComments; // إجمالي التعليقات
  final int totalAttachments; // إجمالي المرفقات
  final int totalSubtasks; // إجمالي المهام الفرعية
  final int completedSubtasks; // المهام الفرعية المكتملة
  final double completionRate; // معدل الإنجاز
  final int daysActive; // عدد الأيام النشطة
  final String mostActiveContributor; // أكثر المساهمين نشاطاً
  final String latestTransfer; // آخر تحويل
  
  // حقول إضافية للتقرير الشامل
  final int totalProgressUpdates; // إجمالي تحديثات التقدم
  final int totalTimeEntries; // إجمالي سجلات الوقت
  final int totalTimeSpent; // إجمالي الوقت المستغرق (بالدقائق)
  final double averageProgressPerDay; // متوسط التقدم اليومي
  final int taskAge; // عمر المهمة بالأيام

  const TaskReportStatistics({
    required this.totalContributors,
    required this.totalTransfers,
    required this.totalComments,
    required this.totalAttachments,
    required this.totalSubtasks,
    required this.completedSubtasks,
    required this.completionRate,
    required this.daysActive,
    required this.mostActiveContributor,
    required this.latestTransfer,
    this.totalProgressUpdates = 0,
    this.totalTimeEntries = 0,
    this.totalTimeSpent = 0,
    this.averageProgressPerDay = 0.0,
    this.taskAge = 0,
  });

  /// إنشاء TaskReportStatistics من JSON
  /// 
  /// [json] - البيانات من JSON
  /// [task] - المهمة الأساسية
  /// [contributors] - قائمة المساهمين
  /// [transfers] - قائمة التحويلات
  static TaskReportStatistics fromJson(
    Map<String, dynamic> json,
    Task task,
    List<TaskContributor> contributors,
    List<TaskTransfer> transfers,
  ) {
    try {
      return TaskReportStatistics(
        totalContributors: json['totalContributors'] ?? contributors.length,
        totalTransfers: json['totalTransfers'] ?? transfers.length,
        totalComments: json['totalComments'] ?? task.comments.length,
        totalAttachments: json['totalAttachments'] ?? task.attachments.length,
        totalSubtasks: json['totalSubtasks'] ?? task.subtasks.length,
        completedSubtasks: json['completedSubtasks'] ?? task.subtasks.where((s) => s.isCompleted).length,
        completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
        daysActive: json['daysActive'] ?? _calculateDaysActive(task.createdAt),
        mostActiveContributor: json['mostActiveContributor'] ?? _getMostActiveContributor(contributors),
        latestTransfer: json['latestTransfer'] ?? _getLatestTransfer(transfers),
        totalProgressUpdates: json['totalProgressUpdates'] ?? 0,
        totalTimeEntries: json['totalTimeEntries'] ?? 0,
        totalTimeSpent: json['totalTimeSpent'] ?? 0,
        averageProgressPerDay: (json['averageProgressPerDay'] as num?)?.toDouble() ?? 0.0,
        taskAge: json['taskAge'] ?? _calculateDaysActive(task.createdAt),
      );
    } catch (e) {
      print('❌ خطأ في إنشاء إحصائيات التقرير من JSON: $e');
      return empty();
    }
  }
  
  /// إنشاء إحصائيات فارغة
  static TaskReportStatistics empty() {
    return TaskReportStatistics(
      totalContributors: 0,
      totalTransfers: 0,
      totalComments: 0,
      totalAttachments: 0,
      totalSubtasks: 0,
      completedSubtasks: 0,
      completionRate: 0.0,
      daysActive: 0,
      mostActiveContributor: 'لا يوجد',
      latestTransfer: 'لا توجد تحويلات',
      totalProgressUpdates: 0,
      totalTimeEntries: 0,
      totalTimeSpent: 0,
      averageProgressPerDay: 0.0,
      taskAge: 0,
    );
  }

  // تم إزالة دالة createDefault - نستخدم البيانات الحقيقية فقط

  /// حساب الإحصائيات من البيانات
  static TaskReportStatistics calculate(
    Task task, 
    List<TaskContributor> contributors, 
    List<TaskTransfer> transfers,
    [List<TaskProgressTracker>? progressTrackers,
     List<TimeTrackingEntry>? timeEntries]
  ) {
    try {
      final progressList = progressTrackers ?? [];
      final timeList = timeEntries ?? [];
      
      final totalContributors = contributors.length;
      final totalTransfers = transfers.length;
      final totalComments = task.comments.length;
      final totalAttachments = task.attachments.length;
      final totalSubtasks = task.subtasks.length;
      final completedSubtasks = task.subtasks.where((s) => s.isCompleted).length;
      
      // حساب إحصائيات التقدم والوقت
      final totalProgressUpdates = progressList.length;
      final totalTimeEntries = timeList.length;
      final totalTimeSpent = timeList.fold<int>(0, (sum, entry) => sum + entry.durationMinutes);
      
      // حساب متوسط التقدم اليومي
      double averageProgressPerDay = 0.0;
      if (progressList.isNotEmpty) {
        final totalProgress = progressList.fold<double>(0, (sum, tracker) => sum + tracker.progress);
        averageProgressPerDay = totalProgress / progressList.length;
      }
      
      // معالجة محسنة لنسبة الإنجاز مع التحقق من القيمة null
      double completionRate = 0.0;
      try {
        // ignore: unnecessary_cast
          completionRate = task.completionPercentage.toDouble();
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في معالجة نسبة الإنجاز: $e');
        }
        completionRate = 0.0;
      }
      
      // حساب عدد الأيام النشطة مع التحقق من القيمة null
      int daysActive = 0;
      try {
        if (task.createdAt > 0) {
          final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
          daysActive = ((now - task.createdAt) / 86400).ceil();
          if (daysActive < 0) daysActive = 0;
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ خطأ في حساب الأيام النشطة: $e');
        }
        daysActive = 0;
      }
      
      // أكثر المساهمين نشاطاً
      final mostActiveContributor = contributors.isNotEmpty 
          ? contributors.first.userName 
          : 'لا يوجد';
      
      // آخر تحويل
      final latestTransfer = transfers.isNotEmpty 
          ? '${transfers.last.fromUser} إلى ${transfers.last.toUser}'
          : 'لا يوجد';
      
      return TaskReportStatistics(
        totalContributors: totalContributors,
        totalTransfers: totalTransfers,
        totalComments: totalComments,
        totalAttachments: totalAttachments,
        totalSubtasks: totalSubtasks,
        completedSubtasks: completedSubtasks,
        completionRate: completionRate,
        daysActive: daysActive,
        mostActiveContributor: mostActiveContributor,
        latestTransfer: latestTransfer,
        totalProgressUpdates: totalProgressUpdates,
        totalTimeEntries: totalTimeEntries,
        totalTimeSpent: totalTimeSpent,
        averageProgressPerDay: averageProgressPerDay,
        taskAge: daysActive,
      );
    } catch (e) {
      print('❌ خطأ في حساب إحصائيات التقرير: $e');
      print('❌ تفاصيل الخطأ: ${e.toString()}');
      
      // إرجاع إحصائيات افتراضية في حالة الخطأ مع معالجة آمنة للقيم
      try {
        return TaskReportStatistics(
          totalContributors: contributors.length,
          totalTransfers: transfers.length,
          totalComments: 0,
          totalAttachments: 0,
          totalSubtasks: 0,
          completedSubtasks: 0,
          completionRate: 0.0,
          daysActive: 0,
          mostActiveContributor: contributors.isNotEmpty ? contributors.first.userName : 'لا يوجد',
          latestTransfer: transfers.isNotEmpty ? '${transfers.last.fromUser} إلى ${transfers.last.toUser}' : 'لا يوجد',
          totalProgressUpdates: 0,
          totalTimeEntries: 0,
          totalTimeSpent: 0,
          averageProgressPerDay: 0.0,
          taskAge: 0,
        );
      } catch (innerError) {
        print('❌ خطأ في إنشاء الإحصائيات الافتراضية: $innerError');
        
        // إرجاع إحصائيات فارغة في حالة الخطأ
        return TaskReportStatistics.empty();
      }
    }
  }
  
  /// حساب عدد الأيام النشطة مع معالجة آمنة للقيم
  static int _calculateDaysActive(int createdAtTimestamp) {
    try {
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return ((now - createdAtTimestamp) / 86400).ceil();
    } catch (e) {
      print('❌ خطأ في حساب عدد الأيام النشطة: $e');
      return 0;
    }
  }
  
  /// الحصول على أكثر المساهمين نشاطاً
  static String _getMostActiveContributor(List<TaskContributor> contributors) {
    return contributors.isNotEmpty ? contributors.first.userName : 'لا يوجد';
  }
  
  /// الحصول على آخر تحويل
  static String _getLatestTransfer(List<TaskTransfer> transfers) {
    return transfers.isNotEmpty 
        ? '${transfers.last.fromUser} إلى ${transfers.last.toUser}'
        : 'لا يوجد';
  }
}

/// متتبع تقدم المهمة الشامل
class TaskProgressTracker {
  final int id; // معرف المتتبع
  final int taskId; // معرف المهمة
  final double progress; // نسبة التقدم
  final DateTime updatedAt; // تاريخ التحديث
  final int updatedBy; // معرف المحدث
  final String notes; // ملاحظات
  final DateTime date; // تاريخ التقدم

  const TaskProgressTracker({
    required this.id,
    required this.taskId,
    required this.progress,
    required this.updatedAt,
    required this.updatedBy,
    required this.notes,
    required this.date,
  });
  
  /// إنشاء من JSON
  factory TaskProgressTracker.fromJson(Map<String, dynamic> json) {
    return TaskProgressTracker(
      id: json['id'] ?? 0,
      taskId: json['taskId'] ?? 0,
      progress: (json['progressPercentage'] ?? 0).toDouble(),
      updatedAt: DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] ?? 0) * 1000),
      updatedBy: json['updatedBy'] ?? 0,
      notes: json['notes'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] ?? 0) * 1000),
    );
  }
}

/// سجل تتبع الوقت الشامل
class TimeTrackingEntry {
  final int id; // معرف السجل
  final int taskId; // معرف المهمة
  final int userId; // معرف المستخدم
  final String userName; // اسم المستخدم
  final DateTime startTime; // وقت البدء
  final DateTime endTime; // وقت الانتهاء
  final int durationMinutes; // المدة بالدقائق
  final String description; // وصف العمل

  const TimeTrackingEntry({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.userName,
    required this.startTime,
    required this.endTime,
    required this.durationMinutes,
    required this.description,
  });
  
  /// إنشاء من JSON
  factory TimeTrackingEntry.fromJson(Map<String, dynamic> json) {
    return TimeTrackingEntry(
      id: json['id'] ?? 0,
      taskId: json['taskId'] ?? 0,
      userId: json['userId'] ?? 0,
      userName: json['user']?['name'] ?? 'مستخدم غير معروف',
      startTime: DateTime.fromMillisecondsSinceEpoch((json['startTime'] ?? 0) * 1000),
      endTime: DateTime.fromMillisecondsSinceEpoch((json['endTime'] ?? 0) * 1000),
      durationMinutes: json['duration'] ?? 0,
      description: json['description'] ?? '',
    );
  }
}