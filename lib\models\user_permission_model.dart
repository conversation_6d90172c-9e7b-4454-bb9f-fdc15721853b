import 'user_model.dart';
import 'permission_models.dart';

/// نموذج صلاحيات المستخدم (UserPermission) - متوافق مع الباك اند
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final bool isActive;
  final int? expiresAt;
  final bool isDeleted;

  // العلاقات البرمجية
  final User? user;
  final Permission? permission;
  final User? grantedByNavigation;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.isActive = true,
    this.expiresAt,
    this.isDeleted = false,
    this.user,
    this.permission,
    this.grantedByNavigation,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as int,
      userId: json['userId'] as int,
      permissionId: json['permissionId'] as int,
      grantedBy: json['grantedBy'] as int,
      grantedAt: json['grantedAt'] as int,
      isActive: json['isActive'] as bool? ?? true,
      expiresAt: json['expiresAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      permission: json['permission'] != null ? Permission.fromJson(json['permission']) : null,
      grantedByNavigation: json['grantedByNavigation'] != null ? User.fromJson(json['grantedByNavigation']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'permissionId': permissionId,
      'grantedBy': grantedBy,
      'grantedAt': grantedAt,
      'isActive': isActive,
      'expiresAt': expiresAt,
      'isDeleted': isDeleted,
      'user': user?.toJson(),
      'permission': permission?.toJson(),
      'grantedByNavigation': grantedByNavigation?.toJson(),
    };
  }
}
