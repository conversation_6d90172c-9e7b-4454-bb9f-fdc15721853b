
   import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../controllers/text_document_controller.dart';
import '../../models/text_document_model.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';
import '../../services/export_services/enhanced_pdf_export_service.dart';

/// شاشة محرر المستندات النصية
///
/// تستخدم لإنشاء وتحرير المستندات النصية
class TextDocumentEditorScreen extends StatefulWidget {
  /// معرف المستند (للتحرير)
  final String? documentId;

  /// معرف المهمة (للإنشاء)
  final String? taskId;

  /// عنوان المستند الافتراضي (للإنشاء)
  final String? defaultTitle;

  /// نوع المستند الافتراضي (للإنشاء)
  final TextDocumentType? defaultType;

  /// إنشاء شاشة محرر المستندات النصية
  const TextDocumentEditorScreen({
    super.key,
    this.documentId,
    this.taskId,
    this.defaultTitle,
    this.defaultType,
  });

  @override
  State<TextDocumentEditorScreen> createState() =>
      _TextDocumentEditorScreenState();
}

class _TextDocumentEditorScreenState extends State<TextDocumentEditorScreen> {
  final TextDocumentController _documentController =
      Get.find<TextDocumentController>();
  final EnhancedPdfExportService _pdfExportService =
      Get.find<EnhancedPdfExportService>();

  final TextEditingController _titleController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _editorFocusNode = FocusNode();

  TextDocumentType _selectedType = TextDocumentType.note;
  bool _isShared = false;
  bool _isEditing = false;
  bool _isNew = true;

  @override
  void initState() {
    super.initState();
    // تهيئة أولية للمستند الجديد
    if (widget.documentId == null) {
      _isNew = true;
      _isEditing = true;

      // تعيين القيم الافتراضية
      if (widget.defaultTitle != null) {
        _titleController.text = widget.defaultTitle!;
      }

      if (widget.defaultType != null) {
        _selectedType = widget.defaultType!;
      }

      // تهيئة محرر النصوص
      _documentController.setTextController(TextEditingController());
    }

    // تأجيل تحميل المستند حتى اكتمال عملية البناء الأولى
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeDocument();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _titleFocusNode.dispose();
    _editorFocusNode.dispose();
    super.dispose();
  }

  /// تهيئة المستند
  Future<void> _initializeDocument() async {
    if (widget.documentId != null) {
      // تحرير مستند موجود
      setState(() {
        _isNew = false;
      });

      await _documentController.loadDocumentById(widget.documentId!);

      final currentDocument = _documentController.currentDocument;
      if (currentDocument != null) {
        setState(() {
          _titleController.text = currentDocument.title;
          _selectedType = currentDocument.type;
          _isShared = currentDocument.isShared;
        });
      }
    }
  }

  /// حفظ المستند
  Future<void> _saveDocument() async {
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان للمستند',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    if (_isNew) {
      // إنشاء مستند جديد
      await _documentController.createDocument(
        title: _titleController.text.trim(),
        type: _selectedType,
        taskId: widget.taskId,
        isShared: _isShared,
      );
    } else {
      // تحديث مستند موجود
      await _documentController.updateDocument(
        id: widget.documentId!,
        title: _titleController.text.trim(),
        type: _selectedType,
        isShared: _isShared,
      );
    }

    setState(() {
      _isEditing = false;
    });
  }

  /// طباعة المستند
  Future<void> _printDocument() async {
    try {
      // الحصول على نص المحرر
      final plainText = _documentController.textController?.text ?? '';

      // إنشاء ملف PDF
      final pdf = pw.Document();

      // إضافة صفحة
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان
                pw.Text(
                  _titleController.text,
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 10),
                // المحتوى
                pw.Text(plainText),
              ],
            );
          },
        ),
      );

      // طباعة المستند
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: _titleController.text,
      );
    } catch (e) {
      debugPrint('خطأ في طباعة المستند: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء طباعة المستند: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصدير المستند كملف PDF
  Future<void> _exportToPdf() async {
    try {
      // الحصول على نص المحرر
      final plainText = _documentController.textController?.text ?? '';

      // تصدير إلى PDF
      final filePath = await _pdfExportService.exportToPdf(
        title: _titleController.text,
        data: {'content': plainText},
        fileName:
            '${_titleController.text.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );

      if (filePath != null) {
        Get.snackbar(
          'تم التصدير',
          'تم تصدير المستند بنجاح إلى: $filePath',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تصدير المستند',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تصدير المستند: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تصدير المستند: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isNew ? 'إنشاء مستند جديد' : 'تحرير المستند'),
        actions: [
          // زر الطباعة
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة',
            onPressed: _printDocument,
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير كملف PDF',
            onPressed: _exportToPdf,
          ),
          // زر التحرير/الحفظ
          IconButton(
            icon: Icon(_isEditing ? Icons.save : Icons.edit),
            tooltip: _isEditing ? 'حفظ' : 'تحرير',
            onPressed: _isEditing
                ? _saveDocument
                : () {
                    setState(() {
                      _isEditing = true;
                    });
                  },
          ),
        ],
      ),
      body: Obx(() {
        if (_documentController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_documentController.error.isNotEmpty) {
          return Center(
            child: Text(
              _documentController.error,
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // عنوان المستند
              TextField(
                controller: _titleController,
                focusNode: _titleFocusNode,
                decoration: AppStyles.inputDecoration(
                  labelText: 'عنوان المستند',
                  prefixIcon: const Icon(Icons.title),
                ),
                enabled: _isEditing,
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 16),

              // نوع المستند
              DropdownButtonFormField<TextDocumentType>(
                decoration: AppStyles.inputDecoration(
                  labelText: 'نوع المستند',
                  prefixIcon: const Icon(Icons.category),
                ),
                value: _selectedType,
                items: TextDocumentType.values.map((type) {
                  String label;
                  IconData icon;

                  switch (type) {
                    case TextDocumentType.note:
                      label = 'ملاحظة';
                      icon = Icons.note;
                      break;
                    case TextDocumentType.report:
                      label = 'تقرير';
                      icon = Icons.description;
                      break;
                    case TextDocumentType.template:
                      label = 'قالب';
                      icon = Icons.article_outlined;
                      break;
                    case TextDocumentType.letter:
                      label = 'خطاب';
                      icon = Icons.mail_outline;
                      break;
                    case TextDocumentType.memo:
                      label = 'مذكرة';
                      icon = Icons.sticky_note_2_outlined;
                      break;
                    case TextDocumentType.contract:
                      label = 'عقد';
                      icon = Icons.handshake_outlined;
                      break;
                    case TextDocumentType.other:
                      label = 'أخرى';
                      icon = Icons.more_horiz;
                      break;
                  }

                  return DropdownMenuItem<TextDocumentType>(
                    value: type,
                    child: Row(
                      children: [
                        Icon(icon, size: 20),
                        const SizedBox(width: 8),
                        Text(label),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: _isEditing
                    ? (value) {
                        if (value != null) {
                          setState(() {
                            _selectedType = value;
                          });
                        }
                      }
                    : null,
              ),
              const SizedBox(height: 16),

              // خيار المشاركة
              SwitchListTile(
                title: const Text('مشاركة المستند مع الآخرين'),
                subtitle: const Text('السماح للآخرين بالوصول إلى هذا المستند'),
                value: _isShared,
                onChanged: _isEditing
                    ? (value) {
                        setState(() {
                          _isShared = value;
                        });
                      }
                    : null,
                activeColor: AppColors.primary,
              ),
              const SizedBox(height: 16),

              // محرر النصوص
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      // شريط أدوات المحرر
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.format_bold),
                              onPressed: () {
                                // تنفيذ أمر تغميق النص
                              },
                              tooltip: 'تغميق',
                            ),
                            IconButton(
                              icon: const Icon(Icons.format_italic),
                              onPressed: () {
                                // تنفيذ أمر مائل
                              },
                              tooltip: 'مائل',
                            ),
                            IconButton(
                              icon: const Icon(Icons.format_underline),
                              onPressed: () {
                                // تنفيذ أمر تسطير
                              },
                              tooltip: 'تسطير',
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.format_list_bulleted),
                              onPressed: () {
                                // تنفيذ أمر قائمة نقطية
                              },
                              tooltip: 'قائمة نقطية',
                            ),
                            IconButton(
                              icon: const Icon(Icons.format_list_numbered),
                              onPressed: () {
                                // تنفيذ أمر قائمة رقمية
                              },
                              tooltip: 'قائمة رقمية',
                            ),
                          ],
                        ),
                      ),

                      // محرر النصوص
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Obx(() {
                            final controller =
                                _documentController.textController;
                            // التحقق من وجود المتحكم قبل استخدامه
                            if (controller == null) {
                              return const Center(
                                child: Text('جاري تحميل المحرر...'),
                              );
                            }

                            // استخدام محرر النصوص البسيط حتى يتم تثبيت المكتبة
                            return TextField(
                              controller: controller,
                              focusNode: _editorFocusNode,
                              enabled: _isEditing,
                              maxLines: null,
                              decoration: const InputDecoration(
                                hintText: 'اكتب محتوى المستند هنا...',
                                border: InputBorder.none,
                              ),
                              style: const TextStyle(fontSize: 16),
                            );

                            // ملاحظة: يمكن استبدال هذا بـ SuperEditor بعد تثبيت المكتبة
                            // return SuperEditor.standard(
                            //   editor: DocumentEditor(
                            //     document: MutableDocument(
                            //       nodes: [
                            //         ParagraphNode(
                            //           id: DocumentEditor.createNodeId(),
                            //           text: AttributedText(text: controller.text),
                            //         ),
                            //       ],
                            //     ),
                            //   ),
                            //   readOnly: !_isEditing,
                            //   stylesheet: defaultStylesheet.copyWith(
                            //     textStyler: defaultStylesheet.textStyler?.copyWith(
                            //       textDirection: TextDirection.rtl,
                            //     ),
                            //   ),
                            // );
                          }),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
