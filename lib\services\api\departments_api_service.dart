import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../models/department_model.dart';
import 'api_service.dart';

/// خدمة API للأقسام - متطابقة مع ASP.NET Core API
class DepartmentsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final response = await _apiService.get('/api/Departments?pageSize=1000');
      
      // تحليل الاستجابة
      final responseBody = response.body;
      if (responseBody.isEmpty) {
        debugPrint('استجابة فارغة من الخادم');
        return [];
      }
      
      final data = jsonDecode(responseBody);
      
      // التحقق من نوع الاستجابة
      if (data is Map<String, dynamic>) {
        if (data.containsKey('data') && data['data'] is List) {
          // استجابة مع pagination
          final departmentsList = data['data'] as List;
          return departmentsList
              .map((json) => Department.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      } else if (data is List) {
        // استجابة مباشرة كقائمة
        return data
            .map((json) => Department.fromJson(json as Map<String, dynamic>))
            .toList();
      }
      
      debugPrint('نوع استجابة غير متوقع: ${data.runtimeType}');
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام: $e');
      rethrow;
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(int id) async {
    try {
      final response = await _apiService.get('/api/Departments/$id');
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على القسم $id: $e');
      return null;
    }
  }

  /// الحصول على الأقسام النشطة
  Future<List<Department>> getActiveDepartments() async {
    try {
      final response = await _apiService.get('/api/Departments/active');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام النشطة: $e');
      rethrow;
    }
  }

  /// الحصول على الأقسام الرئيسية (بدون قسم أب)
  Future<List<Department>> getParentDepartments() async {
    try {
      final response = await _apiService.get('/api/Departments/parents');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام الرئيسية: $e');
      rethrow;
    }
  }

  /// الحصول على الأقسام الفرعية لقسم محدد
  Future<List<Department>> getSubDepartments(int parentId) async {
    try {
      final response = await _apiService.get('/api/Departments/$parentId/children');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأقسام الفرعية للقسم $parentId: $e');
      rethrow;
    }
  }

  /// الحصول على موظفي قسم محدد
  Future<List<dynamic>> getDepartmentEmployees(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Departments/$departmentId/assign-users');
      return _apiService.handleListResponse<dynamic>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على موظفي القسم $departmentId: $e');
      rethrow;
    }
  }

  /// الحصول على مهام قسم محدد
  Future<List<dynamic>> getDepartmentTasks(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Departments/$departmentId/tasks');
      return _apiService.handleListResponse<dynamic>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام القسم $departmentId: $e');
      rethrow;
    }
  }

  /// إنشاء قسم جديد
  Future<Department> createDepartment(Department department) async {
    try {
      final response = await _apiService.post(
        '/api/Departments',
        department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء القسم: $e');
      rethrow;
    }
  }

  /// تحديث قسم
  Future<Department> updateDepartment(int id, Department department) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/$id',
        department.toJson(),
      );
      return _apiService.handleResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث القسم $id: $e');
      rethrow;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(int id) async {
    try {
      final response = await _apiService.delete('/api/Departments/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف القسم $id: $e');
      return false;
    }
  }

  /// تفعيل قسم
  Future<bool> activateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/$id/activate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تفعيل القسم: $e');
      return false;
    }
  }

  /// إلغاء تفعيل قسم
  Future<bool> deactivateDepartment(int id) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/$id/deactivate',
        {},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إلغاء تفعيل القسم: $e');
      return false;
    }
  }

  /// البحث في الأقسام
  Future<List<Department>> searchDepartments(String query) async {
    try {
      final response = await _apiService.get('/api/Departments/search?q=$query');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في الأقسام: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات قسم محدد
  Future<Map<String, dynamic>> getDepartmentStatistics(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Departments/$departmentId/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات القسم: $e');
      return {};
    }
  }

  /// الحصول على إحصائيات جميع الأقسام
  Future<Map<String, dynamic>> getAllDepartmentsStatistics() async {
    try {
      final response = await _apiService.get('/api/Departments/statistics');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأقسام: $e');
      return {};
    }
  }

  /// نقل موظف إلى قسم آخر
  Future<bool> transferEmployee(int employeeId, int toDepartmentId) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/transfer-employee',
        {
          'employeeId': employeeId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل الموظف: $e');
      return false;
    }
  }

  /// نقل مهمة إلى قسم آخر
  Future<bool> transferTask(int taskId, int toDepartmentId) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/transfer-task',
        {
          'taskId': taskId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل المهمة: $e');
      return false;
    }
  }

  /// دمج قسمين
  Future<bool> mergeDepartments(int fromDepartmentId, int toDepartmentId) async {
    try {
      final response = await _apiService.post(
        '/api/Departments/merge',
        {
          'fromDepartmentId': fromDepartmentId,
          'toDepartmentId': toDepartmentId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في دمج الأقسام: $e');
      return false;
    }
  }

  /// تصدير بيانات الأقسام
  Future<Map<String, dynamic>> exportDepartments() async {
    try {
      final response = await _apiService.get('/api/Departments/export');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تصدير بيانات الأقسام: $e');
      return {};
    }
  }

  /// استيراد بيانات الأقسام
  Future<bool> importDepartments(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.post(
        '/api/Departments/import',
        data,
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في استيراد بيانات الأقسام: $e');
      return false;
    }
  }

  /// الحصول على التسلسل الهرمي للأقسام
  Future<List<Department>> getDepartmentHierarchy() async {
    try {
      final response = await _apiService.get('/api/Departments/hierarchy');
      return _apiService.handleListResponse<Department>(
        response,
        (json) => Department.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على التسلسل الهرمي للأقسام: $e');
      return [];
    }
  }

  /// إعادة ترتيب الأقسام
  Future<bool> reorderDepartments(List<Map<String, dynamic>> departmentOrders) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/reorder',
        {
          'departmentOrders': departmentOrders,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إعادة ترتيب الأقسام: $e');
      return false;
    }
  }

  /// تعيين مستخدمين لقسم
  Future<Map<String, dynamic>> assignUsersToDepartment(int departmentId, List<int> userIds) async {
    try {
      final response = await _apiService.post(
        '/api/Departments/$departmentId/assign-users',
        {'userIds': userIds},
      );
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      debugPrint('خطأ في تعيين المستخدمين للقسم $departmentId: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// نقل قسم إلى قسم آخر (تغيير الأب)
  Future<bool> moveDepartment(int departmentId, int? newParentId) async {
    try {
      final response = await _apiService.put(
        '/api/Departments/$departmentId/move',
        {'newParentId': newParentId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نقل القسم: $e');
      return false;
    }
  }
}
