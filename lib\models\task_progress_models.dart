import 'task_models.dart';
import 'user_model.dart';

/// نموذج متتبع تقدم المهمة - متطابق مع ASP.NET Core API
class TaskProgressTracker {
  final int id;
  final int taskId;
  final int progress;
  final int updatedAt;
  final int updatedBy;
  final double progressPercentage;
  final String? notes;

  // Navigation properties
  final Task? task;
  final User? updatedByNavigation;

  // خصائص إضافية للمساهمات (للتوافق مع الواجهة الأمامية)
  final String? evidenceType;
  final String? evidenceDescription;
  final double contributionPercentage;
  final int? attachmentId;

  const TaskProgressTracker({
    required this.id,
    required this.taskId,
    required this.progress,
    required this.updatedAt,
    required this.updatedBy,
    required this.progressPercentage,
    this.notes,
    this.task,
    this.updatedByNavigation,
    this.evidenceType,
    this.evidenceDescription,
    this.contributionPercentage = 0.0,
    this.attachmentId,
  });

  /// الحصول على معرف المستخدم (للتوافق مع الكود الحالي)
  int get userId => updatedBy;

  factory TaskProgressTracker.fromJson(Map<String, dynamic> json) {
    return TaskProgressTracker(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      progress: json['progress'] as int,
      updatedAt: json['updatedAt'] as int,
      updatedBy: json['updatedBy'] as int,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      notes: json['notes'] as String?,
      task: json['task'] != null
          ? Task.fromJson(json['task'] as Map<String, dynamic>)
          : null,
      updatedByNavigation: json['updatedByNavigation'] != null
          ? User.fromJson(json['updatedByNavigation'] as Map<String, dynamic>)
          : null,
      evidenceType: json['evidenceType'] as String?,
      evidenceDescription: json['evidenceDescription'] as String?,
      contributionPercentage: (json['contributionPercentage'] as num?)?.toDouble() ?? 0.0,
      attachmentId: json['attachmentId'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'progress': progress,
      'updatedAt': updatedAt,
      'updatedBy': updatedBy,
      'progressPercentage': progressPercentage,
      'notes': notes,
      'evidenceType': evidenceType,
      'evidenceDescription': evidenceDescription,
      'contributionPercentage': contributionPercentage,
      'attachmentId': attachmentId,
    };
  }

  TaskProgressTracker copyWith({
    int? id,
    int? taskId,
    int? progress,
    int? updatedAt,
    int? updatedBy,
    double? progressPercentage,
    String? notes,
    Task? task,
    User? updatedByNavigation,
    String? evidenceType,
    String? evidenceDescription,
    double? contributionPercentage,
    int? attachmentId,
  }) {
    return TaskProgressTracker(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      progress: progress ?? this.progress,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      notes: notes ?? this.notes,
      task: task ?? this.task,
      updatedByNavigation: updatedByNavigation ?? this.updatedByNavigation,
      evidenceType: evidenceType ?? this.evidenceType,
      evidenceDescription: evidenceDescription ?? this.evidenceDescription,
      contributionPercentage: contributionPercentage ?? this.contributionPercentage,
      attachmentId: attachmentId ?? this.attachmentId,
    );
  }

  /// الحصول على تاريخ التحديث كـ DateTime
  DateTime get updatedAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(updatedAt * 1000);

  /// الحصول على نسبة التقدم كنص
  String get progressText => '${progressPercentage.toStringAsFixed(1)}%';

  /// التحقق من اكتمال المهمة
  bool get isCompleted => progressPercentage >= 100.0;

  @override
  String toString() {
    return 'TaskProgressTracker(id: $id, progress: $progressText, updatedAt: $updatedAtDateTime)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskProgressTracker && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج طلب تحديث تقدم المهمة
class UpdateTaskProgressRequest {
  final int progress;
  final double progressPercentage;
  final String? notes;

  const UpdateTaskProgressRequest({
    required this.progress,
    required this.progressPercentage,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'progress': progress,
      'progressPercentage': progressPercentage,
      'notes': notes,
    };
  }
}

/// نموذج ملخص تقدم المهمة
class TaskProgressSummary {
  final int taskId;
  final double currentProgress;
  final DateTime lastUpdated;
  final Map<String, double> userContributions;
  final int totalUpdates;
  final double averageProgressPerUpdate;
  final List<TaskProgressTracker> recentUpdates;
  final double? dailyProgressRate;
  final double? estimatedDaysToCompletion;
  final double totalPercentage;

  const TaskProgressSummary({
    required this.taskId,
    required this.currentProgress,
    required this.lastUpdated,
    required this.userContributions,
    required this.totalUpdates,
    required this.averageProgressPerUpdate,
    required this.recentUpdates,
    this.dailyProgressRate,
    this.estimatedDaysToCompletion,
    required this.totalPercentage,
  });

  factory TaskProgressSummary.fromJson(Map<String, dynamic> json) {
    return TaskProgressSummary(
      taskId: json['taskId'] as int,
      currentProgress: (json['currentProgress'] as num).toDouble(),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch((json['lastUpdated'] as int) * 1000),
      userContributions: Map<String, double>.from(
        (json['userContributions'] as Map<String, dynamic>).map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ),
      ),
      totalUpdates: json['totalUpdates'] as int,
      averageProgressPerUpdate: (json['averageProgressPerUpdate'] as num).toDouble(),
      recentUpdates: (json['recentUpdates'] as List<dynamic>)
          .map((item) => TaskProgressTracker.fromJson(item as Map<String, dynamic>))
          .toList(),
      dailyProgressRate: (json['dailyProgressRate'] as num?)?.toDouble(),
      estimatedDaysToCompletion: (json['estimatedDaysToCompletion'] as num?)?.toDouble(),
      totalPercentage: (json['totalPercentage'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'currentProgress': currentProgress,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch ~/ 1000,
      'userContributions': userContributions,
      'totalUpdates': totalUpdates,
      'averageProgressPerUpdate': averageProgressPerUpdate,
      'recentUpdates': recentUpdates.map((update) => update.toJson()).toList(),
      'dailyProgressRate': dailyProgressRate,
      'estimatedDaysToCompletion': estimatedDaysToCompletion,
      'totalPercentage': totalPercentage,
    };
  }

  TaskProgressSummary copyWith({
    int? taskId,
    double? currentProgress,
    DateTime? lastUpdated,
    Map<String, double>? userContributions,
    int? totalUpdates,
    double? averageProgressPerUpdate,
    List<TaskProgressTracker>? recentUpdates,
    double? dailyProgressRate,
    double? estimatedDaysToCompletion,
    double? totalPercentage,
  }) {
    return TaskProgressSummary(
      taskId: taskId ?? this.taskId,
      currentProgress: currentProgress ?? this.currentProgress,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      userContributions: userContributions ?? this.userContributions,
      totalUpdates: totalUpdates ?? this.totalUpdates,
      averageProgressPerUpdate: averageProgressPerUpdate ?? this.averageProgressPerUpdate,
      recentUpdates: recentUpdates ?? this.recentUpdates,
      dailyProgressRate: dailyProgressRate ?? this.dailyProgressRate,
      estimatedDaysToCompletion: estimatedDaysToCompletion ?? this.estimatedDaysToCompletion,
      totalPercentage: totalPercentage ?? this.totalPercentage,
    );
  }
}

/// نموذج ملخص تتبع الوقت للمهمة
class TaskTimeTrackingSummary {
  final int taskId;
  final Map<String, int> userMinutes; // معرف المستخدم -> الدقائق المستغرقة
  final int totalMinutes;
  final int activeEntries;
  final DateTime? lastActivity;
  final DateTime? lastUpdated; // إضافة خاصية lastUpdated
  final Map<String, List<dynamic>> userEntries; // تغيير النوع لتجنب مشاكل الاستيراد

  const TaskTimeTrackingSummary({
    required this.taskId,
    required this.userMinutes,
    required this.totalMinutes,
    required this.activeEntries,
    this.lastActivity,
    this.lastUpdated,
    required this.userEntries,
  });

  factory TaskTimeTrackingSummary.fromJson(Map<String, dynamic> json) {
    return TaskTimeTrackingSummary(
      taskId: json['taskId'] as int,
      userMinutes: Map<String, int>.from(json['userMinutes'] as Map<String, dynamic>),
      totalMinutes: json['totalMinutes'] as int,
      activeEntries: json['activeEntries'] as int,
      lastActivity: json['lastActivity'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastActivity'] as int) * 1000)
          : null,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['lastUpdated'] as int) * 1000)
          : null,
      userEntries: Map<String, List<dynamic>>.from(json['userEntries'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'userMinutes': userMinutes,
      'totalMinutes': totalMinutes,
      'activeEntries': activeEntries,
      'lastActivity': lastActivity != null ? lastActivity!.millisecondsSinceEpoch ~/ 1000 : null,
      'lastUpdated': lastUpdated != null ? lastUpdated!.millisecondsSinceEpoch ~/ 1000 : null,
      'userEntries': userEntries,
    };
  }

  TaskTimeTrackingSummary copyWith({
    int? taskId,
    Map<String, int>? userMinutes,
    int? totalMinutes,
    int? activeEntries,
    DateTime? lastActivity,
    DateTime? lastUpdated,
    Map<String, List<dynamic>>? userEntries,
  }) {
    return TaskTimeTrackingSummary(
      taskId: taskId ?? this.taskId,
      userMinutes: userMinutes ?? this.userMinutes,
      totalMinutes: totalMinutes ?? this.totalMinutes,
      activeEntries: activeEntries ?? this.activeEntries,
      lastActivity: lastActivity ?? this.lastActivity,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      userEntries: userEntries ?? this.userEntries,
    );
  }

  /// الحصول على إجمالي الوقت المستغرق كنص منسق
  String get totalTimeFormatted {
    final hours = totalMinutes ~/ 60;
    final minutes = totalMinutes % 60;
    if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }

  /// الحصول على متوسط الوقت لكل مستخدم
  double get averageTimePerUser {
    return userMinutes.isNotEmpty ? totalMinutes / userMinutes.length : 0.0;
  }
}

/// نموذج سجل تتبع الوقت
class TimeTrackingEntry {
  final int id;
  final int taskId;
  final int userId;
  final DateTime startTime;
  final DateTime? endTime;
  final String? description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const TimeTrackingEntry({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.startTime,
    this.endTime,
    this.description,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory TimeTrackingEntry.fromJson(Map<String, dynamic> json) {
    return TimeTrackingEntry(
      id: json['id'] as int,
      taskId: json['taskId'] as int,
      userId: json['userId'] as int,
      startTime: DateTime.fromMillisecondsSinceEpoch((json['startTime'] as int) * 1000),
      endTime: json['endTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['endTime'] as int) * 1000)
          : null,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      createdAt: DateTime.fromMillisecondsSinceEpoch((json['createdAt'] as int) * 1000),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['updatedAt'] as int) * 1000)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'startTime': startTime.millisecondsSinceEpoch ~/ 1000,
      'endTime': endTime != null ? endTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'description': description,
      'isActive': isActive,
      'createdAt': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updatedAt': updatedAt != null ? updatedAt!.millisecondsSinceEpoch ~/ 1000 : null,
    };
  }

  TimeTrackingEntry copyWith({
    int? id,
    int? taskId,
    int? userId,
    DateTime? startTime,
    DateTime? endTime,
    String? description,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TimeTrackingEntry(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب المدة بالدقائق
  int get durationInMinutes {
    if (endTime == null) return 0;
    return endTime!.difference(startTime).inMinutes;
  }

  /// تنسيق المدة كنص
  String get formattedDuration {
    if (endTime == null) return 'نشط';
    final duration = endTime!.difference(startTime);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }
}
