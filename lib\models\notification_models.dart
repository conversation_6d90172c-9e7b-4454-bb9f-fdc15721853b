import 'user_model.dart';

/// أنواع الإشعارات
enum NotificationType {
  taskAssigned('task_assigned', 'تم تعيين مهمة'),
  taskCompleted('task_completed', 'تم إكمال مهمة'),
  taskOverdue('task_overdue', 'مهمة متأخرة'),
  messageReceived('message_received', 'رسالة جديدة'),
  mentionInMessage('mention_in_message', 'تم ذكرك في رسالة'),
  systemUpdate('system_update', 'تحديث النظام'),
  userJoined('user_joined', 'انضم مستخدم جديد'),
  departmentUpdate('department_update', 'تحديث القسم'),
  reportGenerated('report_generated', 'تم إنشاء تقرير'),
  backupCompleted('backup_completed', 'تم إنشاء نسخة احتياطية'),
  general('general', 'عام');

  const NotificationType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static NotificationType fromValue(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.general,
    );
  }
}

/// أولوية الإشعارات
enum NotificationPriority {
  low(1, 'منخفضة'),
  normal(2, 'عادية'),
  high(3, 'عالية'),
  urgent(4, 'عاجلة');

  const NotificationPriority(this.value, this.displayName);
  
  final int value;
  final String displayName;

  static NotificationPriority fromValue(int value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => NotificationPriority.normal,
    );
  }
}

/// أنواع الإشعارات المستخدمة في النظام
class NotificationTypes {
  static const String taskAssigned = 'task_assigned';
  static const String taskUpdated = 'task_updated';
  static const String taskCompleted = 'task_completed';
  static const String commentAdded = 'comment_added';
  static const String messageReceived = 'message_received';
  static const String systemAlert = 'system_alert';
  
  static const List<String> values = [
    taskAssigned,
    taskUpdated,
    taskCompleted,
    commentAdded,
    messageReceived,
    systemAlert,
  ];
}

/// نموذج الإشعار المتطابق مع ASP.NET Core API
class NotificationModel {
  final int id;
  final int userId;
  final String title;
  final String content;
  final String type;
  final bool isRead;
  final int? referenceId;
  final DateTime createdAt;
  final DateTime? readAt;
  final String? actionUrl;

  // Navigation properties
  final User? user;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.content,
    required this.type,
    this.isRead = false,
    this.referenceId,
    required this.createdAt,
    this.readAt,
    this.actionUrl,
    this.user,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as int,
      userId: json['userId'] as int,
      title: json['title'] as String,
      content: json['content'] as String,
      type: json['type'] as String,
      isRead: json['isRead'] as bool? ?? false,
      referenceId: json['referenceId'] as int?,
      createdAt: json['createdAt'] is int 
          ? DateTime.fromMillisecondsSinceEpoch(json['createdAt'] * 1000)
          : DateTime.parse(json['createdAt'].toString()),
      readAt: json['readAt'] != null
          ? (json['readAt'] is int 
              ? DateTime.fromMillisecondsSinceEpoch(json['readAt'] * 1000)
              : DateTime.parse(json['readAt'].toString()))
          : null,
      actionUrl: json['actionUrl'] as String?,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'content': content,
      'type': type,
      'isRead': isRead,
      'referenceId': referenceId,
      'createdAt': createdAt.millisecondsSinceEpoch ~/ 1000,
      'readAt': readAt != null ? readAt!.millisecondsSinceEpoch ~/ 1000 : null,
      'actionUrl': actionUrl,
    };
  }

  NotificationModel copyWith({
    int? id,
    int? userId,
    String? title,
    String? content,
    String? type,
    bool? isRead,
    int? referenceId,
    DateTime? createdAt,
    DateTime? readAt,
    String? actionUrl,
    User? user,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      referenceId: referenceId ?? this.referenceId,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      actionUrl: actionUrl ?? this.actionUrl,
      user: user ?? this.user,
    );
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج الإشعار القديم (للتوافق مع الكود القديم)
class Notification {
  final int id;
  final int userId;
  final String title;
  final String content;
  final NotificationType type;
  final NotificationPriority priority;
  final bool isRead;
  final int createdAt;
  final int? readAt;
  final String? actionUrl;
  final String? actionData;
  final bool isDeleted;

  // Navigation properties
  final User? user;

  const Notification({
    required this.id,
    required this.userId,
    required this.title,
    required this.content,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.actionUrl,
    this.actionData,
    this.isDeleted = false,
    this.user,
  });

  factory Notification.fromJson(Map<String, dynamic> json) {
    return Notification(
      id: json['id'] as int,
      userId: json['userId'] as int,
      title: json['title'] as String,
      content: json['content'] as String,
      type: NotificationType.fromValue(json['type'] as String),
      priority: NotificationPriority.fromValue(json['priority'] as int? ?? 2),
      isRead: json['isRead'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      readAt: json['readAt'] as int?,
      actionUrl: json['actionUrl'] as String?,
      actionData: json['actionData'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'content': content,
      'type': type.value,
      'priority': priority.value,
      'isRead': isRead,
      'createdAt': createdAt,
      'readAt': readAt,
      'actionUrl': actionUrl,
      'actionData': actionData,
      'isDeleted': isDeleted,
    };
  }

  Notification copyWith({
    int? id,
    int? userId,
    String? title,
    String? content,
    NotificationType? type,
    NotificationPriority? priority,
    bool? isRead,
    int? createdAt,
    int? readAt,
    String? actionUrl,
    String? actionData,
    bool? isDeleted,
    User? user,
  }) {
    return Notification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      actionUrl: actionUrl ?? this.actionUrl,
      actionData: actionData ?? this.actionData,
      isDeleted: isDeleted ?? this.isDeleted,
      user: user ?? this.user,
    );
  }

  /// الحصول على تاريخ الإنشاء كـ DateTime
  DateTime get createdAtDateTime => 
      DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

  /// الحصول على تاريخ القراءة كـ DateTime
  DateTime? get readAtDateTime => readAt != null 
      ? DateTime.fromMillisecondsSinceEpoch(readAt! * 1000)
      : null;

  @override
  String toString() {
    return 'Notification(id: $id, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Notification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج إعدادات الإشعارات المتطابق مع ASP.NET Core API
class NotificationSettingModel {
  final int id;
  final int userId;
  final String notificationType;
  final bool isEnabled;
  final String deliveryMethod;
  final int createdAt;
  final int? updatedAt;
  final bool isDeleted;
  final bool isEmailEnabled;
  final bool isPushEnabled;
  final bool isSmsEnabled;

  // Navigation properties
  final User? user;

  const NotificationSettingModel({
    required this.id,
    required this.userId,
    required this.notificationType,
    this.isEnabled = true,
    this.deliveryMethod = 'push',
    required this.createdAt,
    this.updatedAt,
    this.isDeleted = false,
    this.isEmailEnabled = false,
    this.isPushEnabled = true,
    this.isSmsEnabled = false,
    this.user,
  });

  factory NotificationSettingModel.fromJson(Map<String, dynamic> json) {
    return NotificationSettingModel(
      id: json['id'] as int,
      userId: json['userId'] as int,
      notificationType: json['notificationType'] as String,
      isEnabled: json['isEnabled'] as bool? ?? true,
      deliveryMethod: json['deliveryMethod'] as String? ?? 'push',
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isEmailEnabled: json['isEmailEnabled'] as bool? ?? false,
      isPushEnabled: json['isPushEnabled'] as bool? ?? true,
      isSmsEnabled: json['isSmsEnabled'] as bool? ?? false,
      user: json['user'] != null
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'notificationType': notificationType,
      'isEnabled': isEnabled,
      'deliveryMethod': deliveryMethod,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isDeleted': isDeleted,
      'isEmailEnabled': isEmailEnabled,
      'isPushEnabled': isPushEnabled,
      'isSmsEnabled': isSmsEnabled,
    };
  }

  NotificationSettingModel copyWith({
    int? id,
    int? userId,
    String? notificationType,
    bool? isEnabled,
    String? deliveryMethod,
    int? createdAt,
    int? updatedAt,
    bool? isDeleted,
    bool? isEmailEnabled,
    bool? isPushEnabled,
    bool? isSmsEnabled,
    User? user,
  }) {
    return NotificationSettingModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      notificationType: notificationType ?? this.notificationType,
      isEnabled: isEnabled ?? this.isEnabled,
      deliveryMethod: deliveryMethod ?? this.deliveryMethod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      isEmailEnabled: isEmailEnabled ?? this.isEmailEnabled,
      isPushEnabled: isPushEnabled ?? this.isPushEnabled,
      isSmsEnabled: isSmsEnabled ?? this.isSmsEnabled,
      user: user ?? this.user,
    );
  }
}

/// نموذج إعدادات الإشعارات القديم (للتوافق مع الكود القديم)
class NotificationSetting {
  final int id;
  final int userId;
  final NotificationType notificationType;
  final bool isEnabled;
  final bool emailEnabled;
  final bool pushEnabled;
  final bool smsEnabled;
  final int createdAt;
  final int? updatedAt;

  // Navigation properties
  final User? user;

  const NotificationSetting({
    required this.id,
    required this.userId,
    required this.notificationType,
    this.isEnabled = true,
    this.emailEnabled = false,
    this.pushEnabled = true,
    this.smsEnabled = false,
    required this.createdAt,
    this.updatedAt,
    this.user,
  });

  factory NotificationSetting.fromJson(Map<String, dynamic> json) {
    return NotificationSetting(
      id: json['id'] as int,
      userId: json['userId'] as int,
      notificationType: NotificationType.fromValue(json['notificationType'] as String),
      isEnabled: json['isEnabled'] as bool? ?? true,
      emailEnabled: json['emailEnabled'] as bool? ?? false,
      pushEnabled: json['pushEnabled'] as bool? ?? true,
      smsEnabled: json['smsEnabled'] as bool? ?? false,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      user: json['user'] != null 
          ? User.fromJson(json['user'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'notificationType': notificationType.value,
      'isEnabled': isEnabled,
      'emailEnabled': emailEnabled,
      'pushEnabled': pushEnabled,
      'smsEnabled': smsEnabled,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  NotificationSetting copyWith({
    int? id,
    int? userId,
    NotificationType? notificationType,
    bool? isEnabled,
    bool? emailEnabled,
    bool? pushEnabled,
    bool? smsEnabled,
    int? createdAt,
    int? updatedAt,
    User? user,
  }) {
    return NotificationSetting(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      notificationType: notificationType ?? this.notificationType,
      isEnabled: isEnabled ?? this.isEnabled,
      emailEnabled: emailEnabled ?? this.emailEnabled,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      smsEnabled: smsEnabled ?? this.smsEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
    );
  }
}

/// نموذج طلب إنشاء إشعار
class CreateNotificationRequest {
  final int userId;
  final String title;
  final String content;
  final NotificationType type;
  final NotificationPriority priority;
  final String? actionUrl;
  final String? actionData;

  const CreateNotificationRequest({
    required this.userId,
    required this.title,
    required this.content,
    required this.type,
    this.priority = NotificationPriority.normal,
    this.actionUrl,
    this.actionData,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'title': title,
      'content': content,
      'type': type.value,
      'priority': priority.value,
      'actionUrl': actionUrl,
      'actionData': actionData,
    };
  }
}

/// نموذج طلب تحديث إعدادات الإشعارات
class UpdateNotificationSettingsRequest {
  final Map<NotificationType, NotificationSetting> settings;

  const UpdateNotificationSettingsRequest({
    required this.settings,
  });

  Map<String, dynamic> toJson() {
    return {
      'settings': settings.map(
        (type, setting) => MapEntry(
          type.value,
          setting.toJson(),
        ),
      ),
    };
  }
}
