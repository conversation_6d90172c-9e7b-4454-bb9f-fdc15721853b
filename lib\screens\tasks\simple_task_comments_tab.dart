import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/models/task_comment_models.dart';
import 'package:get/get.dart';
import '../../controllers/simple_task_comments_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../constants/app_colors.dart';
// import '../../constants/app_styles.dart';

/// تبويب تعليقات المهمة المحسن مع دعم SignalR
class SimpleTaskCommentsTab extends StatefulWidget {
  final int taskId;
  
  const SimpleTaskCommentsTab({
    super.key, 
    required this.taskId,
  });

  @override
  State<SimpleTaskCommentsTab> createState() => _SimpleTaskCommentsTabState();
}

class _SimpleTaskCommentsTabState extends State<SimpleTaskCommentsTab>
    with TickerProviderStateMixin {
  late final SimpleTaskCommentsController _controller;
  final TextEditingController _textController = TextEditingController();
  final AuthController _authController = Get.find<AuthController>();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _textFocusNode = FocusNode();
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    // إنشاء متحكم جديد أو استخدام الموجود
    if (Get.isRegistered<SimpleTaskCommentsController>()) {
      _controller = Get.find<SimpleTaskCommentsController>();
    } else {
      _controller = Get.put(SimpleTaskCommentsController());
    }

    // تمرير ScrollController إلى المتحكم للتمرير التلقائي
    _controller.setScrollController(_scrollController);

    // تحميل التعليقات
    _loadComments();
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _textFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل التعليقات
  Future<void> _loadComments() async {
    await _controller.loadComments(widget.taskId);
  }

  /// إرسال تعليق جديد
  Future<void> _sendComment() async {
    final content = _textController.text.trim();
    if (content.isEmpty) {
      _showSnackBar('يرجى كتابة تعليق', isError: true);
      return;
    }

    // إضافة تأثير اهتزاز
    HapticFeedback.lightImpact();

    final userId = _authController.getCurrentUserIdOrDefault();
    final success = await _controller.addComment(widget.taskId, userId, content);
    
    if (success) {
      _textController.clear();
      _textFocusNode.unfocus();
      _showSnackBar('تم إرسال التعليق بنجاح');
      
      // استخدام التحديث الشامل الجديد
      final taskController = Get.find<TaskController>();
      await taskController.refreshTaskDetails(widget.taskId);
      
      // التمرير لأسفل لإظهار التعليق الجديد
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    } else {
      _showSnackBar(_controller.error.isNotEmpty ? _controller.error : 'فشل في إرسال التعليق', isError: true);
    }
  }

  /// عرض رسالة
  void _showSnackBar(String message, {bool isError = false}) {
    Get.snackbar(
      isError ? 'خطأ' : 'نجح',
      message,
      backgroundColor: isError ? Colors.red.withValues(alpha: 0.8) : Colors.green.withValues(alpha: 0.8),
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
  }

  /// حذف تعليق
  Future<void> _deleteComment(int commentId) async {
    final confirmed = await _showDeleteConfirmation();
    if (!confirmed) return;

    final success = await _controller.deleteComment(commentId);
    if (success) {
      _showSnackBar('تم حذف التعليق بنجاح');
    } else {
      _showSnackBar(_controller.error.isNotEmpty ? _controller.error : 'فشل في حذف التعليق', isError: true);
    }
  }

  /// تأكيد الحذف
  Future<bool> _showDeleteConfirmation() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا التعليق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// بناء بطاقة تعليق محسنة
  Widget _buildCommentCard(TaskComment comment, int index) {
    final isCurrentUser = comment.userId == _authController.getCurrentUserIdOrDefault();
    final theme = Theme.of(context);
    final isSignalRConnected = _controller.isSignalRConnected;
    
    return AnimatedContainer(
      duration: Duration(milliseconds: 300 + (index * 50)),
      curve: Curves.easeOutBack,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isCurrentUser
                ? LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.1),
                      Colors.white,
                    ],
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                  )
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس التعليق المحسن
                Row(
                  children: [
                    Hero(
                      tag: 'avatar_${comment.userId}',
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              AppColors.primary,
                              AppColors.primary.withValues(alpha: 0.7),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.transparent,
                          child: Text(
                            (comment.user?.name.isNotEmpty == true)
                                ? comment.user!.name!.substring(0, 1).toUpperCase()
                                : comment.userId.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  comment.user?.name ?? 'المستخدم ${comment.userId}',
                                  style: theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: isCurrentUser ? AppColors.primary : null,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isCurrentUser) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'أنت',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 12,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  comment.formattedCreatedAt,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (isSignalRConnected) ...[
                                const SizedBox(width: 8),
                                Container(
                                  width: 6,
                                  height: 6,
                                  decoration: const BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'مباشر',
                                  style: TextStyle(
                                    color: Colors.green[600],
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                    // قائمة الإجراءات المحسنة
                    if (isCurrentUser)
                      PopupMenuButton<String>(
                        icon: Icon(
                          Icons.more_vert,
                          color: Colors.grey[600],
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        onSelected: (value) {
                          if (value == 'delete') {
                            _deleteComment(comment.id);
                          } else if (value == 'copy') {
                            Clipboard.setData(ClipboardData(text: comment.content));
                            _showSnackBar('تم نسخ التعليق');
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'copy',
                            child: Row(
                              children: [
                                Icon(Icons.copy, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('نسخ'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red),
                                SizedBox(width: 8),
                                Text('حذف'),
                              ],
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                // محتوى التعليق المحسن
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.cardColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Text(
                    comment.content,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // شريط الحالة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.cardColor,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.comment_outlined,
                  size: 20,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GetBuilder<SimpleTaskCommentsController>(
                    builder: (controller) => Text(
                      'التعليقات (${controller.comments.length})',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                // مؤشر حالة الاتصال
                    GetBuilder<SimpleTaskCommentsController>(
                      builder: (controller) => controller.isSignalRConnected
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'متصل',
                                  style: TextStyle(
                                    color: Colors.green[600],
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            )
                          : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: Colors.orange,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'غير متصل',
                                  style: TextStyle(
                                    color: Colors.orange[600],
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ],
                ),
            ),

          // قائمة التعليقات مع تحديث فوري
          Expanded(
            child: GetBuilder<SimpleTaskCommentsController>(
              builder: (controller) {
              if (controller.isLoading && controller.comments.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'جاري تحميل التعليقات...',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                );
              }

              if (controller.comments.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.grey.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.comment_outlined,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد تعليقات بعد',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كن أول من يضيف تعليقاً على هذه المهمة',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: () => controller.refreshComments(widget.taskId),
                color: AppColors.primary,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: controller.comments.length,
                  itemBuilder: (context, index) {
                    final comment = controller.comments[index];
                    return _buildCommentCard(comment, index);
                  },
                ),
              );
              },
            ),
          ),

          // شريط إدخال التعليق المحسن
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.cardColor,
              border: Border(
                top: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      child: TextField(
                        controller: _textController,
                        focusNode: _textFocusNode,
                        maxLines: null,
                        minLines: 1,
                        maxLength: 500,
                        textInputAction: TextInputAction.send,
                        decoration: InputDecoration(
                          hintText: 'اكتب تعليقك هنا...',
                          hintStyle: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 14,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          counterText: '',
                          prefixIcon: Icon(
                            Icons.edit_outlined,
                            color: Colors.grey[500],
                            size: 20,
                          ),
                        ),
                        onSubmitted: (_) => _sendComment(),
                        onChanged: (value) {
                          // يمكن إضافة منطق للكتابة المباشرة هنا
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  GetBuilder<SimpleTaskCommentsController>(
                    builder: (controller) => AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      child: Material(
                        color: controller.isLoading 
                            ? Colors.grey[300] 
                            : AppColors.primary,
                        borderRadius: BorderRadius.circular(24),
                        elevation: controller.isLoading ? 0 : 2,
                        child: InkWell(
                          onTap: controller.isLoading ? null : _sendComment,
                          borderRadius: BorderRadius.circular(24),
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                            ),
                            child: controller.isLoading
                                ? const Center(
                                    child: SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                  )
                                : const Icon(
                                    Icons.send_rounded,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                        ),
                      ),
                    ),
                  ),
                )  ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}