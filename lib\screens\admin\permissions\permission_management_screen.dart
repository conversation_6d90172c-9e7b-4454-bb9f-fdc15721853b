import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/permission_models.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_card_widget.dart';

/// شاشة إدارة الصلاحيات
class PermissionManagementScreen extends StatefulWidget {
  const PermissionManagementScreen({super.key});

  @override
  State<PermissionManagementScreen> createState() => _PermissionManagementScreenState();
}

class _PermissionManagementScreenState extends State<PermissionManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<Permission> _filteredPermissions = <Permission>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedGroup = 'الكل'.obs;

  @override
  void initState() {
    super.initState();
    _loadPermissions();
    _setupSearch();
  }

  /// تحميل الصلاحيات
  Future<void> _loadPermissions() async {
    _isLoading.value = true;
    try {
      await _adminController.loadPermissions();
      _filterPermissions();
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل الصلاحيات: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// إعداد البحث
  void _setupSearch() {
    _searchController.addListener(_filterPermissions);
    _filteredPermissions.assignAll(_adminController.permissions);
  }

  /// تصفية الصلاحيات
  void _filterPermissions() {
    final query = _searchController.text.toLowerCase();
    final selectedGroup = _selectedGroup.value;
    
    var filtered = _adminController.permissions.where((permission) {
      final matchesSearch = query.isEmpty ||
          permission.name.toLowerCase().contains(query) ||
          (permission.description?.toLowerCase().contains(query) ?? false);
      
      final matchesGroup = selectedGroup == 'الكل' ||
          permission.permissionGroup == selectedGroup;
      
      return matchesSearch && matchesGroup;
    }).toList();
    
    _filteredPermissions.assignAll(filtered);
  }

  /// الحصول على مجموعات الصلاحيات
  List<String> get _permissionGroups {
    final groups = _adminController.permissions
        .map((p) => p.permissionGroup)
        .toSet()
        .toList();
    groups.sort();
    return ['الكل', ...groups];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصلاحيات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPermissions,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية
          _buildSearchAndFilter(),
          
          // قائمة الصلاحيات
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (_filteredPermissions.isEmpty) {
                return _buildEmptyState();
              }
              
              return _buildPermissionsList();
            }),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث والتصفية
  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الصلاحيات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _filterPermissions();
                    },
                  )
                : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // تصفية المجموعات
          Row(
            children: [
              const Text('المجموعة: '),
              const SizedBox(width: 8),
              Expanded(
                child: Obx(() => DropdownButton<String>(
                  value: _selectedGroup.value,
                  isExpanded: true,
                  items: _permissionGroups.map((group) {
                    return DropdownMenuItem<String>(
                      value: group,
                      child: Text(group),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      _selectedGroup.value = value;
                      _filterPermissions();
                    }
                  },
                )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.security,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isNotEmpty || _selectedGroup.value != 'الكل'
              ? 'لا توجد نتائج للبحث'
              : 'لا يوجد صلاحيات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty || _selectedGroup.value != 'الكل'
              ? 'جرب كلمات بحث مختلفة أو غير المجموعة'
              : 'لا توجد صلاحيات في النظام',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الصلاحيات
  Widget _buildPermissionsList() {
    // تجميع الصلاحيات حسب المجموعة
    final groupedPermissions = <String, List<Permission>>{};
    for (final permission in _filteredPermissions) {
      groupedPermissions.putIfAbsent(permission.permissionGroup, () => []);
      groupedPermissions[permission.permissionGroup]!.add(permission);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedPermissions.length,
      itemBuilder: (context, index) {
        final group = groupedPermissions.keys.elementAt(index);
        final permissions = groupedPermissions[group]!;
        
        return _buildPermissionGroup(group, permissions);
      },
    );
  }

  /// بناء مجموعة الصلاحيات
  Widget _buildPermissionGroup(String group, List<Permission> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس المجموعة
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _getGroupIcon(group),
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                group,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${permissions.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // قائمة الصلاحيات في المجموعة
        ...permissions.map((permission) => _buildPermissionCard(permission)),
        
        const SizedBox(height: 16),
      ],
    );
  }

  /// بناء بطاقة الصلاحية
  Widget _buildPermissionCard(Permission permission) {
    return AdminCardWidget(
      title: permission.name,
      subtitle: permission.description ?? 'لا يوجد وصف',
      icon: _getPermissionIcon(permission),
      iconColor: _getPermissionColor(permission),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مستوى الصلاحية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getLevelColor(permission.level).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getLevelColor(permission.level).withOpacity(0.3),
              ),
            ),
            child: Text(
              'مستوى ${permission.level}',
              style: TextStyle(
                color: _getLevelColor(permission.level),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // صلاحية افتراضية
          if (permission.isDefault)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                ),
              ),
              child: const Text(
                'افتراضية',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      onTap: () => _showPermissionDetails(permission),
    );
  }

  /// الحصول على أيقونة المجموعة
  IconData _getGroupIcon(String group) {
    switch (group.toLowerCase()) {
      case 'tasks':
      case 'المهام':
        return Icons.task;
      case 'users':
      case 'المستخدمين':
        return Icons.people;
      case 'admin':
      case 'الإدارة':
        return Icons.admin_panel_settings;
      case 'reports':
      case 'التقارير':
        return Icons.analytics;
      case 'system':
      case 'النظام':
        return Icons.settings;
      default:
        return Icons.security;
    }
  }

  /// الحصول على أيقونة الصلاحية
  IconData _getPermissionIcon(Permission permission) {
    if (permission.name.contains('view')) return Icons.visibility;
    if (permission.name.contains('create')) return Icons.add;
    if (permission.name.contains('edit')) return Icons.edit;
    if (permission.name.contains('delete')) return Icons.delete;
    if (permission.name.contains('manage')) return Icons.settings;
    return Icons.vpn_key;
  }

  /// الحصول على لون الصلاحية
  Color _getPermissionColor(Permission permission) {
    if (permission.name.contains('view')) return Colors.blue;
    if (permission.name.contains('create')) return Colors.green;
    if (permission.name.contains('edit')) return Colors.orange;
    if (permission.name.contains('delete')) return Colors.red;
    if (permission.name.contains('manage')) return Colors.purple;
    return Colors.grey;
  }

  /// الحصول على لون المستوى
  Color _getLevelColor(int level) {
    switch (level) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.blue;
      case 3:
        return Colors.orange;
      case 4:
        return Colors.red;
      case 5:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// عرض تفاصيل الصلاحية
  void _showPermissionDetails(Permission permission) {
    Get.dialog(
      AlertDialog(
        title: Text(permission.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الاسم', permission.name),
            _buildDetailRow('الوصف', permission.description ?? 'لا يوجد وصف'),
            _buildDetailRow('المجموعة', permission.permissionGroup),
            _buildDetailRow('الفئة', permission.category ?? 'غير محدد'),
            _buildDetailRow('المستوى', permission.level.toString()),
            _buildDetailRow('افتراضية', permission.isDefault ? 'نعم' : 'لا'),
            if (permission.screen != null)
              _buildDetailRow('الشاشة', permission.screen!.name),
            if (permission.action != null)
              _buildDetailRow('الإجراء', permission.action!.name),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
