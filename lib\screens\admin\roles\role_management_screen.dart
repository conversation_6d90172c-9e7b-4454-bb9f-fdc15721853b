import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/role_model.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_card_widget.dart';

/// شاشة إدارة الأدوار
class RoleManagementScreen extends StatefulWidget {
  const RoleManagementScreen({super.key});

  @override
  State<RoleManagementScreen> createState() => _RoleManagementScreenState();
}

class _RoleManagementScreenState extends State<RoleManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final RxBool _isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadRoles();
  }

  /// تحميل الأدوار
  Future<void> _loadRoles() async {
    _isLoading.value = true;
    try {
      await _adminController.loadRoles();
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل الأدوار: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الأدوار'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRoles,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_adminController.roles.isEmpty) {
          return _buildEmptyState();
        }

        return _buildRolesList();
      }),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.security,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أدوار',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الأدوار
  Widget _buildRolesList() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // الأدوار الافتراضية
        _buildSection(
          title: 'الأدوار الافتراضية',
          subtitle: 'أدوار النظام الأساسية',
          icon: Icons.admin_panel_settings,
          roles: _adminController.roles.where((role) => role.isSystemRole).toList(),
        ),
        
        const SizedBox(height: 24),
        
        // الأدوار المخصصة
        _buildSection(
          title: 'الأدوار المخصصة',
          subtitle: 'أدوار مخصصة يمكن تعديلها',
          icon: Icons.group,
          roles: _adminController.roles.where((role) => !role.isSystemRole).toList(),
          canAdd: true,
        ),
      ],
    );
  }

  /// بناء قسم الأدوار
  Widget _buildSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Role> roles,
    bool canAdd = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رأس القسم
        Row(
          children: [
            Icon(icon, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (canAdd && _permissionService.canManagePermissions())
              ElevatedButton.icon(
                onPressed: _showAddRoleDialog,
                icon: const Icon(Icons.add),
                label: const Text('إضافة دور'),
              ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // قائمة الأدوار
        if (roles.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Text(
                  'لا يوجد أدوار في هذا القسم',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ),
          )
        else
          ...roles.map((role) => _buildRoleCard(role)),
      ],
    );
  }

  /// بناء بطاقة الدور
  Widget _buildRoleCard(Role role) {
    return AdminCardWidget(
      title: role.displayName,
      subtitle: role.description ?? 'لا يوجد وصف',
      icon: role.isSystemRole ? Icons.admin_panel_settings : Icons.group,
      iconColor: role.isActive ? null : Colors.grey,
      isEnabled: role.isActive,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مستوى الدور
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getRoleLevelColor(role.level).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getRoleLevelColor(role.level).withOpacity(0.3),
              ),
            ),
            child: Text(
              'مستوى ${role.level}',
              style: TextStyle(
                color: _getRoleLevelColor(role.level),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // حالة النشاط
          Icon(
            role.isActive ? Icons.check_circle : Icons.cancel,
            color: role.isActive ? Colors.green : Colors.red,
            size: 20,
          ),
          
          const SizedBox(width: 8),
          
          // قائمة الإجراءات
          PopupMenuButton<String>(
            onSelected: (value) => _handleRoleAction(value, role),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'permissions',
                child: ListTile(
                  leading: Icon(Icons.security),
                  title: Text('إدارة الصلاحيات'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              if (!role.isSystemRole && _permissionService.canManagePermissions()) ...[
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                PopupMenuItem(
                  value: role.isActive ? 'deactivate' : 'activate',
                  child: ListTile(
                    leading: Icon(
                      role.isActive ? Icons.block : Icons.check_circle,
                      color: role.isActive ? Colors.red : Colors.green,
                    ),
                    title: Text(role.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
      onTap: () => _showRoleDetails(role),
    );
  }

  /// الحصول على لون مستوى الدور
  Color _getRoleLevelColor(int level) {
    switch (level) {
      case 1:
        return Colors.blue;
      case 2:
        return Colors.green;
      case 3:
        return Colors.orange;
      case 4:
        return Colors.red;
      case 5:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  /// معالجة إجراءات الدور
  void _handleRoleAction(String action, Role role) {
    switch (action) {
      case 'permissions':
        _showRolePermissions(role);
        break;
      case 'edit':
        _showEditRoleDialog(role);
        break;
      case 'activate':
      case 'deactivate':
        _toggleRoleStatus(role);
        break;
      case 'delete':
        _deleteRole(role);
        break;
    }
  }

  /// عرض تفاصيل الدور
  void _showRoleDetails(Role role) {
    Get.dialog(
      AlertDialog(
        title: Text(role.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الاسم', role.name),
            _buildDetailRow('الوصف', role.description ?? 'لا يوجد وصف'),
            _buildDetailRow('المستوى', role.level.toString()),
            _buildDetailRow('نوع الدور', role.isSystemRole ? 'افتراضي' : 'مخصص'),
            _buildDetailRow('الحالة', role.isActive ? 'نشط' : 'غير نشط'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          if (_permissionService.canManagePermissions())
            ElevatedButton(
              onPressed: () {
                Get.back();
                _showRolePermissions(role);
              },
              child: const Text('إدارة الصلاحيات'),
            ),
        ],
      ),
    );
  }

  /// بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  /// عرض صلاحيات الدور
  void _showRolePermissions(Role role) {
    // TODO: فتح شاشة إدارة صلاحيات الدور
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض حوار إضافة دور
  void _showAddRoleDialog() {
    // TODO: إضافة حوار إنشاء دور جديد
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// عرض حوار تعديل دور
  void _showEditRoleDialog(Role role) {
    // TODO: إضافة حوار تعديل الدور
    Get.snackbar('قريباً', 'ستتوفر هذه الميزة قريباً');
  }

  /// تبديل حالة الدور
  Future<void> _toggleRoleStatus(Role role) async {
    final action = role.isActive ? 'إلغاء تفعيل' : 'تفعيل';
    final confirmed = await AdminConfirmDialog.show(
      title: '$action الدور',
      message: 'هل أنت متأكد من $action الدور "${role.displayName}"؟',
      confirmText: action,
      icon: role.isActive ? Icons.block : Icons.check_circle,
      confirmColor: role.isActive ? Colors.red : Colors.green,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري $action الدور...');
        
        // TODO: استدعاء API لتبديل حالة الدور
        await Future.delayed(const Duration(seconds: 1)); // محاكاة
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم $action الدور بنجاح',
        );
        
        _loadRoles();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في $action الدور: $e',
        );
      }
    }
  }

  /// حذف الدور
  Future<void> _deleteRole(Role role) async {
    final confirmed = await AdminConfirmDialog.show(
      title: 'حذف الدور',
      message: 'هل أنت متأكد من حذف الدور "${role.displayName}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
      confirmText: 'حذف',
      icon: Icons.delete,
      confirmColor: Colors.red,
    );

    if (confirmed) {
      try {
        AdminLoadingDialog.show(message: 'جاري حذف الدور...');
        
        // TODO: استدعاء API لحذف الدور
        await Future.delayed(const Duration(seconds: 1)); // محاكاة
        
        AdminLoadingDialog.hide();
        AdminMessageDialog.showSuccess(
          title: 'نجح العملية',
          message: 'تم حذف الدور بنجاح',
        );
        
        _loadRoles();
      } catch (e) {
        AdminLoadingDialog.hide();
        AdminMessageDialog.showError(
          title: 'خطأ',
          message: 'فشل في حذف الدور: $e',
        );
      }
    }
  }
}
