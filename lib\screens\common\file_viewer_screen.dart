import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:photo_view/photo_view.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:path/path.dart' as path;

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../services/download_service.dart';
import '../../utils/file_processor.dart';

/// شاشة عرض الملفات داخل التطبيق
///
/// تدعم عرض ملفات PDF والصور وأنواع أخرى من الملفات
class FileViewerScreen extends StatefulWidget {
  /// مسار الملف المراد عرضه
  final String filePath;

  /// اسم الملف
  final String fileName;

  /// عنوان الشاشة
  final String? title;

  const FileViewerScreen({
    super.key,
    required this.filePath,
    required this.fileName,
    this.title,
  });

  @override
  State<FileViewerScreen> createState() => _FileViewerScreenState();
}

class _FileViewerScreenState extends State<FileViewerScreen> {
  final DownloadService _downloadService = DownloadService();
  bool _isLoading = true;
  String _errorMessage = '';
  String _viewerType = 'other';

  @override
  void initState() {
    super.initState();
    _initializeViewer();
  }

  /// تهيئة عارض الملفات
  Future<void> _initializeViewer() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // التحقق من وجود الملف
      if (!kIsWeb) {
        final file = File(widget.filePath);
        if (!await file.exists()) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'الملف غير موجود: ${widget.filePath}';
          });
          return;
        }
      }

      // تحديد نوع العارض المناسب
      final viewerType = _downloadService.getViewerType(widget.filePath);
      setState(() {
        _viewerType = viewerType;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تهيئة العارض: $e';
      });
    }
  }

  /// فتح الملف باستخدام التطبيق الافتراضي
  Future<void> _openWithDefaultApp() async {
    try {
      final result = await _downloadService.openFile(widget.filePath);
      if (!result['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'فشل في فتح الملف'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء فتح الملف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? widget.fileName),
        actions: [
          // زر فتح الملف باستخدام التطبيق الافتراضي
          IconButton(
            icon: const Icon(Icons.open_in_new),
            tooltip: 'فتح باستخدام التطبيق الافتراضي',
            onPressed: _openWithDefaultApp,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? _buildErrorWidget()
              : _buildFileViewer(),
    );
  }

  /// بناء عارض الملفات حسب النوع
  Widget _buildFileViewer() {
    switch (_viewerType) {
      case 'pdf':
        return _buildPdfViewer();
      case 'image':
        return _buildImageViewer();
      default:
        return _buildUnsupportedFileViewer();
    }
  }

  /// بناء عارض ملفات PDF
  Widget _buildPdfViewer() {
    if (kIsWeb) {
      // استخدام Syncfusion PDF Viewer للويب
      return SfPdfViewer.file(
        File(widget.filePath),
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        canShowScrollHead: true,
        canShowScrollStatus: true,
        canShowPaginationDialog: true,
      );
    } else {
      // استخدام Flutter PDF View للتطبيقات المحلية
      return PDFView(
        filePath: widget.filePath,
        enableSwipe: true,
        swipeHorizontal: true,
        autoSpacing: true,
        pageFling: true,
        pageSnap: true,
        defaultPage: 0,
        fitPolicy: FitPolicy.BOTH,
        preventLinkNavigation: false,
        onError: (error) {
          setState(() {
            _errorMessage = 'حدث خطأ أثناء عرض ملف PDF: $error';
          });
        },
        onPageError: (page, error) {
          debugPrint('خطأ في الصفحة $page: $error');
        },
      );
    }
  }

  /// بناء عارض الصور
  Widget _buildImageViewer() {
    return PhotoView(
      imageProvider: FileImage(File(widget.filePath)),
      backgroundDecoration: const BoxDecoration(color: Colors.black),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,
      enableRotation: true,
      loadingBuilder: (context, event) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// بناء عارض للملفات غير المدعومة
  Widget _buildUnsupportedFileViewer() {
    final fileType = FileProcessor.getFileType(widget.filePath);
    final fileSize = FileProcessor.formatFileSize(File(widget.filePath).lengthSync());
    final fileExtension = path.extension(widget.filePath);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(fileType),
            size: 64,
            color: _getFileColor(fileType),
          ),
          const SizedBox(height: 16),
          Text(
            widget.fileName,
            style: AppStyles.subtitle1,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'نوع الملف: $fileExtension',
            style: AppStyles.body2,
          ),
          Text(
            'حجم الملف: $fileSize',
            style: AppStyles.body2,
          ),
          const SizedBox(height: 24),
          const Text(
            'هذا النوع من الملفات غير مدعوم للعرض داخل التطبيق',
            style: TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _openWithDefaultApp,
            icon: const Icon(Icons.open_in_new),
            label: const Text('فتح باستخدام التطبيق الافتراضي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _initializeViewer,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الملف حسب نوعه
  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// الحصول على لون الملف حسب نوعه
  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }
}
