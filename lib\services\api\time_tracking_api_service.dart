import 'package:flutter/foundation.dart';
import '../../models/time_tracking_models.dart';
import 'api_service.dart';

/// خدمة API لتتبع الوقت - متطابقة مع ASP.NET Core API
class TimeTrackingApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع سجلات تتبع الوقت
  Future<List<TimeEntry>> getAllTimeEntries() async {
    try {
      final response = await _apiService.get('/TimeEntries');
      return _apiService.handleListResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت: $e');
      rethrow;
    }
  }

  /// الحصول على سجل تتبع وقت بواسطة المعرف
  Future<TimeEntry?> getTimeEntryById(int id) async {
    try {
      final response = await _apiService.get('/TimeEntries/$id');
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل تتبع الوقت: $e');
      return null;
    }
  }

  /// إنشاء سجل تتبع وقت جديد
  Future<TimeEntry?> createTimeEntry(TimeEntry timeEntry) async {
    try {
      final response = await _apiService.post(
        '/TimeEntries',
        timeEntry.toJson(),
      );
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل تتبع الوقت: $e');
      rethrow;
    }
  }

  /// تحديث سجل تتبع وقت
  Future<TimeEntry?> updateTimeEntry(TimeEntry timeEntry) async {
    try {
      final response = await _apiService.put(
        '/TimeEntries/${timeEntry.id}',
        timeEntry.toJson(),
      );
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث سجل تتبع الوقت: $e');
      rethrow;
    }
  }

  /// حذف سجل تتبع وقت
  Future<bool> deleteTimeEntry(int id) async {
    try {
      final response = await _apiService.delete('/TimeEntries/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف سجل تتبع الوقت: $e');
      return false;
    }
  }

  /// الحصول على سجلات تتبع الوقت لمستخدم معين
  Future<List<TimeEntry>> getTimeEntriesByUser(int userId) async {
    try {
      final response = await _apiService.get('/TimeEntries/user/$userId');
      return _apiService.handleListResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت للمستخدم: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات تتبع الوقت لمهمة معينة
  Future<List<TimeEntry>> getTimeEntriesByTask(int taskId) async {
    try {
      final response = await _apiService.get('/TimeEntries/task/$taskId');
      return _apiService.handleListResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت للمهمة: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات تتبع الوقت لمشروع معين
  Future<List<TimeEntry>> getTimeEntriesByProject(int projectId) async {
    try {
      final response = await _apiService.get('/TimeEntries/project/$projectId');
      return _apiService.handleListResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت للمشروع: $e');
      rethrow;
    }
  }

  /// الحصول على سجلات تتبع الوقت في فترة زمنية
  Future<List<TimeEntry>> getTimeEntriesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/TimeEntries/date-range?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      return _apiService.handleListResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على سجلات تتبع الوقت بالفترة الزمنية: $e');
      rethrow;
    }
  }

  /// بدء تتبع الوقت لمهمة
  Future<TimeEntry?> startTimeTracking(int taskId, String? description) async {
    try {
      final response = await _apiService.post(
        '/TimeEntries/start',
        {
          'taskId': taskId,
          'description': description,
          'startTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
      );
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في بدء تتبع الوقت: $e');
      rethrow;
    }
  }

  /// إيقاف تتبع الوقت
  Future<TimeEntry?> stopTimeTracking(int timeEntryId) async {
    try {
      final response = await _apiService.put(
        '/TimeEntries/$timeEntryId/stop',
        {
          'endTime': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
      );
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إيقاف تتبع الوقت: $e');
      rethrow;
    }
  }

  /// الحصول على السجل النشط لمستخدم معين
  Future<TimeEntry?> getActiveTimeEntry(int userId) async {
    try {
      final response = await _apiService.get('/TimeEntries/active/$userId');
      return _apiService.handleResponse<TimeEntry>(
        response,
        (json) => TimeEntry.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على السجل النشط: $e');
      return null;
    }
  }

  /// الحصول على إجمالي الوقت المسجل لمستخدم في فترة معينة
  Future<int> getTotalTimeByUser(
    int userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
      
      final response = await _apiService.get(
        '/TimeEntries/total-time/user/$userId?startDate=$startTimestamp&endDate=$endTimestamp'
      );
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return (response.body as num?)?.toInt() ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على إجمالي الوقت للمستخدم: $e');
      return 0;
    }
  }

  /// الحصول على إجمالي الوقت المسجل لمهمة
  Future<int> getTotalTimeByTask(int taskId) async {
    try {
      final response = await _apiService.get('/TimeEntries/total-time/task/$taskId');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return (response.body as num?)?.toInt() ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على إجمالي الوقت للمهمة: $e');
      return 0;
    }
  }

  /// الحصول على إجمالي الوقت المسجل لمشروع
  Future<int> getTotalTimeByProject(int projectId) async {
    try {
      final response = await _apiService.get('/TimeEntries/total-time/project/$projectId');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return (response.body as num?)?.toInt() ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('خطأ في الحصول على إجمالي الوقت للمشروع: $e');
      return 0;
    }
  }

  /// الحصول على تقرير تتبع الوقت
  Future<Map<String, dynamic>?> getTimeTrackingReport(
    DateTime? startDate,
    DateTime? endDate,
    int? userId,
    int? projectId,
  ) async {
    try {
      var url = '/TimeEntries/report';
      final params = <String, String>{};
      
      if (startDate != null) {
        params['startDate'] = (startDate.millisecondsSinceEpoch ~/ 1000).toString();
      }
      if (endDate != null) {
        params['endDate'] = (endDate.millisecondsSinceEpoch ~/ 1000).toString();
      }
      if (userId != null) {
        params['userId'] = userId.toString();
      }
      if (projectId != null) {
        params['projectId'] = projectId.toString();
      }

      if (params.isNotEmpty) {
        url += '?${params.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }

      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على تقرير تتبع الوقت: $e');
      return null;
    }
  }

  /// الحصول على إحصائيات تتبع الوقت
  Future<Map<String, dynamic>?> getTimeTrackingStatistics() async {
    try {
      final response = await _apiService.get('/TimeEntries/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات تتبع الوقت: $e');
      return null;
    }
  }

  /// تصدير تقرير تتبع الوقت
  Future<String?> exportTimeTrackingReport(
    String format,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    try {
      var url = '/TimeEntries/export?format=$format';
      if (startDate != null && endDate != null) {
        final startTimestamp = startDate.millisecondsSinceEpoch ~/ 1000;
        final endTimestamp = endDate.millisecondsSinceEpoch ~/ 1000;
        url += '&startDate=$startTimestamp&endDate=$endTimestamp';
      }
      
      final response = await _apiService.get(url);
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as String?;
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في تصدير تقرير تتبع الوقت: $e');
      return null;
    }
  }

  /// الحصول على متوسط الوقت اليومي لمستخدم
  Future<double> getAverageDailyTime(int userId, int days) async {
    try {
      final response = await _apiService.get('/TimeEntries/average-daily/$userId?days=$days');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return (response.body as num?)?.toDouble() ?? 0.0;
      }
      return 0.0;
    } catch (e) {
      debugPrint('خطأ في الحصول على متوسط الوقت اليومي: $e');
      return 0.0;
    }
  }

  /// الحصول على أكثر المهام استهلاكاً للوقت
  Future<List<Map<String, dynamic>>> getTopTimeConsumingTasks(int limit) async {
    try {
      final response = await _apiService.get('/TimeEntries/top-tasks?limit=$limit');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على أكثر المهام استهلاكاً للوقت: $e');
      return [];
    }
  }
}
