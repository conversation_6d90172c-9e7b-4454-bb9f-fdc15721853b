import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/models/dashboard_widget_model.dart'
    as widget_model;
import 'package:flutter_application_2/screens/home/<USER>'
    hide DashboardWidgetType;
import 'package:get/get.dart';
import 'dart:convert';

import '../../models/dashboard_model.dart' as simple_model;
import '../../models/dashboard_models.dart' as dashboard_models;
import '../../services/dashboard_service.dart';
import '../widgets/dashboard/draggable_chart_widget.dart';
import '../widgets/dashboard/chart_detail_dialog.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/department_controller.dart';
import '../widgets/app_drawer.dart';

/// شاشة لوحة المعلومات القابلة للتخصيص
///
/// تعرض لوحة معلومات مخصصة مع مخططات قابلة للسحب والإفلات
class CustomizableDashboardScreen extends StatefulWidget {
  const CustomizableDashboardScreen({super.key});

  @override
  State<CustomizableDashboardScreen> createState() =>
      _CustomizableDashboardScreenState();
}

class _CustomizableDashboardScreenState
    extends State<CustomizableDashboardScreen> {
  final DashboardService _dashboardService = Get.find<DashboardService>();
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController =
      Get.find<DepartmentController>();
  // حالة السحب (يستخدم في setState)

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  void _loadData() async {
    // تحميل لوحات المعلومات
    await _dashboardService.loadDashboards();

    // تحميل البيانات الأخرى
    await _taskController.loadAllTasks();
    await _userController.loadAllUsers();
    await _departmentController.loadDepartments();
  }

  /// إضافة عنصر جديد
  void _addNewWidget() {
    final dashboard = _dashboardService.currentDashboardObs.value;
    if (dashboard == null) return;

    // عرض مربع حوار لاختيار نوع العنصر
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة عنصر جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر نوع العنصر:'),
            const SizedBox(height: 16),
            _buildWidgetTypeButton(
              title: 'مخطط حالة المهام',
              icon: Icons.pie_chart,
              color: Colors.blue,
              onTap: () => _createWidget(
                  widget_model.DashboardWidgetType.taskStatusChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط تقدم المهام',
              icon: Icons.show_chart,
              color: Colors.green,
              onTap: () => _createWidget(
                  widget_model.DashboardWidgetType.taskProgressChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء المستخدمين',
              icon: Icons.people,
              color: Colors.orange,
              onTap: () => _createWidget(
                  widget_model.DashboardWidgetType.userPerformanceChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء الأقسام',
              icon: Icons.business,
              color: Colors.purple,
              onTap: () => _createWidget(
                  widget_model.DashboardWidgetType.departmentPerformanceChart),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// إنشاء عنصر جديد
  void _createWidget(widget_model.DashboardWidgetType type) {
    final dashboard = _dashboardService.currentDashboardObs.value;
    if (dashboard == null) return;

    // إغلاق مربع الحوار
    Get.back();

    // تحديد عنوان العنصر
    String title;
    Map<String, dynamic> settings = {};

    switch (type) {
      case widget_model.DashboardWidgetType.taskStatusChart:
        title = 'توزيع المهام حسب الحالة';
        settings = {
          'chartType': 'pie',
          'showLegend': true,
          'showValues': true,
          'showPercentages': true,
        };
        break;
      case widget_model.DashboardWidgetType.taskProgressChart:
        title = 'تقدم المهام على مدار الوقت';
        settings = {
          'chartType': 'line',
          'showGrid': true,
          'showDots': true,
          'showBelowArea': true,
          'timeRange': 'month',
        };
        break;
      case widget_model.DashboardWidgetType.userPerformanceChart:
        title = 'أداء المستخدمين';
        settings = {
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'maxUsers': 5,
          'sortBy': 'completed',
        };
        break;
      case widget_model.DashboardWidgetType.departmentPerformanceChart:
        title = 'أداء الأقسام';
        settings = {
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'sortBy': 'completed',
        };
        break;
      default:
        title = 'عنصر جديد';
        break;
    }

    // إنشاء العنصر
    final widget = dashboard_models.DashboardWidget(
      id: DateTime.now().millisecondsSinceEpoch,
      dashboardId: dashboard.id,
      type: type.toString().split('.').last,
      title: title,
      config: jsonEncode(settings),
      positionX: 0,
      positionY: 0,
      width: 6,
      height: 4,
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );

    // إضافة العنصر إلى لوحة المعلومات
    final updatedWidgets = List<dashboard_models.DashboardWidget>.from(
        dashboard.dashboardWidgets ?? [])
      ..add(widget);
    final updatedDashboard = dashboard.copyWith(
      dashboardWidgets: updatedWidgets,
      updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
    );

    // حفظ التغييرات
    _dashboardService.saveDashboard(updatedDashboard);

    // عرض رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Text('تم إضافة $title بنجاح'),
          ],
        ),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green[700],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // تم نقل وظيفة حذف العنصر إلى داخل _buildDraggableWidget

  // تم نقل الوظائف إلى داخل _buildDraggableWidget

  /// بناء زر نوع العنصر
  Widget _buildWidgetTypeButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: color.withAlpha(128),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[600],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color bgColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF181B34)
        : const Color(0xFFF6F7FB);

    final Color headerColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;

    final Color accentColor = const Color(0xFF00A9FF); // أزرق Monday.com

    // تم تعليق المتغير غير المستخدم
    // final Color textColor = Theme.of(context).brightness == Brightness.dark
    //   ? Colors.white
    //   : const Color(0xFF323338);

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة المعلومات'),
        actions: [
          // زر إضافة عنصر جديد
          IconButton(
            icon: Icon(
              Icons.add_box_rounded,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFD0D4E4)
                  : const Color(0xFF676879),
              size: 20,
            ),
            tooltip: 'إضافة عنصر جديد',
            onPressed: _addNewWidget,
          ),
          // زر التصدير
          IconButton(
            icon: Icon(
              Icons.save_alt_rounded,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFD0D4E4)
                  : const Color(0xFF676879),
              size: 20,
            ),
            tooltip: 'تصدير لوحة المعلومات',
            onPressed: () {
              final dashboard = _dashboardService.currentDashboardObs.value;
              if (dashboard == null) return;

              // تصدير لوحة المعلومات (سيتم تنفيذ الوظيفة الفعلية لاحقاً)

              // عرض رسالة نجاح
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تصدير لوحة المعلومات بنجاح'),
                  duration: Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings_rounded,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFD0D4E4)
                  : const Color(0xFF676879),
              size: 20,
            ),
            tooltip: 'إعدادات لوحة المعلومات',
            onPressed: () {
              // عرض إعدادات لوحة المعلومات (سيتم تنفيذ الوظيفة الفعلية لاحقاً)

              // عرض رسالة نجاح
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تفعيل وظيفة إعدادات لوحة المعلومات'),
                  duration: Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.green,
                ),
              );
            },
          ),
          // زر التحديث
          IconButton(
            icon: Icon(
              Icons.refresh_rounded,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFFD0D4E4)
                  : const Color(0xFF676879),
              size: 20,
            ),
            tooltip: 'تحديث البيانات',
            onPressed: _loadData,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      backgroundColor: bgColor,
      // appBar: AppBar(
      //   elevation: 0,
      //   backgroundColor: headerColor,
      //   title: Row(
      //     children: [
      //       Container(
      //         padding: const EdgeInsets.all(6),
      //         decoration: BoxDecoration(
      //           color: const Color(0x1A00A9FF), // 0.1 opacity
      //           borderRadius: BorderRadius.circular(4),
      //         ),
      //         child: Icon(
      //           Icons.dashboard_rounded,
      //           color: accentColor,
      //           size: 20,
      //         ),
      //       ),
      //       const SizedBox(width: 12),
      //       Text(
      //         'لوحة المعلومات',
      //         style: TextStyle(
      //           fontWeight: FontWeight.w600,
      //           fontSize: 16,
      //           color: textColor,
      //         ),
      //       ),
      //     ],
      //   ),
      //   actions: [
      //     // زر التحديث
      //     Container(
      //       margin: const EdgeInsets.symmetric(horizontal: 4),
      //       child: IconButton(
      //         icon: Icon(
      //           Icons.refresh_rounded,
      //           color: Theme.of(context).brightness == Brightness.dark
      //             ? const Color(0xFFD0D4E4)
      //             : const Color(0xFF676879),
      //           size: 20,
      //         ),
      //         tooltip: 'تحديث البيانات',
      //         onPressed: _loadData,
      //         style: IconButton.styleFrom(
      //           shape: RoundedRectangleBorder(
      //             borderRadius: BorderRadius.circular(4),
      //           ),
      //         ),
      //       ),
      //     ),

      //     // زر تخصيص لوحة المعلومات
      //     Container(
      //       margin: const EdgeInsets.symmetric(horizontal: 4),
      //       child: IconButton(
      //         icon: Icon(
      //           Icons.tune_rounded,
      //           color: Theme.of(context).brightness == Brightness.dark
      //             ? const Color(0xFFD0D4E4)
      //             : const Color(0xFF676879),
      //           size: 20,
      //         ),
      //         tooltip: 'تخصيص لوحة المعلومات',
      //         onPressed: _showDashboardSettings,
      //         style: IconButton.styleFrom(
      //           shape: RoundedRectangleBorder(
      //             borderRadius: BorderRadius.circular(4),
      //           ),
      //         ),
      //       ),
      //     ),

      //   ],
      // ),
      body: Obx(() {
        final dashboard = _dashboardService.currentDashboardObs.value;

        if (_dashboardService.isLoading) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 48,
                  height: 48,
                  child: CircularProgressIndicator(
                    color: accentColor,
                    strokeWidth: 3,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'جاري تحميل لوحة المعلومات...',
                  style: TextStyle(
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
        }

        if (dashboard == null) {
          return Center(
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: headerColor,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x0A000000), // 0.04 opacity
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0x2900A9FF), // 0.16 opacity
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.dashboard_customize,
                      size: 48,
                      color: accentColor,
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'لا توجد لوحة معلومات',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'قم بإنشاء لوحة معلومات جديدة للبدء',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => _dashboardService.createDefaultDashboard(),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text(
                      'إنشاء لوحة معلومات افتراضية',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: accentColor,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // استخدام Column بدلاً من Stack لتحسين التفاعل مع شريط العنوان
        return Column(
          children: [
            // منطقة الإفلات - تغليفها بـ Expanded لضمان عدم تداخلها مع شريط العنوان
            Expanded(
              child: DragTarget<simple_model.SimpleDashboardWidget>(
                builder: (context, candidateData, rejectedData) {
                  return Container(
                    color: candidateData.isNotEmpty
                        ? Theme.of(context).brightness == Brightness.dark
                            ? const Color(0x6600A9FF) // 0.4 opacity
                            : const Color(0x3300A9FF) // 0.2 opacity
                        : bgColor,
                    child: _buildDashboardContent(dashboard),
                  );
                },
                onAcceptWithDetails: (details) {
                  // اهتزاز عند الإفلات
                  HapticFeedback.mediumImpact();

                  // تحديث موقع العنصر
                  final dashboard =
                      Get.find<DashboardService>().currentDashboardObs.value;
                  if (dashboard == null) return;

                  // حساب الموقع الجديد
                  final cellWidth = 100.0; // عرض الخلية الافتراضي
                  final cellHeight = 100.0; // ارتفاع الخلية

                  final columnIndex = (details.offset.dx / cellWidth).floor();
                  final rowIndex = (details.offset.dy / cellHeight).floor();

                  // تحديث موقع العنصر
                  Get.find<DashboardService>().updateWidgetPosition(
                    details.data.id,
                    rowIndex,
                    columnIndex,
                  );

                  // عرض رسالة نجاح
                  Get.snackbar(
                    'نقل العنصر',
                    'تم نقل العنصر بنجاح',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green[700],
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(16),
                    duration: const Duration(seconds: 2),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }

  /// عرض إعدادات لوحة المعلومات
  // تم تفعيل هذه الدالة في زر الإعدادات
  /*
  void _showDashboardSettings() {
    final dashboard = _dashboardService.currentDashboard.value;
    if (dashboard == null) return;

    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color accentColor = const Color(0xFF00A9FF); // أزرق Monday.com

    final Color cardColor = Theme.of(context).brightness == Brightness.dark
      ? const Color(0xFF292F4C)
      : Colors.white;

    final Color bgColor = Theme.of(context).brightness == Brightness.dark
      ? const Color(0xFF181B34)
      : const Color(0xFFF6F7FB);

    // عرض مربع حوار لتخصيص لوحة المعلومات
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: cardColor,
        child: Container(
          width: 500,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان مربع الحوار
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0x1A00A9FF), // 0.1 opacity
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.tune_rounded,
                      color: accentColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'إعدادات لوحة المعلومات',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                    tooltip: 'إغلاق',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  */
}

/// بناء محتوى لوحة المعلومات
Widget _buildDashboardContent(dashboard_models.Dashboard dashboard) {
  // تحديد ألوان النص حسب السمة
  final Color titleColor = Colors.black;
  final Color descriptionColor = Colors.grey;

  return SingleChildScrollView(
    padding: const EdgeInsets.all(16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // عنوان لوحة المعلومات
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: const Color(0x1A00A9FF), // 0.1 opacity
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(
                Icons.dashboard_rounded,
                color: Color(0xFF00A9FF),
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              dashboard.title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: titleColor,
              ),
            ),
          ],
        ),

        if (dashboard.description != null &&
            dashboard.description!.isNotEmpty) ...[
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(right: 34),
            child: Text(
              dashboard.description!,
              style: TextStyle(
                fontSize: 14,
                color: descriptionColor,
              ),
            ),
          ),
        ],

        const SizedBox(height: 20),

        // شبكة العناصر
        _buildWidgetsGrid(dashboard),
      ],
    ),
  );
}

/// بناء شبكة العناصر
Widget _buildWidgetsGrid(dashboard_models.Dashboard dashboard) {
  // ترتيب العناصر حسب الصف والعمود
  final widgets = dashboard.dashboardWidgets ?? [];
  final sortedWidgets = List<dashboard_models.DashboardWidget>.from(widgets);
  sortedWidgets.sort((a, b) {
    if (a.positionY == b.positionY) {
      return a.positionX.compareTo(b.positionX);
    }
    return a.positionY.compareTo(b.positionY);
  });

  // تنظيم العناصر في صفوف
  final Map<int, List<dashboard_models.DashboardWidget>> rowWidgets = {};
  for (final widget in sortedWidgets) {
    if (!rowWidgets.containsKey(widget.positionY)) {
      rowWidgets[widget.positionY] = [];
    }
    rowWidgets[widget.positionY]!.add(widget);
  }

  // بناء الصفوف
  final rows = <Widget>[];
  final sortedRowIndices = rowWidgets.keys.toList()..sort();

  for (final rowIndex in sortedRowIndices) {
    final rowItems = rowWidgets[rowIndex]!;

    // ترتيب العناصر في الصف حسب العمود
    rowItems.sort((a, b) => a.positionX.compareTo(b.positionX));

    // بناء الصف
    rows.add(
      Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowItems.map((widget) {
            // استخدام عرض العنصر كـ flex
            return Expanded(
              flex: widget.width,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: SizedBox(
                  height: widget.height * 100.0, // ارتفاع العنصر
                  child: _buildDraggableWidget(widget),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  // إذا لم تكن هناك عناصر، عرض رسالة
  if (rows.isEmpty) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_customize,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد عناصر في لوحة المعلومات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'انقر على زر الإضافة لإضافة عناصر جديدة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // استخدام زر الإضافة في شريط التطبيق بدلاً من هنا
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة عنصر جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF00A9FF),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  return Column(
    crossAxisAlignment: CrossAxisAlignment.stretch,
    children: rows,
  );
}

/// بناء عنصر قابل للسحب
Widget _buildDraggableWidget(dashboard_models.DashboardWidget widget) {
  // تحديد ألوان حسب السمة - مستوحاة من Monday.com
  final Color deleteColor = const Color(0xFFE2445C); // أحمر Monday.com
  final Color expandColor = const Color(0xFFFFCB00); // أصفر Monday.com

  // تحويل نوع العنصر
  widget_model.DashboardWidgetType convertToWidgetModelType(String type) {
    switch (type) {
      case 'taskStatusChart':
        return widget_model.DashboardWidgetType.taskStatusChart;
      case 'taskProgressChart':
        return widget_model.DashboardWidgetType.taskProgressChart;
      case 'userPerformanceChart':
        return widget_model.DashboardWidgetType.userPerformanceChart;
      case 'departmentPerformanceChart':
        return widget_model.DashboardWidgetType.departmentPerformanceChart;
      default:
        return widget_model.DashboardWidgetType.custom;
    }
  }

  // تحويل DashboardWidget إلى widget_model.DashboardWidget للتوافق مع DraggableChartWidget
  widget_model.DashboardWidget convertToDashboardWidget(
      dashboard_models.DashboardWidget w) {
    return widget_model.DashboardWidget(
      id: w.id.toString(),
      dashboardId: w.dashboardId.toString(),
      title: w.title,
      description: null,
      type: convertToWidgetModelType(w.type),
      dataSource: 'api',
      query: '',
      position: Offset(w.positionX.toDouble(), w.positionY.toDouble()),
      size: Size(w.width.toDouble(), w.height.toDouble()),
      createdAt: w.createdAtDateTime,
      updatedAt: w.updatedAtDateTime ?? DateTime.now(),
      createdById: '1',
    );
  }

  // تعريف الوظائف المحلية للتعامل مع الأحداث
  void handleDragEnd(dashboard_models.DashboardWidget w, Offset position) {
    HapticFeedback.mediumImpact();

    // استدعاء الدالة الموجودة في الكلاس
    final dashboard = Get.find<DashboardService>().currentDashboardObs.value;
    if (dashboard == null) return;

    // حساب الموقع الجديد
    final cellWidth = 100.0; // عرض الخلية الافتراضي
    final cellHeight = 100.0; // ارتفاع الخلية

    final columnIndex = (position.dx / cellWidth).floor();
    final rowIndex = (position.dy / cellHeight).floor();

    // تحديث موقع العنصر
    Get.find<DashboardService>().updateWidgetPosition(
      w.id,
      rowIndex,
      columnIndex,
    );
  }

  void handleDoubleTap(dashboard_models.DashboardWidget w) {
    HapticFeedback.mediumImpact();
    try {
      final settings =
          w.config != null ? jsonDecode(w.config!) : <String, dynamic>{};

      // Convert to SimpleDashboardWidget for ChartDetailDialog
      final simpleWidget = simple_model.SimpleDashboardWidget(
        id: w.id.toString(),
        title: w.title,
        type: w.type,
        row: w.positionY,
        column: w.positionX,
        width: w.width,
        height: w.height,
        settings: settings,
        isExpandable: true,
        isExpanded: false,
        isRefreshable: true,
      );

      Get.dialog(
        ChartDetailDialog(
          widget: simpleWidget,
          settings: settings,
          onSettingsUpdated: (updatedSettings) {
            Get.find<DashboardService>()
                .updateWidgetSettings(w.id, updatedSettings);

            // عرض رسالة نجاح
            Get.snackbar(
              'تحديث الإعدادات',
              'تم تحديث إعدادات المخطط بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green[700],
              colorText: Colors.white,
              margin: const EdgeInsets.all(16),
              duration: const Duration(seconds: 2),
            );
          },
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض مربع حوار تفاصيل المخطط: $e');

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[700],
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 3),
      );
    }
  }

  void handleDeleteWidget(dashboard_models.DashboardWidget w) {
    HapticFeedback.mediumImpact();
    final dashboard = Get.find<DashboardService>().currentDashboardObs.value;
    if (dashboard == null) return;

    // عرض مربع حوار للتأكيد
    Get.dialog(
      AlertDialog(
        title: const Text('حذف العنصر'),
        content: const Text('هل أنت متأكد من حذف هذا العنصر؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // إغلاق مربع الحوار
              Get.back();

              // حذف العنصر
              final updatedWidgets =
                  List<dashboard_models.DashboardWidget>.from(
                      dashboard.dashboardWidgets ?? [])
                    ..removeWhere((item) => item.id == w.id);

              final updatedDashboard = dashboard.copyWith(
                dashboardWidgets: updatedWidgets,
                updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
              );

              // حفظ التغييرات
              Get.find<DashboardService>().saveDashboard(updatedDashboard);

              // عرض رسالة نجاح
              Get.snackbar(
                'حذف العنصر',
                'تم حذف العنصر بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red[700],
                colorText: Colors.white,
                margin: const EdgeInsets.all(16),
                duration: const Duration(seconds: 2),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  return Stack(
    children: [
      // المخطط
      DraggableChartWidget(
        widget: convertToDashboardWidget(widget),
        onDragStarted: (w) {
          // اهتزاز خفيف عند بدء السحب
          HapticFeedback.lightImpact();
        },
        onDragEnd: (w, offset) => handleDragEnd(widget, offset),
        onDoubleTap: (w) => handleDoubleTap(widget),
        onSettingsUpdated: (w, settings) {
          Get.find<DashboardService>()
              .updateWidgetSettings(widget.id, settings);
        },
      ),

      // أزرار التحكم - تظهر عند التمرير فوق العنصر
      Positioned(
        top: 6,
        left: 6,
        child: Row(
          children: [
            // زر الحذف
            _buildControlButton(
              icon: Icons.delete_outline_rounded,
              color: deleteColor,
              bgColor: const Color(0x1AE2445C), // 0.1 opacity
              tooltip: 'حذف العنصر',
              onPressed: () => handleDeleteWidget(widget),
            ),

            const SizedBox(width: 6),

            // زر التكبير
            _buildControlButton(
              icon: Icons.fullscreen_rounded,
              color: expandColor,
              bgColor: const Color(0x1AFFCB00), // 0.1 opacity
              tooltip: 'تكبير العنصر',
              onPressed: () {
                // تم تبسيط الوظيفة لتجنب الأخطاء
                HapticFeedback.mediumImpact();

                // عرض رسالة للمستخدم
                // تم تبسيط الوظيفة لتجنب الأخطاء
                debugPrint('تم تفعيل وظيفة تكبير العناصر');
              },
            ),
          ],
        ),
      ),

      // مؤشر السحب
      // Positioned(
      //   top: 6,
      //   right: 6,
      //   child: SafeMouseRegion(
      //     cursor: SystemMouseCursors.grab,
      //     child: _buildControlButton(
      //       icon: Icons.drag_indicator_rounded,
      //       color: dragColor,
      //       bgColor: const Color(0x1A00A9FF), // 0.1 opacity
      //       tooltip: 'سحب العنصر',
      //       onPressed: () {
      //         // لا شيء، فقط للإشارة إلى أن العنصر قابل للسحب
      //         HapticFeedback.selectionClick();
      //       },
      //     ),
      //   ),
      // ),
    ],
  );
}

/// بناء زر تحكم
Widget _buildControlButton({
  required IconData icon,
  required Color color,
  required Color bgColor,
  required String tooltip,
  required VoidCallback onPressed,
}) {
  return Container(
    width: 28,
    height: 28,
    decoration: BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(4),
    ),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(4),
        onTap: onPressed,
        child: Tooltip(
          message: tooltip,
          child: Center(
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
        ),
      ),
    ),
  );
}
