import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/user_controller.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/task_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/task_progress_models.dart';

/// تبويب عرض مساهمات مستخدم محدد في مهمة
class ContributorContributionsTab extends StatefulWidget {
  /// معرف المهمة
  final String taskId;

  /// معرف المستخدم (إذا كان محدداً)
  final String? selectedUserId;

  /// دالة يتم استدعاؤها عند إلغاء تحديد المساهم
  final VoidCallback? onContributorDeselected;

  const ContributorContributionsTab({
    super.key,
    required this.taskId,
    this.selectedUserId,
    this.onContributorDeselected,
  });

  @override
  State<ContributorContributionsTab> createState() => _ContributorContributionsTabState();
}

class _ContributorContributionsTabState extends State<ContributorContributionsTab> {
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();

  List<TaskProgressTracker> _userContributions = [];
  List<TaskProgressTracker> _filteredContributions = [];
  String _userName = '';
  bool _isLoading = true;
  String? _error;

  // فلتر المساهمات
  String? _selectedFilter;

  // أنواع المساهمات المتاحة
  final List<Map<String, dynamic>> _contributionTypes = [
    {'type': 'all', 'label': 'الكل', 'icon': Icons.all_inclusive},
    {'type': 'comment', 'label': 'تعليقات', 'icon': Icons.comment},
    {'type': 'file', 'label': 'ملفات', 'icon': Icons.attach_file},
    {'type': 'access', 'label': 'وصول', 'icon': Icons.lock_open},
    {'type': 'transfer', 'label': 'نقل المهمة', 'icon': Icons.swap_horiz},
    {'type': 'progress', 'label': 'تحديث التقدم', 'icon': Icons.trending_up},
  ];

  @override
  void initState() {
    super.initState();
    _loadContributorDetails();
  }

  @override
  void didUpdateWidget(ContributorContributionsTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // إذا تغير معرف المستخدم المحدد، نقوم بإعادة تحميل البيانات
    if (widget.selectedUserId != oldWidget.selectedUserId) {
      _loadContributorDetails();
    }
  }

  /// تحميل تفاصيل مساهمات المستخدم
  Future<void> _loadContributorDetails() async {
    // إذا لم يكن هناك مستخدم محدد، نعرض رسالة
    if (widget.selectedUserId == null) {
      setState(() {
        _isLoading = false;
        _userContributions = [];
        _filteredContributions = [];
        _userName = '';
        _error = null;
        _selectedFilter = null;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل اسم المستخدم
      _userName = await _userController.getUserNameById(widget.selectedUserId!);

      // تحميل مساهمات المستخدم في المهمة
      _userContributions = await _taskController.getProgressTrackersForUserInTask(
        widget.taskId,
        widget.selectedUserId!,
      );

      // ترتيب المساهمات حسب التاريخ (الأحدث أولاً)
      _userContributions.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      // تعيين القائمة المفلترة بشكل مبدئي لتكون نفس القائمة الكاملة
      _filteredContributions = List.from(_userContributions);
      _selectedFilter = 'all';

      // تحميل التعليقات إذا لم تكن محملة بالفعل
      if (_taskController.comments.isEmpty) {
        final taskIdInt = int.tryParse(widget.taskId);
        if (taskIdInt != null) {
          await _taskController.loadTaskComments(taskIdInt);
        }
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تطبيق فلتر على المساهمات
  void _applyFilter(String? filterType) {
    if (filterType == null || filterType == 'all') {
      setState(() {
        _filteredContributions = List.from(_userContributions);
        _selectedFilter = 'all';
      });
      return;
    }

    setState(() {
      _filteredContributions = _userContributions
          .where((contribution) => contribution.evidenceType == filterType)
          .toList();
      _selectedFilter = filterType;
    });
  }

  /// بناء إحصائيات المساهمات
  Widget _buildContributionStats() {
    // حساب عدد المساهمات من كل نوع
    final Map<String, int> typeCounts = {};
    double totalContribution = 0;

    for (final contribution in _userContributions) {
      final type = contribution.evidenceType ?? 'other';
      typeCounts[type] = (typeCounts[type] ?? 0) + 1;
      totalContribution += contribution.contributionPercentage;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص المساهمات',
            style: AppStyles.titleSmall.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي المساهمة',
                  '${totalContribution.toStringAsFixed(1)}%',
                  Icons.pie_chart,
                  Colors.purple,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'عدد المساهمات',
                  '${_userContributions.length}',
                  Icons.format_list_numbered,
                  Colors.blue,
                ),
              ),
              if (typeCounts.containsKey('comment'))
                Expanded(
                  child: _buildStatItem(
                    'التعليقات',
                    '${typeCounts['comment']}',
                    Icons.comment,
                    Colors.green,
                  ),
                ),
              if (typeCounts.containsKey('file'))
                Expanded(
                  child: _buildStatItem(
                    'الملفات',
                    '${typeCounts['file']}',
                    Icons.attach_file,
                    Colors.orange,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: color.withAlpha(30),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppStyles.titleSmall.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء أزرار تصفية المساهمات
  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _contributionTypes.map((type) {
          final isSelected = _selectedFilter == type['type'];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Text(type['label']),
              avatar: Icon(
                type['icon'] as IconData,
                size: 18,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
              onSelected: (selected) {
                _applyFilter(selected ? type['type'] : 'all');
              },
              backgroundColor: Colors.grey.shade100,
              selectedColor: AppColors.primary,
              checkmarkColor: Colors.white,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  /// بناء محتوى التبويب
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ أثناء تحميل البيانات',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: AppStyles.bodyMedium.copyWith(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadContributorDetails,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    // إذا لم يكن هناك مستخدم محدد، نعرض رسالة
    if (widget.selectedUserId == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_search,
              color: Colors.grey.shade400,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'اختر مساهم من قائمة المساهمين لعرض تفاصيل مساهماته',
              style: AppStyles.titleMedium.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_userContributions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey.shade400,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مساهمات مسجلة لـ $_userName',
              style: AppStyles.titleMedium.copyWith(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان مع اسم المستخدم ومعلومات إحصائية
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Hero(
                    tag: 'contributor_${_userName}_avatar',
                    child: CircleAvatar(
                      radius: 24,
                      backgroundColor: AppColors.primary,
                      child: Text(
                        _userName.isNotEmpty ? _userName[0].toUpperCase() : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مساهمات $_userName',
                          style: AppStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.bar_chart,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'عدد المساهمات: ${_userContributions.length}',
                              style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // زر العودة إلى قائمة المساهمين
                  OutlinedButton.icon(
                    onPressed: () {
                      // العودة إلى تبويب المساهمين (التبويب رقم 9)
                      try {
                        final tabController = Get.find<TabController>(tag: 'task_detail_tabs');
                        tabController.animateTo(9);
                      } catch (e) {
                        // إذا لم يتم العثور على متحكم التبويب، نقوم بإعادة تعيين المستخدم المحدد
                        if (widget.onContributorDeselected != null) {
                          widget.onContributorDeselected!();
                        }
                      }
                    },
                    icon: const Icon(Icons.people),
                    label: const Text('المساهمين'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: BorderSide(color: AppColors.primary),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ],
              ),

              // إحصائيات المساهمات
              if (_userContributions.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildContributionStats(),
              ],
            ],
          ),
        ),

        // أزرار تصفية المساهمات
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: _buildFilterChips(),
        ),

        // قائمة المساهمات
        Expanded(
          child: _userContributions.isEmpty
              ? Center(
                  child: Text(
                    'لا توجد مساهمات للعرض',
                    style: AppStyles.bodyMedium.copyWith(color: Colors.grey.shade600),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredContributions.length,
                  itemBuilder: (context, index) {
                    return _buildContributionItem(_filteredContributions[index]);
                  },
                ),
        ),
      ],
    );
  }

  /// بناء عنصر المساهمة
  Widget _buildContributionItem(TaskProgressTracker contribution) {
    // تحديد لون ورمز نوع المساهمة
    IconData icon;
    Color color;
    String typeText;

    switch (contribution.evidenceType) {
      case 'comment':
        icon = Icons.comment;
        color = Colors.blue;
        typeText = 'تعليق';
        break;
      case 'file':
        icon = Icons.attach_file;
        color = Colors.green;
        typeText = 'ملف مرفق';
        break;
      case 'access':
        icon = Icons.lock_open;
        color = Colors.orange;
        typeText = 'وصول';
        break;
      case 'transfer':
        icon = Icons.swap_horiz;
        color = Colors.purple;
        typeText = 'نقل المهمة';
        break;
      case 'progress':
        icon = Icons.trending_up;
        color = Colors.teal;
        typeText = 'تحديث التقدم';
        break;
      default:
        icon = Icons.star;
        color = Colors.amber;
        typeText = 'مساهمة';
    }

    // تحويل التاريخ من int إلى DateTime
    final updatedAtDateTime = DateTime.fromMillisecondsSinceEpoch(contribution.updatedAt);

    // تنسيق التاريخ
    final formattedDate = DateFormat('yyyy/MM/dd').format(updatedAtDateTime);
    final formattedTime = DateFormat('HH:mm').format(updatedAtDateTime);

    // حساب الوقت المنقضي
    final now = DateTime.now();
    final difference = now.difference(updatedAtDateTime);
    String timeAgo;

    if (difference.inDays > 365) {
      timeAgo = 'منذ ${(difference.inDays / 365).floor()} سنة';
    } else if (difference.inDays > 30) {
      timeAgo = 'منذ ${(difference.inDays / 30).floor()} شهر';
    } else if (difference.inDays > 0) {
      timeAgo = 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      timeAgo = 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      timeAgo = 'منذ ${difference.inMinutes} دقيقة';
    } else {
      timeAgo = 'الآن';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          // عرض تفاصيل المساهمة في نافذة منبثقة
          _showContributionDetails(contribution);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // أيقونة نوع المساهمة
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: color.withAlpha(30),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(icon, color: color, size: 22),
                  ),
                  const SizedBox(width: 12),

                  // معلومات المساهمة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // نوع المساهمة
                        Row(
                          children: [
                            Text(
                              typeText,
                              style: AppStyles.titleSmall.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: color.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${contribution.contributionPercentage.toStringAsFixed(1)}%',
                                style: AppStyles.labelSmall.copyWith(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 4),

                        // التاريخ والوقت
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              timeAgo,
                              style: AppStyles.bodySmall.copyWith(
                                color: Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '$formattedDate - $formattedTime',
                              style: AppStyles.bodySmall.copyWith(
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),

                        // الوصف المختصر
                        if (contribution.evidenceDescription != null &&
                            contribution.evidenceDescription!.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          // استخدام نفس طريقة التنسيق مع تحديد عدد الأسطر
                          ConstrainedBox(
                            constraints: const BoxConstraints(maxHeight: 48),
                            child: _buildFormattedDescription(
                              contribution.evidenceDescription!.length > 100
                                  ? '${contribution.evidenceDescription!.substring(0, 100)}...'
                                  : contribution.evidenceDescription!,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // أيقونة للإشارة إلى وجود تفاصيل إضافية
                  if (contribution.evidenceType == 'comment')
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.comment,
                            size: 14,
                            color: Colors.blue.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'عرض التعليق',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    )
                  else if (contribution.notes != null ||
                      contribution.evidenceDescription != null ||
                      contribution.attachmentId != null)
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey.shade400,
                    ),
                ],
              ),

              // عرض المرفقات إن وجدت
              if (contribution.attachmentId != null) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.attach_file,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'مرفق',
                      style: AppStyles.bodySmall.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () {
                        // فتح المرفق (سيتم تنفيذه لاحقاً)
                        Get.snackbar(
                          'فتح المرفق',
                          'سيتم تنفيذ هذه الميزة قريباً',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('عرض'),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// عرض تفاصيل المساهمة في نافذة منبثقة
  void _showContributionDetails(TaskProgressTracker contribution) {
    // تحديد لون ورمز نوع المساهمة
    IconData icon;
    Color color;
    String typeText;

    switch (contribution.evidenceType) {
      case 'comment':
        icon = Icons.comment;
        color = Colors.blue;
        typeText = 'تعليق';
        break;
      case 'file':
        icon = Icons.attach_file;
        color = Colors.green;
        typeText = 'ملف مرفق';
        break;
      case 'access':
        icon = Icons.lock_open;
        color = Colors.orange;
        typeText = 'وصول';
        break;
      case 'transfer':
        icon = Icons.swap_horiz;
        color = Colors.purple;
        typeText = 'نقل المهمة';
        break;
      case 'progress':
        icon = Icons.trending_up;
        color = Colors.teal;
        typeText = 'تحديث التقدم';
        break;
      default:
        icon = Icons.star;
        color = Colors.amber;
        typeText = 'مساهمة';
    }

    // إذا كان نوع المساهمة تعليق، نبحث عن التعليق المرتبط
    if (contribution.evidenceType == 'comment') {
      _showCommentContributionDetails(contribution, icon, color, typeText);
    } else {
      _showRegularContributionDetails(contribution, icon, color, typeText);
    }
  }

  /// عرض تفاصيل مساهمة عادية
  void _showRegularContributionDetails(
    TaskProgressTracker contribution,
    IconData icon,
    Color color,
    String typeText
  ) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          constraints: const BoxConstraints(maxWidth: 500),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان النافذة
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: color.withAlpha(30),
                    child: Icon(icon, color: color),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تفاصيل المساهمة',
                          style: AppStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          typeText,
                          style: AppStyles.bodyMedium.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                    splashRadius: 24,
                  ),
                ],
              ),

              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // معلومات المساهمة
              _buildDetailRow(
                'التاريخ والوقت',
                DateFormat('yyyy/MM/dd - HH:mm').format(DateTime.fromMillisecondsSinceEpoch(contribution.updatedAt)),
                Icons.calendar_today,
              ),

              const SizedBox(height: 12),
              _buildDetailRow(
                'نسبة المساهمة',
                '${contribution.contributionPercentage.toStringAsFixed(1)}%',
                Icons.percent,
              ),

              if (contribution.evidenceDescription != null &&
                  contribution.evidenceDescription!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'الوصف:',
                  style: AppStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  width: double.infinity,
                  child: _buildFormattedDescription(contribution.evidenceDescription!),
                ),
              ],

              if (contribution.notes != null && contribution.notes!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'ملاحظات:',
                  style: AppStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  width: double.infinity,
                  child: Text(
                    contribution.notes!,
                    style: AppStyles.bodyMedium,
                  ),
                ),
              ],

              if (contribution.attachmentId != null) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // فتح المرفق (سيتم تنفيذه لاحقاً)
                      Get.back(); // إغلاق النافذة المنبثقة
                      Get.snackbar(
                        'فتح المرفق',
                        'سيتم تنفيذ هذه الميزة قريباً',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                    icon: const Icon(Icons.visibility),
                    label: const Text('عرض المرفق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض تفاصيل مساهمة تعليق مع محتوى التعليق
  void _showCommentContributionDetails(
    TaskProgressTracker contribution,
    IconData icon,
    Color color,
    String typeText
  ) {
    // البحث عن التعليق المرتبط بهذه المساهمة
    _findRelatedComment(contribution).then((commentContent) {
      Get.dialog(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان النافذة
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: color.withAlpha(30),
                      child: Icon(icon, color: color),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تفاصيل التعليق',
                            style: AppStyles.titleMedium.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            typeText,
                            style: AppStyles.bodyMedium.copyWith(
                              color: color,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.close),
                      splashRadius: 24,
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),

                // معلومات المساهمة
                _buildDetailRow(
                  'التاريخ والوقت',
                  DateFormat('yyyy/MM/dd - HH:mm').format(DateTime.fromMillisecondsSinceEpoch(contribution.updatedAt)),
                  Icons.calendar_today,
                ),

                const SizedBox(height: 12),
                _buildDetailRow(
                  'نسبة المساهمة',
                  '${contribution.contributionPercentage.toStringAsFixed(1)}%',
                  Icons.percent,
                ),

                // محتوى التعليق
                if (commentContent != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    'محتوى التعليق:',
                    style: AppStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    width: double.infinity,
                    child: Text(
                      commentContent,
                      style: AppStyles.bodyMedium,
                    ),
                  ),
                ],

                if (contribution.evidenceDescription != null &&
                    contribution.evidenceDescription!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'الوصف:',
                    style: AppStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    width: double.infinity,
                    child: _buildFormattedDescription(contribution.evidenceDescription!),
                  ),
                ],

                if (contribution.notes != null && contribution.notes!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'ملاحظات:',
                    style: AppStyles.labelMedium.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    width: double.infinity,
                    child: Text(
                      contribution.notes!,
                      style: AppStyles.bodyMedium,
                    ),
                  ),
                ],

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      );
    });
  }

  /// البحث عن التعليق المرتبط بمساهمة
  Future<String?> _findRelatedComment(TaskProgressTracker contribution) async {
    try {
      // الحصول على التعليقات من متحكم المهام
      final comments = _taskController.comments;

      // البحث عن تعليق بنفس المستخدم وتاريخ قريب من تاريخ المساهمة
      // (نفترض أن التعليق تم إنشاؤه قبل المساهمة بفترة قصيرة)
      final matchingComments = comments.where((comment) {
        // التحقق من أن التعليق للمستخدم نفسه
        if (comment.userId != contribution.userId) return false;

        // التحقق من أن التعليق تم إنشاؤه قبل المساهمة بفترة قصيرة (5 دقائق)
        final contributionDateTime = DateTime.fromMillisecondsSinceEpoch(contribution.updatedAt);
        final commentDateTime = DateTime.fromMillisecondsSinceEpoch(comment.createdAt);
        final timeDifference = contributionDateTime.difference(commentDateTime).inMinutes.abs();
        return timeDifference <= 5;
      }).toList();

      // ترتيب التعليقات حسب الأقرب زمنياً للمساهمة
      matchingComments.sort((a, b) {
        final contributionDateTime = DateTime.fromMillisecondsSinceEpoch(contribution.updatedAt);
        final commentADateTime = DateTime.fromMillisecondsSinceEpoch(a.createdAt);
        final commentBDateTime = DateTime.fromMillisecondsSinceEpoch(b.createdAt);
        final diffA = contributionDateTime.difference(commentADateTime).inSeconds.abs();
        final diffB = contributionDateTime.difference(commentBDateTime).inSeconds.abs();
        return diffA.compareTo(diffB);
      });

      // إرجاع محتوى التعليق الأقرب زمنياً (إذا وجد)
      return matchingComments.isNotEmpty ? matchingComments.first.content : null;
    } catch (e) {
      debugPrint('خطأ في البحث عن التعليق المرتبط: $e');
      return null;
    }
  }

  /// بناء صف تفاصيل في النافذة المنبثقة
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: AppStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }

  /// بناء نص منسق للوصف مع إبراز أو حذف بعض الأجزاء
  Widget _buildFormattedDescription(String description) {
    // قائمة بالعبارات التي نريد حذفها
    final List<String> phrasesToRemove = [
      'تم حساب المساهمة تلقائيًا بناءً على',
      'تم احتساب المساهمة تلقائيًا عند',
      'تم تسجيل المساهمة تلقائيًا عند',
    ];

    // التحقق من وجود أي من العبارات المراد حذفها
    String cleanedText = description;
    String? removedPhrase;

    for (final phrase in phrasesToRemove) {
      if (description.contains(phrase)) {
        final parts = description.split(phrase);
        cleanedText = parts.length > 1 ? parts[1].trim() : '';
        removedPhrase = phrase;
        break;
      }
    }

    // إذا تم حذف جزء من النص، نتعامل مع الجزء المتبقي
    if (removedPhrase != null) {
      // التعامل مع حالة تحويل المهمة
      if (cleanedText.contains('تحويل المهمة إلى') || cleanedText.contains('نشاطات المستخدم عند تحويل المهمة إلى')) {
        // استخراج اسم المستخدم المحول إليه
        String userName = '';
        if (cleanedText.contains('تحويل المهمة إلى')) {
          final parts = cleanedText.split('تحويل المهمة إلى');
          userName = parts.length > 1 ? parts[1].trim() : '';
        } else if (cleanedText.contains('نشاطات المستخدم عند تحويل المهمة إلى')) {
          final parts = cleanedText.split('نشاطات المستخدم عند تحويل المهمة إلى');
          userName = parts.length > 1 ? parts[1].trim() : '';
        }

        // إنشاء نص منسق مع إبراز عملية التحويل
        return RichText(
          text: TextSpan(
            style: AppStyles.bodyMedium,
            children: [
              TextSpan(
                text: 'تحويل المهمة إلى ',
                style: TextStyle(
                  color: Colors.purple,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: userName,
                style: TextStyle(
                  color: Colors.purple,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }

      // التعامل مع حالة إضافة تعليق
      if (cleanedText.contains('إضافة تعليق')) {
        return RichText(
          text: TextSpan(
            style: AppStyles.bodyMedium,
            children: [
              TextSpan(
                text: 'إضافة تعليق',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }

      // التعامل مع حالة إرفاق ملف
      if (cleanedText.contains('إرفاق ملف')) {
        return RichText(
          text: TextSpan(
            style: AppStyles.bodyMedium,
            children: [
              TextSpan(
                text: 'إرفاق ملف',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }

      // التعامل مع حالة إضافة المستخدم إلى قائمة الوصول
      if (cleanedText.contains('إضافة المستخدم إلى قائمة الوصول')) {
        return RichText(
          text: TextSpan(
            style: AppStyles.bodyMedium,
            children: [
              TextSpan(
                text: 'إضافة إلى قائمة الوصول',
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      }

      // إذا لم تكن هناك حالة خاصة، نعرض النص المنظف
      return Text(
        cleanedText.isEmpty ? 'مساهمة تلقائية' : cleanedText,
        style: AppStyles.bodyMedium,
      );
    }

    // إذا كان النص لا يحتوي على أي من العبارات المراد حذفها، نعرضه كما هو
    return Text(
      description,
      style: AppStyles.bodyMedium,
    );
  }
}
