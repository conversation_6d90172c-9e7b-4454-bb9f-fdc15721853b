import 'package:flutter/material.dart';
import '../../models/department_model.dart';
import '../../models/user_model.dart';
import '../../constants/app_styles.dart';
import '../../services/api/departments_api_service.dart';
import '../../services/api/user_api_service.dart';

/// عنصر واجهة لعرض التسلسل الهرمي للأقسام بشكل شجري
class DepartmentHierarchyWidget extends StatelessWidget {
  final List<Department> hierarchy;
  final bool canEdit;
  final void Function(Department parent)? onAddSubDepartment;
  final void Function(Department department)? onEditDepartment;
  final void Function(Department department)? onDeleteDepartment;
  final void Function(Department department)? onDepartmentTap;
  final void Function(BuildContext context, Department department)?
      onDepartmentMerge;
  final void Function(BuildContext context, Department department)?
      onDepartmentMove;

  const DepartmentHierarchyWidget({
    super.key,
    required this.hierarchy,
    this.canEdit = false,
    this.onAddSubDepartment,
    this.onEditDepartment,
    this.onDeleteDepartment,
    this.onDepartmentTap,
    this.onDepartmentMerge,
    this.onDepartmentMove,
  });

  @override
  Widget build(BuildContext context) {
    if (hierarchy.isEmpty) {
      return const Center(child: Text('لا يوجد تسلسل هرمي للأقسام'));
    }
    
    // Debug: طباعة التسلسل الهرمي في Widget
    debugPrint('=== DEBUG: Widget - التسلسل الهرمي المستلم ===');
    for (int i = 0; i < hierarchy.length; i++) {
      final dept = hierarchy[i];
      debugPrint('[$i] Widget - القسم الرئيسي: ${dept.name} (ID: ${dept.id})');
      for (int j = 0; j < dept.children.length; j++) {
        final child = dept.children[j];
        debugPrint('  [$j] Widget - القسم الفرعي: ${child.name} (ID: ${child.id}, Parent: ${child.parentId})');
      }
    }
    debugPrint('=== نهاية DEBUG Widget ===');
    
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: hierarchy.length,
      itemBuilder: (context, index) {
        return _buildDepartmentTile(context, hierarchy[index], 0);
      },
    );
  }

  Widget _buildDepartmentTile(
      BuildContext context, Department department, int level) {
    final hasChildren = department.hasChildren;
    final departmentsApi = DepartmentsApiService();
    // إضافة متغير لتحديد إذا كان القسم محدد (للتظليل عند النقر)
    bool isSelected = false;
    return Container(
      margin: EdgeInsets.only(left: level * 16.0, right: 8, top: 2, bottom: 2),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade100, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          if (level > 0)
            Positioned(
              left: 8,
              top: 0,
              bottom: 0,
              child: Container(
                width: 2,
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(1),
                ),
              ),
            ),
          Padding(
            padding: EdgeInsets.only(left: level > 0 ? 16.0 : 0),
            child: ExpansionTile(
              leading: CircleAvatar(
                backgroundColor: department.isActive
                    ? (level == 0 ? const Color.fromARGB(255, 33, 130, 209) : Colors.teal)
                    : Colors.grey,
                child: Icon(
                  hasChildren ? Icons.folder_open : Icons.business,
                  color: Colors.white,
                ),
              ),
              title: GestureDetector(
                // onTap: () {
                //   if (onDepartmentTap != null) onDepartmentTap!(department);
                // },
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        department.name,
                        style: AppStyles.titleMedium.copyWith(
                          color: department.isActive
                              ? (level == 0
                                  ? Colors.blue[900]
                                  : Colors.teal[900])
                              : Colors.grey,
                          fontWeight:
                              level == 0 ? FontWeight.bold : FontWeight.normal,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (hasChildren)
                      Icon(Icons.keyboard_arrow_down_outlined,
                          color: Colors.blue.shade300, size: 20),
                    if (!department.isActive)
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text('غير نشط',
                              style: TextStyle(
                                  color: Colors.red.shade800, fontSize: 11)),
                        ),
                      ),
                  ],
                ),
              ),
              subtitle: Row(
                children: [
                  
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (department.description != null &&
                          department.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: Text(department.description!,
                              style: AppStyles.bodySmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis),
                        ),
                      Row(
                        children: [
                          Icon(Icons.person, size: 16, color: Colors.grey[700]),
                          const SizedBox(width: 4),
                          Builder(
                            builder: (context) {
                              final manager = department.manager;
                              if (manager != null && manager.name.trim().isNotEmpty) {
                                return Text(manager.name, style: AppStyles.labelSmall);
                              } else if (department.managerId != null) {
                                // إذا لم يكن هناك كائن مدير، جلب اسم المدير من جدول المستخدمين
                                return FutureBuilder<User?>(
                                  future: UserApiService().getUserById(department.managerId!),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState == ConnectionState.waiting) {
                                      return Row(
                                        children: [
                                          SizedBox(width: 12, height: 12, child: CircularProgressIndicator(strokeWidth: 1)),
                                          SizedBox(width: 4),
                                          Text('...', style: AppStyles.labelSmall),
                                        ],
                                      );
                                    }
                                    final user = snapshot.data;
                                    if (user != null && user.name.trim().isNotEmpty) {
                                      return Text(user.name, style: AppStyles.labelSmall);
                                    } else {
                                      return Text('لا يوجد مدير', style: AppStyles.labelSmall);
                                    }
                                  },
                                );
                              } else {
                                return Text('لا يوجد مدير', style: AppStyles.labelSmall);
                              }
                            },
                          ),
                          const SizedBox(width: 12),
                          FutureBuilder<List<dynamic>>(
                            future: departmentsApi
                                .getDepartmentEmployees(department.id),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return Row(
                                  children: [
                                    SizedBox(
                                        width: 12,
                                        height: 12,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 1)),
                                    SizedBox(width: 4),
                                    Text('...', style: AppStyles.labelSmall),
                                  ],
                                );
                              }
                              final count =
                                  snapshot.hasData ? snapshot.data!.length : 0;
                              return Row(
                                children: [
                                  Icon(Icons.people,
                                      size: 16, color: Colors.blue[700]),
                                  SizedBox(width: 4),
                                  Text('$count عضو',
                                      style: AppStyles.labelSmall),
                                ],
                              );
                            },
                          ),
                          const SizedBox(width: 12),
                          FutureBuilder<List<dynamic>>(
                            future: departmentsApi
                                .getDepartmentTasks(department.id),
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return Row(
                                  children: [
                                    SizedBox(
                                        width: 12,
                                        height: 12,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 1)),
                                    SizedBox(width: 4),
                                    Text('...', style: AppStyles.labelSmall),
                                  ],
                                );
                              }
                              final count =
                                  snapshot.hasData ? snapshot.data!.length : 0;
                              return Row(
                                children: [
                                  Icon(Icons.task,
                                      size: 16, color: Colors.orange[700]),
                                  SizedBox(width: 4),
                                  Text('$count مهمة',
                                      style: AppStyles.labelSmall),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(width: 80), // Add space between subtitle and trailing
            
                ],
              ),
              trailing: canEdit
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // منطقة الانتقال (بدون هوفر، مع تلميح)
                        GestureDetector(
                          onTap: () {
                            if (onDepartmentTap != null) onDepartmentTap!(department);
                          },
                          child: Tooltip(
                            message: 'انتقال إلى تفاصيل القسم',
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: const Icon(Icons.open_in_new, color: Colors.blue),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // زر القائمة المنبثقة
                        PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'add_sub':
                                if (onAddSubDepartment != null)
                                  onAddSubDepartment!(department);
                                break;
                              case 'edit':
                                if (onEditDepartment != null)
                                  onEditDepartment!(department);
                                break;
                              case 'delete':
                                if (onDeleteDepartment != null)
                                  onDeleteDepartment!(department);
                                break;
                              case 'merge':
                                if (onDepartmentMerge != null)
                                  onDepartmentMerge!(context, department);
                                break;
                              case 'move':
                                if (onDepartmentMove != null)
                                  onDepartmentMove!(context, department);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'add_sub',
                              child: ListTile(
                                  leading: Icon(Icons.add),
                                  title: Text('إضافة قسم فرعي')),
                            ),
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                  leading: Icon(Icons.edit), title: Text('تعديل')),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                  leading: Icon(Icons.delete, color: Colors.red),
                                  title: Text('حذف',
                                      style: TextStyle(color: Colors.red))),
                            ),
                            const PopupMenuItem(
                              value: 'merge',
                              child: ListTile(
                                  leading:
                                      Icon(Icons.merge_type, color: Colors.purple),
                                  title: Text('دمج مع قسم آخر')),
                            ),
                            const PopupMenuItem(
                              value: 'move',
                              child: ListTile(
                                  leading: Icon(Icons.drive_file_move,
                                      color: Colors.teal),
                                  title: Text('نقل إلى قسم آخر')),
                            ),
                          ],
                        ),
                      ],
                    )
                  : null,
              children: department.children.map((child) {
                debugPrint('DEBUG: عرض القسم الفرعي: ${child.name} (ID: ${child.id}) تحت الوالد: ${department.name} (ID: ${department.id})');
                return _buildDepartmentTile(context, child, level + 1);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  void _showMoveDepartmentDialog(BuildContext context, Department department) {
    // TODO: Implement move department functionality
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('نقل القسم'),
          content: Text('هل أنت متأكد أنك تريد نقل هذا القسم؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                // Implement the move logic here
                Navigator.of(context).pop();
              },
              child: Text('نعم'),
            ),
          ],
        );
      },
    );
  }

  // أضف هذا المتغير أعلى الكلاس (خارج الدوال)
  static final ValueNotifier<bool> _hoverNotifier = ValueNotifier(false);
}
