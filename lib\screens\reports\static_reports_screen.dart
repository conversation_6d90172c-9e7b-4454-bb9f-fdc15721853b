import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:path_provider/path_provider.dart';

import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../models/user_model.dart';
import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';
import '../../services/download_service.dart';
import '../../services/api/reports_api_service.dart';
import '../../services/report_export_service.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/pdf_viewer_widget.dart';

/// شاشة التقارير الثابتة
///
/// تعرض قائمة بالتقارير الثابتة الجاهزة للطباعة
class StaticReportsScreen extends StatefulWidget {
  const StaticReportsScreen({super.key});

  @override
  State<StaticReportsScreen> createState() => _StaticReportsScreenState();
}

class _StaticReportsScreenState extends State<StaticReportsScreen> {
  final ReportsApiService _reportsApiService = ReportsApiService();
  final ReportExportService _reportExportService = ReportExportService();
  final DownloadService _downloadService = DownloadService();
  final AuthController _authController = Get.find<AuthController>();

  List<Report> _staticReports = [];
  bool _isLoading = false;
  String? _errorMessage;
  Report? _selectedReport;
  bool _isGeneratingReport = false;

  @override
  void initState() {
    super.initState();
    _loadStaticReports();
  }

  /// تحميل التقارير الثابتة
  Future<void> _loadStaticReports() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // الحصول على جميع التقارير العامة من API
      final reports = await _reportsApiService.getPublicReports();

      // فلترة التقارير حسب صلاحيات المستخدم
      final currentUser = _authController.currentUser.value;
      final isAdmin = currentUser?.role == UserRole.admin;

      final filteredReports = reports.where((report) {
        // إذا كان التقرير متاحًا للجميع
        if (report.isPublic) return true;

        // إذا كان المستخدم مديرًا
        if (isAdmin) return true;

        // في حالة عدم وجود معلومات كافية، نسمح بالوصول
        return true;
      }).toList();

      // ترتيب التقارير حسب تاريخ الإنشاء
      filteredReports.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _staticReports = filteredReports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل التقارير الثابتة: $e';
      });
    }
  }

  /// عرض التقرير الثابت
  Future<void> _viewStaticReport(Report report) async {
    setState(() {
      _selectedReport = report;
      _isGeneratingReport = true;
    });

    try {
      debugPrint('بدء إنشاء التقرير الثابت: ${report.title} (${report.id})');

      // تنفيذ التقرير الثابت وتصديره بتنسيق PDF
      final filePath = await _reportExportService.exportReport(
        reportId: report.id,
        format: ReportFormat.pdf,
      );

      if (!mounted) return;

      setState(() {
        _isGeneratingReport = false;
      });

      if (filePath == null) {
        debugPrint('فشل في إنشاء التقرير: لم يتم إرجاع مسار الملف');

        // عرض رسالة خطأ أكثر تفصيلاً
        _showErrorDialog(
          'فشل في إنشاء التقرير',
          'لم يتمكن النظام من إنشاء ملف التقرير. يرجى التحقق من وجود مساحة كافية على الجهاز والمحاولة مرة أخرى.',
        );
        return;
      }

      debugPrint('تم إنشاء التقرير بنجاح: $filePath');

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('الملف غير موجود بعد إنشائه: $filePath');

        _showErrorDialog(
          'ملف التقرير غير موجود',
          'تم إنشاء التقرير ولكن الملف غير موجود في المسار المتوقع. يرجى المحاولة مرة أخرى.',
        );
        return;
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (fileSize <= 0) {
        debugPrint('ملف التقرير فارغ: $filePath (الحجم: $fileSize بايت)');

        _showErrorDialog(
          'ملف التقرير فارغ',
          'تم إنشاء ملف التقرير ولكنه فارغ. يرجى المحاولة مرة أخرى.',
        );
        return;
      }

      debugPrint('حجم ملف التقرير: $fileSize بايت');

      // التحقق من أن الشاشة لا تزال مرتبطة بشجرة العناصر
      if (!mounted) return;

      // عرض التقرير في عارض PDF
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => Scaffold(
            appBar: AppBar(
              title: Text(report.title),
              actions: [
                IconButton(
                  icon: const Icon(Icons.download),
                  tooltip: 'تنزيل',
                  onPressed: () => _downloadReport(filePath),
                ),
                IconButton(
                  icon: const Icon(Icons.print),
                  tooltip: 'طباعة',
                  onPressed: () => _printReport(filePath),
                ),
              ],
            ),
            body: PdfViewerWidget(
              filePath: filePath,
              showToolbar: true,
              title: report.title,
            ),
          ),
        ),
      ).then((_) {
        // تنظيف الملف المؤقت بعد الانتهاء من عرضه (اختياري)
        // file.delete().catchError((e) => debugPrint('خطأ في حذف الملف المؤقت: $e'));
      });
    } catch (e) {
      debugPrint('استثناء أثناء إنشاء التقرير: $e');

      if (!mounted) return;

      setState(() {
        _isGeneratingReport = false;
      });

      _showErrorDialog(
        'حدث خطأ أثناء إنشاء التقرير',
        'تفاصيل الخطأ: $e',
      );
    }
  }

  /// عرض مربع حوار خطأ
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// تنزيل التقرير
  Future<void> _downloadReport(String filePath) async {
    try {
      final fileName = 'تقرير_${_selectedReport?.title ?? 'ثابت'}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';
      // نسخ الملف إلى مجلد التنزيلات
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final result = await _downloadService.saveDataAsFile(
        data: bytes,
        fileName: fileName,
      );

      if (!mounted) return;

      if (result) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تنزيل التقرير: $fileName'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'فتح',
              onPressed: () => _openDownloadedFile(fileName),
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تنزيل التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تنزيل التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// فتح الملف المنزل باستخدام التطبيق الافتراضي
  Future<void> _openDownloadedFile(String fileName) async {
    try {
      // بناء مسار الملف في مجلد التنزيلات
      final downloadPath = await _getDownloadPath();
      final filePath = '$downloadPath/$fileName';

      final result = await _downloadService.openFile(filePath);
      if (!result['success'] && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'فشل في فتح الملف'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء فتح الملف: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// الحصول على مسار التنزيل
  Future<String> _getDownloadPath() async {
    try {
      if (Platform.isAndroid) {
        final directory = await getExternalStorageDirectory();
        return directory != null ? '${directory.path}/Downloads' : '/storage/emulated/0/Download';
      } else {
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      }
    } catch (e) {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
  }

  /// طباعة التقرير
  Future<void> _printReport(String filePath) async {
    try {
      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('الملف غير موجود'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // قراءة محتوى الملف
      final bytes = await file.readAsBytes();

      // طباعة الملف باستخدام حزمة printing
      await Printing.layoutPdf(
        onLayout: (format) => bytes,
        name: _selectedReport?.title ?? 'تقرير ثابت',
        format: PdfPageFormat.a4,
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء طباعة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// تصدير التقرير بتنسيق معين
  Future<void> _exportReport(Report report, ReportFormat format) async {
    setState(() {
      _selectedReport = report;
      _isGeneratingReport = true;
    });

    try {
      // تنفيذ التقرير الثابت وتصديره بالتنسيق المطلوب
      final filePath = await _reportExportService.exportReport(
        reportId: report.id,
        format: format,
      );

      if (!mounted) return;

      setState(() {
        _isGeneratingReport = false;
      });

      if (filePath == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // تنزيل الملف
      String extension;
      switch (format) {
        case ReportFormat.pdf:
          extension = 'pdf';
          break;
        case ReportFormat.excel:
          extension = 'xlsx';
          break;
        case ReportFormat.csv:
          extension = 'csv';
          break;
        case ReportFormat.json:
          extension = 'json';
          break;
        default:
          extension = 'pdf';
      }

      final fileName = 'تقرير_${report.title}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.$extension';

      // نسخ الملف إلى مجلد التنزيلات
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      final result = await _downloadService.saveDataAsFile(
        data: bytes,
        fileName: fileName,
      );

      if (!mounted) return;

      if (result) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير التقرير: $fileName'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'فتح',
              onPressed: () => _openDownloadedFile(fileName),
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تصدير التقرير'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isGeneratingReport = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض قائمة التنسيقات المتاحة للتصدير
  void _showExportFormats(Report report) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('تصدير كملف PDF'),
            leading: const Icon(Icons.picture_as_pdf),
            onTap: () {
              Navigator.pop(context);
              _exportReport(report, ReportFormat.pdf);
            },
          ),
          ListTile(
            title: const Text('تصدير كملف Excel'),
            leading: const Icon(Icons.table_chart),
            onTap: () {
              Navigator.pop(context);
              _exportReport(report, ReportFormat.excel);
            },
          ),
          ListTile(
            title: const Text('تصدير كملف CSV'),
            leading: const Icon(Icons.format_list_bulleted),
            onTap: () {
              Navigator.pop(context);
              _exportReport(report, ReportFormat.csv);
            },
          ),
          ListTile(
            title: const Text('تصدير كملف JSON'),
            leading: const Icon(Icons.code),
            onTap: () {
              Navigator.pop(context);
              _exportReport(report, ReportFormat.json);
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final isTablet = ResponsiveHelper.isTablet(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير الثابتة'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadStaticReports,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                )
              : _staticReports.isEmpty
                  ? const Center(
                      child: Text('لا توجد تقارير ثابتة متاحة'),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'التقارير الثابتة الجاهزة للطباعة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Expanded(
                            child: GridView.builder(
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: isDesktop ? 3 : (isTablet ? 2 : 1),
                                childAspectRatio: 1.5,
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 16,
                              ),
                              itemCount: _staticReports.length,
                              itemBuilder: (context, index) {
                                final report = _staticReports[index];
                                return _buildReportCard(report);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
      // عرض مؤشر التقدم أثناء إنشاء التقرير
      floatingActionButton: _isGeneratingReport
          ? const FloatingActionButton.extended(
              onPressed: null,
              icon: CircularProgressIndicator(
                color: Colors.white,
              ),
              label: Text('جاري إنشاء التقرير...'),
            )
          : null,
    );
  }

  /// بناء بطاقة التقرير
  Widget _buildReportCard(Report report) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _viewStaticReport(report),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getReportIcon(report.reportType),
                    color: AppColors.primary,
                    size: 32,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      report.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.more_vert),
                    onPressed: () => _showExportFormats(report),
                    tooltip: 'تصدير',
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  report.description ?? 'لا يوجد وصف',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.print),
                    label: const Text('طباعة'),
                    onPressed: () => _viewStaticReport(report),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة التقرير حسب نوعه
  IconData _getReportIcon(ReportType type) {
    switch (type) {
      case ReportType.taskSummary:
        return Icons.summarize;
      case ReportType.userActivity:
        return Icons.people;
      case ReportType.departmentPerformance:
        return Icons.business;
      case ReportType.taskProgress:
        return Icons.trending_up;
      case ReportType.taskStatus:
        return Icons.warning;
      case ReportType.taskDetails:
        return Icons.pie_chart;
      case ReportType.userPerformance:
        return Icons.person;
      case ReportType.departmentWorkload:
        return Icons.work;
      case ReportType.timeTracking:
        return Icons.access_time;
      case ReportType.projectProgress:
        return Icons.timeline;
      case ReportType.projectStatus:
        return Icons.assignment;
      case ReportType.systemUsage:
        return Icons.computer;
      case ReportType.custom:
        return Icons.settings;
      default:
        return Icons.description;
    }
  }
}
