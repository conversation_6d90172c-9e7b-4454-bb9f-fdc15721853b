import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/custom_app_bar.dart';
import '../../utils/app_colors.dart';
import '../../services/database_repair_service.dart';

/// شاشة إصلاح نظام الأرشفة الإلكتروني
///
/// تستخدم لاختبار وإصلاح مشاكل نظام الأرشفة الإلكتروني
class ArchiveSystemRepairScreen extends StatefulWidget {
  const ArchiveSystemRepairScreen({super.key});

  @override
  State<ArchiveSystemRepairScreen> createState() => _ArchiveSystemRepairScreenState();
}

class _ArchiveSystemRepairScreenState extends State<ArchiveSystemRepairScreen> {
  final DatabaseRepair _databaseRepair = DatabaseRepair();
  bool _isLoading = false;
  String _statusMessage = 'جاهز للاختبار والإصلاح';
  final List<String> _logMessages = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'إصلاح نظام الأرشفة الإلكتروني',
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 16),
            _buildActionButtons(),
            const SizedBox(height: 16),
            _buildLogSection(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الحالة
  Widget _buildStatusCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLoading ? Icons.pending : Icons.info_outline,
                  color: _isLoading ? Colors.orange : AppColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة النظام',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(_statusMessage),
            if (_isLoading) ...[
              const SizedBox(height: 16),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _testArchiveSystem,
            icon: const Icon(Icons.check_circle_outline),
            label: const Text('اختبار النظام'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _repairArchiveSystem,
            icon: const Icon(Icons.build),
            label: const Text('إصلاح النظام'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قسم السجل
  Widget _buildLogSection() {
    return Expanded(
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'سجل العمليات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear_all),
                    onPressed: _clearLog,
                    tooltip: 'مسح السجل',
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: ListView.builder(
                  itemCount: _logMessages.length,
                  itemBuilder: (context, index) {
                    final message = _logMessages[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: Text(
                        message,
                        style: TextStyle(
                          color: message.contains('خطأ') || message.contains('فشل')
                              ? Colors.red
                              : message.contains('تحذير')
                                  ? Colors.orange
                                  : message.contains('نجاح') || message.contains('تم')
                                      ? Colors.green
                                      : null,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// اختبار نظام الأرشفة الإلكتروني
  Future<void> _testArchiveSystem() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري اختبار نظام الأرشفة الإلكتروني...';
      _addLogMessage('بدء اختبار نظام الأرشفة الإلكتروني...');
    });

    try {
      final result = await _databaseRepair.testArchiveSystem();

      setState(() {
        _isLoading = false;
        if (result) {
          _statusMessage = 'تم اختبار نظام الأرشفة الإلكتروني بنجاح';
          _addLogMessage('تم اختبار نظام الأرشفة الإلكتروني بنجاح');
        } else {
          _statusMessage = 'فشل اختبار نظام الأرشفة الإلكتروني';
          _addLogMessage('فشل اختبار نظام الأرشفة الإلكتروني');
        }
      });

      Get.snackbar(
        result ? 'نجاح' : 'تحذير',
        result
            ? 'تم اختبار نظام الأرشفة الإلكتروني بنجاح'
            : 'فشل اختبار نظام الأرشفة الإلكتروني، قد تكون هناك مشاكل في النظام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result ? AppColors.success.withAlpha(178) : AppColors.warning.withAlpha(178),
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'حدث خطأ أثناء اختبار نظام الأرشفة الإلكتروني';
        _addLogMessage('خطأ: $e');
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختبار نظام الأرشفة الإلكتروني',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    }
  }

  /// إصلاح نظام الأرشفة الإلكتروني
  Future<void> _repairArchiveSystem() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'جاري إصلاح نظام الأرشفة الإلكتروني...';
      _addLogMessage('بدء إصلاح نظام الأرشفة الإلكتروني...');
    });

    try {
      final result = await _databaseRepair.repairArchiveSystem();

      setState(() {
        _isLoading = false;
        if (result) {
          _statusMessage = 'تم إصلاح نظام الأرشفة الإلكتروني بنجاح';
          _addLogMessage('تم إصلاح نظام الأرشفة الإلكتروني بنجاح');
        } else {
          _statusMessage = 'فشل إصلاح نظام الأرشفة الإلكتروني';
          _addLogMessage('فشل إصلاح نظام الأرشفة الإلكتروني');
        }
      });

      Get.snackbar(
        result ? 'نجاح' : 'تحذير',
        result
            ? 'تم إصلاح نظام الأرشفة الإلكتروني بنجاح'
            : 'فشل إصلاح نظام الأرشفة الإلكتروني، قد تكون هناك مشاكل في النظام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result ? AppColors.success.withAlpha(178) : AppColors.warning.withAlpha(178),
        colorText: Colors.white,
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'حدث خطأ أثناء إصلاح نظام الأرشفة الإلكتروني';
        _addLogMessage('خطأ: $e');
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إصلاح نظام الأرشفة الإلكتروني',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    }
  }

  /// إضافة رسالة إلى السجل
  void _addLogMessage(String message) {
    setState(() {
      final timestamp = DateTime.now().toString().split('.').first;
      _logMessages.add('[$timestamp] $message');
    });
  }

  /// مسح السجل
  void _clearLog() {
    setState(() {
      _logMessages.clear();
      _addLogMessage('تم مسح السجل');
    });
  }
}
