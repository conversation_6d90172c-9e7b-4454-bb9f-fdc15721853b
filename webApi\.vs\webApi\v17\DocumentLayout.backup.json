{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\userpermissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_types.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "UserPermissionsController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\UserPermissionsController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\UserPermissionsController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\UserPermissionsController.cs", "RelativeToolTip": "webApi\\Controllers\\UserPermissionsController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAB0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T02:29:39.424Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "dbo.task_types.sql", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_types.sql", "ToolTip": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.task_types.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-05T02:43:13.31Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "PermissionsController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\PermissionsController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\PermissionsController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\PermissionsController.cs", "RelativeToolTip": "webApi\\Controllers\\PermissionsController.cs", "ViewState": "AgIAABgAAAAAAAAAAAAAABAAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T02:05:05.274Z"}]}]}]}