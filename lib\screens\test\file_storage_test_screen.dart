import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:open_file/open_file.dart';
import '../../services/upload_service.dart';
import '../../controllers/auth_controller.dart';
import '../../services/api/attachments_api_service.dart';

/// شاشة اختبار نظام التخزين المحلي المحسن
/// تتيح اختبار جميع الميزات الجديدة للرفع والإحصائيات
class FileStorageTestScreen extends StatefulWidget {
  final int taskId;
  const FileStorageTestScreen({Key? key, required this.taskId}) : super(key: key);

  @override
  State<FileStorageTestScreen> createState() => _FileStorageTestScreenState();
}

class _FileStorageTestScreenState extends State<FileStorageTestScreen> {
  final UploadService _uploadService = UploadService();
  final AuthController _authController = Get.find<AuthController>();
  
  bool _isUploading = false;
  bool _isLoadingStats = false;
  Map<String, dynamic>? _lastUploadResult;
  Map<String, dynamic>? _statistics;
  List<Map<String, dynamic>> _uploadHistory = [];
  // قائمة المرفقات من الخادم
  List<Map<String, dynamic>> _serverAttachments = [];

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  /// تحميل إحصائيات المرفقات وجلب قائمة المرفقات من الخادم
  Future<void> _loadStatistics() async {
    setState(() => _isLoadingStats = true);
    try {
      // محاولة تحميل الإحصائيات من الخادم
      final stats = await _uploadService.getAttachmentsStatistics();
      // جلب قائمة المرفقات من الخادم عبر AttachmentsApiService
      final attachments = await AttachmentsApiService().getAttachmentsByTask(widget.taskId); // ديناميكي
      setState(() {
        _statistics = stats;
        _serverAttachments = attachments.map((a) => a.toJson()).toList();
        _isLoadingStats = false;
      });
    } catch (e) {
      setState(() => _isLoadingStats = false);
      // إنشاء إحصائيات وهمية للاختبار
      _statistics = {
        'totalFiles': _uploadHistory.length,
        'totalSize': _uploadHistory.fold<int>(0, (sum, item) => sum + (item['fileSize'] as int? ?? 0)),
        'totalSizeFormatted': _formatFileSize(_uploadHistory.fold<int>(0, (sum, item) => sum + (item['fileSize'] as int? ?? 0))),
        'compressedFiles': _uploadHistory.where((item) => item['isCompressed'] == true).length,
        'spaceSaved': 0,
        'spaceSavedFormatted': '0 B',
        'fileTypes': _getFileTypesStats(),
      };
      debugPrint('تم إنشاء إحصائيات محلية للاختبار');
    }
  }

  /// حساب إحصائيات أنواع الملفات
  List<Map<String, dynamic>> _getFileTypesStats() {
    final Map<String, int> typeCount = {};
    
    for (final upload in _uploadHistory) {
      final fileName = upload['fileName'] as String? ?? '';
      final extension = fileName.contains('.') 
          ? fileName.split('.').last.toLowerCase()
          : 'غير محدد';
      
      typeCount[extension] = (typeCount[extension] ?? 0) + 1;
    }
    
    return typeCount.entries
        .map((entry) => {'type': entry.key, 'count': entry.value})
        .toList();
  }

  /// اختيار ورفع ملف
  Future<void> _pickAndUploadFile() async {
    try {
      // اختيار الملف
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() => _isUploading = true);

        final file = File(result.files.single.path!);
        final currentUser = _authController.currentUser.value;
        
        if (currentUser == null) {
          throw Exception('يجب تسجيل الدخول أولاً');
        }
        
        // تحديد مجلد التخزين المناسب
        final folder = _uploadService.determineStorageFolder(file);
        
        // رفع الملف مع التحسينات
        final uploadResult = await _uploadService.uploadAttachmentEnhanced(
          file,
          widget.taskId, // ديناميكي
          currentUser.id,
          folder: folder,
        );

        if (uploadResult != null) {
          setState(() {
            _lastUploadResult = uploadResult;
            _uploadHistory.insert(0, {
              'fileName': uploadResult['fileName'] ?? file.path.split('/').last,
              'fileSize': uploadResult['fileSize'] ?? file.lengthSync(),
              'isCompressed': uploadResult['isCompressed'] ?? false,
              'compressionRatio': uploadResult['compressionRatio'] ?? 0.0,
              'uploadedAt': DateTime.now(),
              'folder': uploadResult['storageFolder'] ?? folder,
              'filePath': file.path, // إضافة مسار الملف
            });
            _isUploading = false;
          });

          _showSuccessSnackBar('تم رفع الملف بنجاح!');
          
          // تحديث الإحصائيات
          _loadStatistics();
        } else {
          throw Exception('فشل في رفع الملف');
        }
      }
    } catch (e) {
      setState(() => _isUploading = false);
      _showErrorSnackBar('خطأ في رفع الملف: $e');
    }
  }

  /// تنظيف الملفات القديمة
  Future<void> _cleanupOldFiles() async {
    try {
      final result = await _uploadService.cleanupOldFiles(olderThanDays: 30);
      if (result != null) {
        _showSuccessSnackBar('تم تنظيف ${result['cleaned']} ملف');
        _loadStatistics();
      } else {
        _showSuccessSnackBar('لا توجد ملفات قديمة للتنظيف');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التنظيف: $e');
    }
  }

  /// فتح ملف باستخدام open_file
  Future<void> _openFile(String filePath) async {
    try {
      // استدعاء مكتبة open_file لفتح الملف
      await OpenFile.open(filePath);
    } catch (e) {
      _showErrorSnackBar('تعذر فتح الملف: $e');
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🗂️ اختبار نظام التخزين المحسن'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStatistics,
            tooltip: 'تحديث الإحصائيات',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            _buildSystemInfoCard(),
            const SizedBox(height: 16),
            
            // قسم الرفع
            _buildUploadSection(),
            const SizedBox(height: 16),
            
            // قسم الإحصائيات
            _buildStatisticsSection(),
            const SizedBox(height: 16),
            
            // قسم آخر رفع
            if (_lastUploadResult != null) _buildLastUploadSection(),
            const SizedBox(height: 16),
            
            // قسم تاريخ الرفع
            if (_uploadHistory.isNotEmpty) _buildUploadHistorySection(),
          ],
        ),
      ),
    );
  }

  /// بطاقة معلومات النظام
  Widget _buildSystemInfoCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  'معلومات النظام',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('🚀 الميزات:', 'نسخ احتياطي تلقائي، تحقق من السلامة، إحصائيات شاملة'),
            _buildInfoRow('📁 أنواع الملفات المدعومة:', 'الصور، المستندات، جميع الأنواع'),
            _buildInfoRow('💾 الحد الأقصى لحجم الملف:', '100 MB'),
            _buildInfoRow('🔒 الأمان:', 'تشفير SHA256، نسخ احتياطي محلي'),
          ],
        ),
      ),
    );
  }

  /// قسم الرفع
  Widget _buildUploadSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                const Icon(Icons.upload_file, color: Colors.green),
                const SizedBox(width: 8),
                const Text(
                  'رفع الملفات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isUploading ? null : _pickAndUploadFile,
              icon: _isUploading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.upload_file),
              label: Text(_isUploading ? 'جاري الرفع...' : 'اختيار ورفع ملف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed: _cleanupOldFiles,
              icon: const Icon(Icons.cleaning_services),
              label: const Text('تنظيف الملفات القديمة (30+ يوم)'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الإحصائيات
  Widget _buildStatisticsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.purple),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات التخزين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingStats)
              const Center(child: CircularProgressIndicator())
            else if (_statistics != null)
              _buildStatisticsContent()
            else
              const Text('لا توجد إحصائيات متاحة'),
          ],
        ),
      ),
    );
  }

  /// محتوى الإحصائيات
  Widget _buildStatisticsContent() {
    final stats = _statistics!;
    
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي الملفات',
                '${stats['totalFiles'] ?? 0}',
                Icons.folder,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'الحجم الإجمالي',
                stats['totalSizeFormatted'] ?? '0 B',
                Icons.storage,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'ملفات محسنة',
                '${stats['compressedFiles'] ?? 0}',
                Icons.compress,
                Colors.purple,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildStatCard(
                'مساحة موفرة',
                stats['spaceSavedFormatted'] ?? '0 B',
                Icons.save,
                Colors.orange,
              ),
            ),
          ],
        ),
        if (stats['fileTypes'] != null && (stats['fileTypes'] as List).isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'أنواع الملفات:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...List.generate(
            (stats['fileTypes'] as List).length,
            (index) {
              final fileType = (stats['fileTypes'] as List)[index];
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('📄 ${fileType['type'] ?? 'غير محدد'}'),
                    Text('${fileType['count']} ملف'),
                  ],
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// قسم آخر رفع
  Widget _buildLastUploadSection() {
    final result = _lastUploadResult!;
    final filePath = result['filePath'] ?? '';
    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  'آخر ملف تم رفعه',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('📄 اسم الملف:', result['fileName'] ?? 'غير محدد'),
            _buildInfoRow('📏 الحجم:', _formatFileSize(result['fileSize'] ?? 0)),
            _buildInfoRow('📁 المجلد:', result['storageFolder'] ?? 'غير محدد'),
            _buildInfoRow('🗜️ محسن:', result['isCompressed'] == true ? 'نعم ✅' : 'لا ❌'),
            if (result['compressionRatio'] != null && result['compressionRatio'] > 0)
              _buildInfoRow(
                '📊 نسبة التحسين:',
                '${(result['compressionRatio'] * 100).toStringAsFixed(1)}%',
              ),
            if (filePath.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: ElevatedButton.icon(
                  onPressed: () => _openFile(filePath),
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('فتح الملف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// قسم تاريخ الرفع
  Widget _buildUploadHistorySection() {
    final history = _serverAttachments.isNotEmpty ? _serverAttachments : _uploadHistory;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.history, color: Colors.indigo),
                const SizedBox(width: 8),
                const Text(
                  'تاريخ الرفع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: history.length > 5 ? 5 : history.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final upload = history[index];
                return ListTile(
                  leading: Icon(
                    upload['isCompressed'] == true ? Icons.compress : Icons.insert_drive_file,
                    color: upload['isCompressed'] == true ? Colors.green : Colors.blue,
                  ),
                  title: Text(upload['fileName'] ?? 'غير محدد'),
                  subtitle: Text(
                    '${_formatFileSize(upload['fileSize'] ?? 0)} • ${upload['folder'] ?? ''}',
                  ),
                  trailing: Text(
                    upload['uploadedAt'] != null
                      ? (upload['uploadedAt'] is DateTime
                        ? '${upload['uploadedAt'].hour}:${upload['uploadedAt'].minute.toString().padLeft(2, '0')}'
                        : upload['uploadedAt'].toString())
                      : '',
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              },
            ),
            if (history.length > 5)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'و ${history.length - 5} ملف آخر...',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}