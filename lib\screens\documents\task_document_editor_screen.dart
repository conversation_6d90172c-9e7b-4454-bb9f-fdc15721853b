import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:convert';
import 'package:flutter_quill/flutter_quill.dart';

import '../../models/task_document_model.dart';
import '../../controllers/task_documents_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../widgets/documents/advanced_quill_editor_widget.dart';

/// شاشة محرر المستندات المرتبطة بالمهام
/// تستخدم AppFlowy Editor لتوفير تجربة تحرير متقدمة
class TaskDocumentEditorScreen extends StatefulWidget {
  final TaskDocument? document;
  final int taskId;
  final TaskDocumentType? initialType;

  const TaskDocumentEditorScreen({
    Key? key,
    this.document,
    required this.taskId,
    this.initialType,
  }) : super(key: key);

  @override
  State<TaskDocumentEditorScreen> createState() => _TaskDocumentEditorScreenState();
}

class _TaskDocumentEditorScreenState extends State<TaskDocumentEditorScreen> {
  late TaskDocumentsController _documentsController;
  late AuthController _authController;
  
  // متحكمات النماذج
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  // حالة التطبيق
  TaskDocumentType _selectedType = TaskDocumentType.report;
  bool _isModified = false;
  bool _isSaving = false;
  String _currentContent = '';

  @override
  void initState() {
    super.initState();
    _documentsController = Get.find<TaskDocumentsController>();
    _authController = Get.find<AuthController>();
    
    _initializeDocument();
  }

  void _initializeDocument() {
    if (widget.document != null) {
      // تحرير مستند موجود
      _titleController.text = widget.document!.archiveDocument?.title ?? '';
      _descriptionController.text = widget.document!.description ?? '';
      _selectedType = widget.document!.type;
      _currentContent = widget.document!.archiveDocument?.content ?? '';
    } else {
      // إنشاء مستند جديد
      _selectedType = widget.initialType ?? TaskDocumentType.report;
      _titleController.text = 'مستند جديد';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      body: Column(
        children: [
          // معلومات المستند
          buildDocumentInfo(),
          
          // المحرر
          Expanded(
            child: _buildEditor(),
          ),
        ],
      ),
      
      // شريط الحالة السفلي
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// بناء شريط التطبيق
PreferredSizeWidget buildAppBar() {
    return AppBar(
      title: Text(
        widget.document != null ? 'تحرير المستند' : 'مستند جديد',
        style: AppStyles.titleLarge.copyWith(color: Colors.white),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // حفظ
        if (_isModified)
          IconButton(
            icon: _isSaving 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            onPressed: _isSaving ? null : _saveDocument,
            tooltip: 'حفظ (Ctrl+S)',
          ),
        
        // المزيد من الخيارات
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export_pdf',
              child: ListTile(
                leading: Icon(Icons.picture_as_pdf),
                title: Text('تصدير PDF'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export_markdown',
              child: ListTile(
                leading: Icon(Icons.code),
                title: Text('تصدير Markdown'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share),
                title: Text('مشاركة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            if (widget.document != null)
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('حذف', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// بناء معلومات المستند
  Widget buildDocumentInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // العنوان ونوع المستند
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'عنوان المستند',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  style: AppStyles.titleMedium,
                  onChanged: (_) => _markAsModified(),
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: DropdownButtonFormField<TaskDocumentType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المستند',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: TaskDocumentType.allTypes.map((type) =>
                    DropdownMenuItem<TaskDocumentType>(
                      value: type,
                      child: Row(
                        children: [
                          Icon(
                            _getTypeIcon(type),
                            size: 16,
                            color: _getTypeColor(type),
                          ),
                          const SizedBox(width: 8),
                          Text(type.displayName),
                        ],
                      ),
                    ),
                  ).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedType = value;
                      });
                      _markAsModified();
                    }
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // الوصف
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'وصف المستند (اختياري)',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            maxLines: 2,
            onChanged: (_) => _markAsModified(),
          ),
        ],
      ),
    );
  }

  /// بناء المحرر
  Widget _buildEditor() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: AdvancedQuillEditorWidget(
        key: ValueKey('editor_${widget.document?.archiveDocumentId ?? 'new'}_${widget.taskId}'),
        // تم إصلاح مشكلة ترتيب الأسطر - الآن الأسطر الجديدة تظهر في الأسفل بالترتيب الطبيعي
        
         initialContent: _currentContent,
        onContentChanged: (content) {
          if (mounted) {
            setState(() {
              _currentContent = content;
            });
            _markAsModified();
          }
        },
        onSave: _saveDocument,
        placeholder: 'ابدأ كتابة ${_selectedType.displayName} هنا...',
        isReadOnly: false,
        showToolbar: true,
        minHeight: 400,
        // الميزات الجديدة
        documentId: widget.document?.archiveDocumentId,
        enableAutoSave: true,
        onAutoSave: (content) {
          if (mounted) {
            setState(() {
              _currentContent = content;
            });
            _autoSaveDocument();
          }
        },
        onExport: (format, content) {
          _handleExport(format, content);
        },
      ),
    );
  }

  /// بناء الشريط السفلي
  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // معلومات الحالة
          Icon(
            _isModified ? Icons.edit : Icons.check_circle,
            size: 16,
            color: _isModified ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            _isModified ? 'تم التعديل' : 'محفوظ',
            style: AppStyles.bodySmall.copyWith(
              color: _isModified ? Colors.orange : Colors.green,
            ),
          ),
          
          const Spacer(),
          
          // عدد الكلمات
          Text(
            'عدد الكلمات: ${_getWordCount()}',
            style: AppStyles.bodySmall.copyWith(color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع المستند
  IconData _getTypeIcon(TaskDocumentType type) {
    switch (type) {
      case TaskDocumentType.report:
        return Icons.assessment;
      case TaskDocumentType.analysis:
        return Icons.analytics;
      case TaskDocumentType.plan:
        return Icons.timeline;
      case TaskDocumentType.attachment:
        return Icons.attach_file;
      case TaskDocumentType.note:
        return Icons.note;
      case TaskDocumentType.specification:
        return Icons.description;
      case TaskDocumentType.documentation:
        return Icons.book;
    }
  }

  /// الحصول على لون نوع المستند
  Color _getTypeColor(TaskDocumentType type) {
    switch (type) {
      case TaskDocumentType.report:
        return Colors.blue;
      case TaskDocumentType.analysis:
        return Colors.green;
      case TaskDocumentType.plan:
        return Colors.orange;
      case TaskDocumentType.attachment:
        return Colors.purple;
      case TaskDocumentType.note:
        return Colors.teal;
      case TaskDocumentType.specification:
        return Colors.indigo;
      case TaskDocumentType.documentation:
        return Colors.brown;
    }
  }

  /// تحديد المستند كمعدل
  void _markAsModified() {
    if (!_isModified && mounted) {
      setState(() {
        _isModified = true;
      });
    }
  }

  /// الحفظ التلقائي للمستند
  Future<void> _autoSaveDocument() async {
    if (_titleController.text.trim().isEmpty) {
      return; // لا نحفظ تلقائياً إذا لم يكن هناك عنوان
    }

    try {
      if (widget.document != null) {
        // تحديث مستند موجود تلقائياً
        await _documentsController.updateTaskDocument(
          taskId: widget.document!.taskId,
          archiveDocumentId: widget.document!.archiveDocumentId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          content: _currentContent,
          type: _selectedType,
          updatedBy: _authController.currentUser.value!.id,
        );

        debugPrint('تم الحفظ التلقائي للمستند');
      }
    } catch (e) {
      debugPrint('خطأ في الحفظ التلقائي: $e');
    }
  }

  /// معالجة التصدير
  void _handleExport(String format, String content) {
    switch (format.toLowerCase()) {
      case 'pdf':
        _showExportSuccess('PDF', content);
        break;
      case 'html':
        _showExportSuccess('HTML', content);
        break;
      case 'txt':
        _showExportSuccess('نص عادي', content);
        break;
      default:
        debugPrint('تنسيق تصدير غير مدعوم: $format');
    }
  }

  /// عرض رسالة نجاح التصدير
  void _showExportSuccess(String format, String content) {
    Get.snackbar(
      'تم التصدير',
      'تم تصدير المستند بتنسيق $format بنجاح',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      duration: const Duration(seconds: 3),
    );

    // يمكن إضافة منطق إضافي هنا مثل حفظ الملف أو مشاركته
    debugPrint('تم تصدير المستند بتنسيق $format');
  }

  /// حفظ المستند
  Future<void> _saveDocument() async {
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان المستند',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      bool success = false;

      if (widget.document != null) {
        // تحديث مستند موجود
        success = await _documentsController.updateTaskDocument(
          taskId: widget.document!.taskId,
          archiveDocumentId: widget.document!.archiveDocumentId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          content: _currentContent,
          type: _selectedType,
          updatedBy: _authController.currentUser.value!.id,
        );
      } else {
        // إنشاء مستند جديد
        final document = await _documentsController.createTaskDocument(
          taskId: widget.taskId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          type: _selectedType,
          content: _currentContent,
          isShared: false,
          createdBy: _authController.currentUser.value!.id,
        );

        success = document != null;
      }

      if (success && mounted) {
        setState(() {
          _isModified = false;
        });

        Get.snackbar(
          'تم بنجاح',
          'تم حفظ المستند بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حفظ المستند: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_pdf':
        _exportToPdf();
        break;
      case 'export_markdown':
        _exportToMarkdown();
        break;
      case 'share':
        _shareDocument();
        break;
      case 'delete':
        _deleteDocument();
        break;
    }
  }

  /// تصدير إلى PDF
  void _exportToPdf() {
    // استخدام ميزة التصدير المدمجة في المحرر
    _handleExport('pdf', _currentContent);
  }

  /// تصدير إلى Markdown
  void _exportToMarkdown() {
    // تحويل المحتوى إلى Markdown
    final markdownContent = _convertToMarkdown(_currentContent);
    _showExportSuccess('Markdown', markdownContent);
  }

  /// تحويل المحتوى إلى Markdown
  String _convertToMarkdown(String content) {
    // تحويل بسيط - يمكن تحسينه لاحقاً
    if (content.isEmpty) return '';

    // إذا كان المحتوى JSON (Quill Delta)، نحوله إلى نص عادي أولاً
    try {
      final deltaData = jsonDecode(content);
      final document = Document.fromJson(deltaData);
      final plainText = document.toPlainText();

      // تحويل بسيط إلى Markdown
      return plainText
          .split('\n')
          .map((line) => line.trim().isEmpty ? '' : line)
          .join('\n\n');
    } catch (e) {
      // إذا لم يكن JSON، نعامله كنص عادي
      return content
          .split('\n')
          .map((line) => line.trim().isEmpty ? '' : line)
          .join('\n\n');
    }
  }

  /// مشاركة المستند
  void _shareDocument() {
    if (widget.document == null) return;

    // تنفيذ بسيط لمشاركة المستند
    Get.snackbar(
      'معلومات',
      'سيتم تنفيذ مشاركة المستند قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// حذف المستند
  Future<void> _deleteDocument() async {
    if (widget.document == null) return;

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المستند "${_titleController.text}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await _documentsController.deleteTaskDocument(widget.document!);

        if (success) {
          Get.back(); // العودة للشاشة السابقة
          Get.snackbar(
            'تم بنجاح',
            'تم حذف المستند بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
          );
        } else {
          Get.snackbar(
            'خطأ',
            'فشل في حذف المستند',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف المستند: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }

  /// حساب عدد الكلمات
  int _getWordCount() {
    if (_currentContent.isEmpty) return 0;
    return _currentContent.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }
}
