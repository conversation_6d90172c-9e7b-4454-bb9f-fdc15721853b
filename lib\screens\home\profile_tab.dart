import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../models/user_model.dart';
import '../../screens/settings/language_settings_screen.dart';
import '../../screens/settings/change_password_screen.dart';
import '../../screens/settings/edit_profile_screen.dart';
import '../../screens/settings/help_support_screen.dart';
import '../../screens/settings/terms_conditions_screen.dart';
import '../../screens/settings/privacy_policy_screen.dart';
import '../../routes/app_routes.dart';
import '../../utils/image_helper.dart';

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    final themeController = Get.find<ThemeController>();

    return Obx(() {
      if (authController.currentUser.value == null) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      final user = authController.currentUser.value!;

    return RefreshIndicator(
      onRefresh: () async {
        // Refresh user profile from API
        await authController.loadCurrentUser();
      },
      child: SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header
          Center(
            child: Column(
              children: [
                // Profile image
                _buildProfileImage(user),
                const SizedBox(height: 16),

                // User name
                Text(
                  user.name,
                  style: AppStyles.headingMedium,
                ),
                const SizedBox(height: 4),

                // User role
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(33, 150, 243, 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getRoleText(user.role),
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // User information
          Text(
            'personalInfo'.tr,
            style: AppStyles.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildInfoItem(Icons.email, 'email'.tr, user.email),
          _buildInfoItem(
            Icons.business,
            'department'.tr,
            user.departmentName ?? 'notAssigned'.tr,
          ),
          _buildInfoItem(
            Icons.person_outline,
            'role'.tr,
            user.roleName,
          ),
          _buildStatusItem(
            Icons.verified_user,
            'status'.tr,
            user.isActive ? 'active'.tr : 'inactive'.tr,
            user.isActive,
          ),
          _buildStatusItem(
            Icons.circle,
            'onlineStatus'.tr,
            user.isOnline ? 'online'.tr : 'offline'.tr,
            user.isOnline,
          ),
          const SizedBox(height: 32),

          // Settings
          Text(
            'settings'.tr,
            style: AppStyles.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSettingsItem(
            Icons.dark_mode,
            'theme'.tr,
            trailing: Obx(() => Switch(
                  value: themeController.isDarkModeRx.value,
                  onChanged: (value) {
                    themeController.toggleTheme();
                  },
                  activeColor: AppColors.primary,
                )),
          ),
          _buildSettingsItem(
            Icons.language,
            'language'.tr,
            onTap: () {
              Get.to(() => const LanguageSettingsScreen());
            },
          ),
          _buildSettingsItem(
            Icons.lock,
            'changePassword'.tr,
            onTap: () {
              Get.to(() => const ChangePasswordScreen());
            },
          ),
          _buildSettingsItem(
            Icons.edit,
            'editProfile'.tr,
            onTap: () {
              Get.to(() => const EditProfileScreen());
            },
          ),
          _buildSettingsItem(
            Icons.dashboard_customize,
            'لوحة تحكم المستخدم',
            onTap: () {
              Get.toNamed('/user-dashboard');
            },
          ),
          _buildSettingsItem(
            Icons.build_circle,
            'إصلاح قاعدة البيانات',
            onTap: () {
              Get.toNamed(AppRoutes.databaseRepair);
            },
          ),
          const SizedBox(height: 32),

          // About
          Text(
            'about'.tr,
            style: AppStyles.titleLarge,
          ),
          const SizedBox(height: 16),
          _buildSettingsItem(
            Icons.info_outline,
            'About App',
            onTap: () {
              _showAboutDialog(context);
            },
          ),
          _buildSettingsItem(
            Icons.help_outline,
            'Help & Support',
            onTap: () {
              Get.to(() => const HelpSupportScreen());
            },
          ),
          _buildSettingsItem(
            Icons.description_outlined,
            'Terms & Conditions',
            onTap: () {
              Get.to(() => const TermsConditionsScreen());
            },
          ),
          _buildSettingsItem(
            Icons.privacy_tip_outlined,
            'Privacy Policy',
            onTap: () {
              Get.to(() => const PrivacyPolicyScreen());
            },
          ),
          const SizedBox(height: 32),

          // Logout button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Logout'),
                    content: const Text('Are you sure you want to logout?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Logout'),
                      ),
                    ],
                  ),
                );

                if (confirmed == true) {
                  authController.logout();
                }
              },
              icon: const Icon(Icons.logout),
              label: const Text('Logout'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Get.isDarkMode ? Colors.black : Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 32),
        ],
      ),
      ),
    );
    });
  }

  /// Builds the profile image widget with proper error handling
  Widget _buildProfileImage(UserInfo user) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ImageHelper.buildProfileImage(
        imagePath: user.profileImage,
        radius: 50,
        fallbackText: user.name,
        backgroundColor: AppColors.primary.withValues(alpha: 0.1),
        textColor: AppColors.primary,
        fallbackIcon: Icons.person,
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppStyles.labelMedium,
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppStyles.bodyMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds a status item with colored indicator
  Widget _buildStatusItem(IconData icon, String label, String value, bool isPositive) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppStyles.labelMedium,
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isPositive ? Colors.green : Colors.red,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      value,
                      style: AppStyles.bodyMedium.copyWith(
                        color: isPositive ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem(
    IconData icon,
    String label, {
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
      ),
      title: Text(label),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      contentPadding: EdgeInsets.zero,
      onTap: onTap,
    );
  }

  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'مدير النظام العام';
      case UserRole.admin:
        return 'مدير عام';
      case UserRole.manager:
        return 'مدير';
      case UserRole.supervisor:
        return 'مشرف';
      case UserRole.user:
        return 'مستخدم';
    }
  }

  /// Shows an about dialog with app information
  Future<void> _showAboutDialog(BuildContext context) async {
    // Get package info
    final packageInfo = await PackageInfo.fromPlatform();

    // Check if the widget is still mounted before showing the dialog
    if (!context.mounted) return;

    showAboutDialog(
      context: context,
      applicationName: AppStrings.appName,
      applicationVersion:
          'Version ${packageInfo.version} (Build ${packageInfo.buildNumber})',
      applicationIcon: const Icon(
        Icons.task_alt,
        size: 50,
        color: AppColors.primary,
      ),
      applicationLegalese: '© ${DateTime.now().year} Your Company Name',
      children: [
        const SizedBox(height: 16),
        const Text(
          'Task management system is a fully Arabized application designed to help teams manage tasks efficiently. '
          'It provides features for task assignment, tracking, and collaboration.',
          style: TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 16),
        const Text(
          'Features:',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        _buildFeatureItem('User management and role-based access'),
        _buildFeatureItem('Task assignment and tracking'),
        _buildFeatureItem('File attachments and comments'),
        _buildFeatureItem('Real-time notifications'),
        _buildFeatureItem('Dashboard with analytics'),
        _buildFeatureItem('Team messaging and collaboration'),
      ],
    );
  }

  /// Builds a feature item for the about dialog
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontSize: 14)),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
