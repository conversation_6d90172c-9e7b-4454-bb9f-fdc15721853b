import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:path/path.dart' as path;
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

import '../../constants/app_colors.dart';
import '../../services/download_service.dart';

/// شاشة عرض الملفات المحسنة
/// توفر واجهة متقدمة لعرض الملفات المختلفة
class EnhancedFileViewerScreen extends StatefulWidget {
  /// مسار الملف
  final String filePath;

  /// اسم الملف (اختياري)
  final String? fileName;

  /// نوع الملف (اختياري)
  final String? mimeType;

  /// المرفق (اختياري)
  final Attachment? attachment;

  const EnhancedFileViewerScreen({
    super.key,
    required this.filePath,
    this.fileName,
    this.mimeType,
    this.attachment,
  });

  @override
  State<EnhancedFileViewerScreen> createState() => _EnhancedFileViewerScreenState();
}

class _EnhancedFileViewerScreenState extends State<EnhancedFileViewerScreen> {
  final DownloadService _downloadService = DownloadService();

  // حالة التحميل
  bool _isLoading = true;
  String _errorMessage = '';

  // معلومات الملف
  late String _fileName;
  late String _fileExtension;
  late FileType _fileType;

  // متحكمات العرض
  final PdfViewerController _pdfController = PdfViewerController();
  // استخدام TransformationController بدلاً من SfImageViewerController
  final TransformationController _imageController = TransformationController();

  // حالة التنزيل
  bool _isDownloading = false;
  double _downloadProgress = 0;

  @override
  void initState() {
    super.initState();
    _initFileInfo();
  }

  // تهيئة معلومات الملف
  void _initFileInfo() {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // تحديد اسم الملف
      _fileName = widget.fileName ?? path.basename(widget.filePath);

      // تحديد امتداد الملف
      _fileExtension = path.extension(widget.filePath).toLowerCase();

      // تحديد نوع الملف
      if (_fileExtension == '.pdf') {
        _fileType = FileType.pdf;
      } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(_fileExtension)) {
        _fileType = FileType.image;
      } else if (['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].contains(_fileExtension)) {
        _fileType = FileType.office;
      } else if (['.txt', '.csv', '.json', '.xml', '.html', '.md'].contains(_fileExtension)) {
        _fileType = FileType.text;
      } else {
        _fileType = FileType.other;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ أثناء تحميل معلومات الملف: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_fileName),
        actions: [
          // زر التنزيل
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تنزيل الملف',
            onPressed: _isDownloading ? null : _downloadFile,
          ),
          // زر المشاركة
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'مشاركة الملف',
            onPressed: _shareFile,
          ),
          // قائمة الخيارات
          PopupMenuButton<String>(
            onSelected: _handleMenuItemSelected,
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'print',
                child: Row(
                  children: [
                    Icon(Icons.print),
                    SizedBox(width: 8),
                    Text('طباعة'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'info',
                child: Row(
                  children: [
                    Icon(Icons.info),
                    SizedBox(width: 8),
                    Text('معلومات الملف'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? _buildErrorWidget()
              : _buildFileViewer(),
      bottomNavigationBar: _isDownloading ? _buildDownloadProgressBar() : null,
    );
  }

  // بناء عارض الملفات
  Widget _buildFileViewer() {
    switch (_fileType) {
      case FileType.pdf:
        return _buildPdfViewer();
      case FileType.image:
        return _buildImageViewer();
      case FileType.office:
        return _buildOfficeViewer();
      case FileType.text:
        return _buildTextViewer();
      case FileType.other:
        return _buildGenericViewer();
    }
  }

  // بناء عارض ملفات PDF
  Widget _buildPdfViewer() {
    return Stack(
      children: [
        SfPdfViewer.file(
          File(widget.filePath),
          controller: _pdfController,
          enableDoubleTapZooming: true,
          enableTextSelection: true,
          onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
            setState(() {
              _errorMessage = 'فشل تحميل ملف PDF: ${details.error}';
            });
          },
        ),
        Positioned(
          bottom: 16,
          right: 16,
          child: Row(
            children: [
              FloatingActionButton(
                heroTag: 'zoom_out',
                mini: true,
                onPressed: () {
                  _pdfController.zoomLevel = (_pdfController.zoomLevel - 0.25).clamp(0.75, 3.0);
                },
                child: const Icon(Icons.zoom_out),
              ),
              const SizedBox(width: 8),
              FloatingActionButton(
                heroTag: 'zoom_in',
                mini: true,
                onPressed: () {
                  _pdfController.zoomLevel = (_pdfController.zoomLevel + 0.25).clamp(0.75, 3.0);
                },
                child: const Icon(Icons.zoom_in),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // بناء عارض الصور
  Widget _buildImageViewer() {
    return InteractiveViewer(
      transformationController: _imageController,
      minScale: 0.5,
      maxScale: 4.0,
      boundaryMargin: const EdgeInsets.all(20.0),
      child: Center(
        child: Image.file(
          File(widget.filePath),
          errorBuilder: (context, error, stackTrace) {
            setState(() {
              _errorMessage = 'فشل تحميل الصورة: $error';
            });
            return const Icon(Icons.error, size: 48, color: Colors.red);
          },
        ),
      ),
    );
  }

  // بناء عارض ملفات Office
  Widget _buildOfficeViewer() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(),
            size: 72,
            color: _getFileColor(),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يمكن عرض ملفات $_fileExtension مباشرة',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: const Text('تنزيل الملف'),
          ),
        ],
      ),
    );
  }

  // بناء عارض الملفات النصية
  Widget _buildTextViewer() {
    return FutureBuilder<String>(
      future: File(widget.filePath).readAsString(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(
            child: Text('فشل قراءة الملف: ${snapshot.error}'),
          );
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text('الملف فارغ'),
          );
        } else {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              snapshot.data!,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
              ),
            ),
          );
        }
      },
    );
  }

  // بناء عارض عام للملفات
  Widget _buildGenericViewer() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(),
            size: 72,
            color: _getFileColor(),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يمكن عرض ملفات $_fileExtension مباشرة',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: const Text('تنزيل الملف'),
          ),
        ],
      ),
    );
  }

  // بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initFileInfo,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  // بناء شريط تقدم التنزيل
  Widget _buildDownloadProgressBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LinearProgressIndicator(
            value: _downloadProgress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          const SizedBox(height: 8),
          Text(
            'جاري التنزيل... ${(_downloadProgress * 100).toStringAsFixed(0)}%',
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  // تنزيل الملف
  Future<void> _downloadFile() async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0;
    });

    try {
      // إذا كان الملف مرفق، استخدم downloadAttachment
      if (widget.attachment != null) {
        final success = await _downloadService.downloadAttachment(
          widget.attachment!,
          onProgress: (received, total) {
            if (total > 0) {
              setState(() {
                _downloadProgress = received / total;
              });
            }
          },
        );

        if (success) {
          setState(() {
            _isDownloading = false;
          });

          Get.snackbar(
            'تم التنزيل',
            'تم تنزيل الملف بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          throw Exception('فشل في تنزيل الملف');
        }
      } else {
        // للملفات المحلية، نسخ الملف إلى مجلد التنزيلات
        final file = File(widget.filePath);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          final success = await _downloadService.saveDataAsFile(
            data: bytes,
            fileName: _fileName,
          );

          if (success) {
            setState(() {
              _isDownloading = false;
            });

            Get.snackbar(
              'تم التنزيل',
              'تم تنزيل الملف بنجاح',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green,
              colorText: Colors.white,
            );
          } else {
            throw Exception('فشل في حفظ الملف');
          }
        } else {
          throw Exception('الملف غير موجود');
        }
      }
    } catch (e) {
      setState(() {
        _isDownloading = false;
      });

      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تنزيل الملف: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // مشاركة الملف
  Future<void> _shareFile() async {
    try {
      final success = await _downloadService.shareFile(widget.filePath);
      if (!success) {
        throw Exception('فشل في مشاركة الملف');
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء مشاركة الملف: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // معالجة اختيار عنصر القائمة
  void _handleMenuItemSelected(String value) {
    switch (value) {
      case 'print':
        _printFile();
        break;
      case 'info':
        _showFileInfo();
        break;
    }
  }

  // طباعة الملف
  Future<void> _printFile() async {
    if (_fileType == FileType.pdf) {
      try {
        final file = File(widget.filePath);
        if (await file.exists()) {
          final bytes = await file.readAsBytes();
          await Printing.layoutPdf(
            onLayout: (format) => bytes,
            name: _fileName,
            format: PdfPageFormat.a4,
          );
        } else {
          throw Exception('الملف غير موجود');
        }
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء طباعة الملف: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } else {
      Get.snackbar(
        'غير مدعوم',
        'طباعة هذا النوع من الملفات غير مدعومة حاليًا',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  // عرض معلومات الملف
  void _showFileInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات الملف'),
        content: FutureBuilder<FileStat>(
          future: File(widget.filePath).stat(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Text('فشل الحصول على معلومات الملف: ${snapshot.error}');
            } else {
              final stat = snapshot.data!;
              final size = _formatFileSize(stat.size);
              final modified = _formatDate(stat.modified);

              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('اسم الملف', _fileName),
                  _buildInfoRow('النوع', _fileExtension),
                  _buildInfoRow('الحجم', size),
                  _buildInfoRow('آخر تعديل', modified),
                  if (widget.attachment != null) ...[
                    _buildInfoRow('تم الرفع بواسطة', widget.attachment!.uploadedByUser?.name ?? 'المستخدم ${widget.attachment!.uploadedBy}'),
                    _buildInfoRow('تاريخ الرفع', _formatDate(widget.attachment!.uploadedAtDateTime)),
                  ],
                ],
              );
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // الحصول على أيقونة الملف
  IconData _getFileIcon() {
    switch (_fileType) {
      case FileType.pdf:
        return Icons.picture_as_pdf;
      case FileType.image:
        return Icons.image;
      case FileType.office:
        if (['.doc', '.docx'].contains(_fileExtension)) {
          return Icons.description;
        } else if (['.xls', '.xlsx'].contains(_fileExtension)) {
          return Icons.table_chart;
        } else if (['.ppt', '.pptx'].contains(_fileExtension)) {
          return Icons.slideshow;
        }
        return Icons.insert_drive_file;
      case FileType.text:
        return Icons.text_snippet;
      case FileType.other:
        return Icons.insert_drive_file;
    }
  }

  // الحصول على لون الملف
  Color _getFileColor() {
    switch (_fileType) {
      case FileType.pdf:
        return Colors.red;
      case FileType.image:
        return Colors.blue;
      case FileType.office:
        if (['.doc', '.docx'].contains(_fileExtension)) {
          return Colors.blue;
        } else if (['.xls', '.xlsx'].contains(_fileExtension)) {
          return Colors.green;
        } else if (['.ppt', '.pptx'].contains(_fileExtension)) {
          return Colors.orange;
        }
        return Colors.grey;
      case FileType.text:
        return Colors.teal;
      case FileType.other:
        return Colors.grey;
    }
  }

  // تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} كيلوبايت';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
    }
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }
}

/// أنواع الملفات
enum FileType {
  pdf,
  image,
  office,
  text,
  other,
}
