import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../models/calendar_models.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;
import 'calendar_event_form.dart';

/// شاشة التقويم
/// تعرض تقويمًا تفاعليًا مع أحداث المستخدم
class CalendarScreen extends StatefulWidget {
  const CalendarScreen({super.key});

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  late app_calendar.CalendarController _calendarController;

  // تاريخ التركيز الحالي
  late DateTime _focusedDay;

  // التاريخ المحدد
  late DateTime _selectedDay;

  // تنسيق التقويم
  CalendarFormat _calendarFormat = CalendarFormat.month;

  // نص البحث
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // تهيئة متغيرات التقويم
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();

    // الحصول على وحدة تحكم التقويم أو إنشاؤها
    try {
      _calendarController = Get.find<app_calendar.CalendarController>();
    } catch (e) {
      _calendarController = Get.put(app_calendar.CalendarController());
    }

    // تحميل الأحداث عند تهيئة الشاشة
    _loadEvents();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // تحميل الأحداث
  Future<void> _loadEvents() async {
    await _calendarController.loadEvents();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقويم'),
        elevation: 0,
        backgroundColor: AppColors.primary,
        actions: [
          // زر تغيير نوع العرض
          IconButton(
            icon: const Icon(Icons.view_agenda),
            tooltip: 'تغيير نوع العرض',
            onPressed: _showViewTypeMenu,
          ),
          // زر المزامنة
          IconButton(
            icon: const Icon(Icons.sync),
            tooltip: 'مزامنة المهام',
            onPressed: _syncTasksToEvents,
          ),
          // زر إضافة حدث جديد
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'إضافة حدث جديد',
            onPressed: _showAddEventDialog,
          ),
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            tooltip: 'بحث في الأحداث',
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات المصغرة
          _buildStatsBar(),

          // جسم التقويم
          Expanded(
            child: GetBuilder<app_calendar.CalendarController>(
              builder: (controller) {
                // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
                if (controller.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                // عرض رسالة الخطأ إذا كان هناك خطأ
                if (controller.error.isNotEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 48, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          controller.error,
                          style: const TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadEvents,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                // عرض التقويم
                return Column(
                  children: [
                    // رأس التقويم مع أزرار التنقل
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // زر تغيير تنسيق التقويم
                          TextButton(
                            onPressed: () {
                              setState(() {
                                if (_calendarFormat == CalendarFormat.month) {
                                  _calendarFormat = CalendarFormat.week;
                                } else if (_calendarFormat == CalendarFormat.week) {
                                  _calendarFormat = CalendarFormat.twoWeeks;
                                } else {
                                  _calendarFormat = CalendarFormat.month;
                                }
                              });
                            },
                            child: Text(
                              _calendarFormat == CalendarFormat.month
                                  ? 'شهر'
                                  : _calendarFormat == CalendarFormat.week
                                      ? 'أسبوع'
                                      : 'أسبوعين',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                          // زر العودة إلى اليوم الحالي
                          TextButton(
                            onPressed: () {
                              setState(() {
                                _focusedDay = DateTime.now();
                                _selectedDay = DateTime.now();
                              });
                            },
                            child: Text(
                              'اليوم',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // التقويم
                    TableCalendar<CalendarEvent>(
                      firstDay: DateTime.now().subtract(const Duration(days: 365 * 2)),
                      lastDay: DateTime.now().add(const Duration(days: 365 * 2)),
                      focusedDay: _focusedDay,
                      calendarFormat: _calendarFormat,
                      startingDayOfWeek: StartingDayOfWeek.saturday, // السبت هو أول يوم في الأسبوع
                      selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                      headerStyle: HeaderStyle(
                        titleCentered: true,
                        formatButtonVisible: false,
                        leftChevronIcon: const Icon(Icons.chevron_left, size: 24),
                        rightChevronIcon: const Icon(Icons.chevron_right, size: 24),
                        titleTextStyle: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      daysOfWeekStyle: const DaysOfWeekStyle(
                        weekdayStyle: TextStyle(fontSize: 14),
                        weekendStyle: TextStyle(fontSize: 14, color: Colors.red),
                      ),
                      calendarStyle: CalendarStyle(
                        outsideDaysVisible: true,
                        // تحسين مظهر اليوم الحالي
                        todayDecoration: BoxDecoration(
                          color: Theme.of(context).primaryColor.withAlpha(40),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Theme.of(context).primaryColor,
                            width: 1.5,
                          ),
                        ),
                        todayTextStyle: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        // تحسين مظهر اليوم المحدد
                        selectedDecoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).primaryColor.withAlpha(100),
                              blurRadius: 4,
                              spreadRadius: 1,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        selectedTextStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        // تحسين مظهر المؤشرات
                        markerDecoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.secondary,
                          shape: BoxShape.circle,
                        ),
                        markersMaxCount: 3,
                        cellMargin: const EdgeInsets.all(6), // زيادة الهامش لمزيد من المساحة
                        weekendTextStyle: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                        // إضافة تأثير عند تمرير المؤشر فوق اليوم
                        holidayDecoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.amber,
                            width: 1.5,
                          ),
                          shape: BoxShape.circle,
                        ),
                      ),
                      onDaySelected: (selectedDay, focusedDay) {
                        // عند النقر على يوم في التقويم، يتم تحديثه وعرض أحداثه في القائمة أدناه فقط
                        // دون الانتقال تلقائيًا إلى شاشة إضافة حدث جديد
                        setState(() {
                          _selectedDay = selectedDay;
                          _focusedDay = focusedDay;
                        });
                        // قائمة الأحداث ستتحدث تلقائيًا بسبب إعادة البناء بعد تغيير الحالة
                      },
                      onPageChanged: (focusedDay) {
                        setState(() {
                          _focusedDay = focusedDay;
                        });

                        // تحميل الأحداث
                        _calendarController.loadEvents();
                      },
                      eventLoader: (day) {
                        // استخدام وحدة تحكم التقويم للحصول على أحداث اليوم
                        final normalizedDay = DateTime(day.year, day.month, day.day);
                        try {
                          final events = controller.getEventsForDay(normalizedDay);
                          return events;
                        } catch (e) {
                          debugPrint('خطأ في تحميل أحداث اليوم: $e');
                          return [];
                        }
                      },
                      // إضافة بناة مخصصة للتقويم لإضافة التلميحات
                      calendarBuilders: CalendarBuilders(
                        // بناء خلية اليوم العادي مع تلميح للأحداث
                        defaultBuilder: (context, day, focusedDay) {
                          final events = controller.getEventsForDay(day);

                          // إنشاء ويدجت اليوم العادي مع تحسين المظهر
                          final defaultCell = AnimatedContainer(
                            duration: const Duration(milliseconds: 250),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.transparent,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '${day.day}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          );

                          // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                          if (events.isEmpty) {
                            return defaultCell;
                          }

                          // إنشاء ويدجت لعرض اليوم مع تلميح محسن
                          return Tooltip(
                            message: _buildEventsTooltipText(events),
                            preferBelow: false,
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.all(10),
                            showDuration: const Duration(seconds: 3),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(70),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context).primaryColor.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            textStyle: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 13,
                              height: 1.4,
                            ),
                            child: Stack(
                              alignment: Alignment.bottomCenter,
                              children: [
                                defaultCell,
                                // إضافة مؤشر صغير لإظهار وجود أحداث
                                if (events.length > 3)
                                  Positioned(
                                    bottom: 2,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        '+${events.length}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                        // بناء خلية اليوم المحدد مع تلميح للأحداث
                        selectedBuilder: (context, day, focusedDay) {
                          final events = controller.getEventsForDay(day);

                          // إنشاء ويدجت اليوم المحدد مع تحسين المظهر
                          final selectedCell = AnimatedContainer(
                            duration: const Duration(milliseconds: 250),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Theme.of(context).primaryColor,
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).primaryColor.withAlpha(100),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                '${day.day}',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          );

                          // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                          if (events.isEmpty) {
                            return selectedCell;
                          }

                          // إنشاء ويدجت لعرض اليوم المحدد مع تلميح محسن
                          return Tooltip(
                            message: _buildEventsTooltipText(events),
                            preferBelow: false,
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.all(10),
                            showDuration: const Duration(seconds: 3),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(70),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context).primaryColor.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            textStyle: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 13,
                              height: 1.4,
                            ),
                            child: Stack(
                              alignment: Alignment.bottomCenter,
                              children: [
                                selectedCell,
                                // إضافة مؤشر صغير لإظهار وجود أحداث
                                if (events.length > 3)
                                  Positioned(
                                    bottom: 2,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        '+${events.length}',
                                        style: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                          fontSize: 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                        // بناء خلية اليوم الحالي مع تلميح للأحداث
                        todayBuilder: (context, day, focusedDay) {
                          final events = controller.getEventsForDay(day);

                          // إنشاء ويدجت اليوم الحالي مع تحسين المظهر
                          final todayCell = AnimatedContainer(
                            duration: const Duration(milliseconds: 250),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Theme.of(context).primaryColor.withAlpha(40),
                              border: Border.all(
                                color: Theme.of(context).primaryColor,
                                width: 1.5,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                '${day.day}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                          );

                          // إذا لم تكن هناك أحداث، عرض اليوم بشكل عادي
                          if (events.isEmpty) {
                            return todayCell;
                          }

                          // إنشاء ويدجت لعرض اليوم الحالي مع تلميح محسن
                          return Tooltip(
                            message: _buildEventsTooltipText(events),
                            preferBelow: false,
                            padding: const EdgeInsets.all(12),
                            margin: const EdgeInsets.all(10),
                            showDuration: const Duration(seconds: 3),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(70),
                                  blurRadius: 6,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context).primaryColor.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            textStyle: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 13,
                              height: 1.4,
                            ),
                            child: Stack(
                              alignment: Alignment.bottomCenter,
                              children: [
                                todayCell,
                                // إضافة مؤشر صغير لإظهار وجود أحداث
                                if (events.length > 3)
                                  Positioned(
                                    bottom: 2,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        '+${events.length}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                        // بناء المؤشرات
                        markerBuilder: (context, date, events) {
                          if (events.isEmpty) return null;

                          return Positioned(
                            bottom: 1,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: List.generate(
                                events.length > 3 ? 3 : events.length,
                                (index) {
                                  final event = events[index];
                                  return Container(
                                    margin: const EdgeInsets.symmetric(horizontal: 0.5),
                                    width: 6,
                                    height: 6,
                                    decoration: BoxDecoration(
                                      color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
                                      shape: BoxShape.circle,
                                    ),
                                  );
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // قائمة الأحداث لليوم المحدد
                    Expanded(
                      child: _buildEventsList(controller.getEventsForDay(_selectedDay)),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddEventDialog,
        tooltip: 'إضافة حدث جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  // عرض قائمة تغيير نوع العرض
  void _showViewTypeMenu() {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(button.size.bottomRight(Offset.zero), ancestor: overlay),
      ),
      Offset.zero & overlay.size,
    );

    showMenu<CalendarFormat>(
      context: context,
      position: position,
      items: [
        const PopupMenuItem<CalendarFormat>(
          value: CalendarFormat.month,
          child: Text('عرض الشهر'),
        ),
        const PopupMenuItem<CalendarFormat>(
          value: CalendarFormat.twoWeeks,
          child: Text('عرض أسبوعين'),
        ),
        const PopupMenuItem<CalendarFormat>(
          value: CalendarFormat.week,
          child: Text('عرض الأسبوع'),
        ),
      ],
    ).then((CalendarFormat? value) {
      if (value != null) {
        setState(() {
          _calendarFormat = value;
        });
      }
    });
  }

  // مزامنة المهام مع أحداث التقويم
  Future<void> _syncTasksToEvents() async {
    try {
      // تحديث الأحداث
      await _calendarController.loadEvents();
      Get.snackbar(
        'تمت المزامنة',
        'تم تحديث أحداث التقويم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء مزامنة المهام: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // عرض نموذج إضافة حدث جديد
  void _showAddEventDialog() {
    Get.to(() => CalendarEventForm(
      onEventCreated: (event) {
        _loadEvents();
      },
    ));
  }



  // عرض تفاصيل الحدث
  void _showEventDetails(int eventId) async {
    final event = _calendarController.events.firstWhere(
      (e) => e.id == eventId,
      orElse: () => _calendarController.events.first,
    );
    if (event.id == eventId) {
      Get.to(() => CalendarEventForm(
        event: event,
        onEventUpdated: (updatedEvent) {
          _loadEvents();
        },
      ));
    } else {
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على الحدث',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }



  // الحصول على لون حسب نوع الحدث
  Color _getEventTypeColor(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return Colors.orange;
      case CalendarEventType.reminder:
        return Colors.purple;
      case CalendarEventType.vacation:
        return Colors.green;
      case CalendarEventType.other:
        return Colors.grey;
      default:
        return AppColors.primary;
    }
  }

  // الحصول على أيقونة حسب نوع الحدث
  IconData _getEventTypeIcon(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Icons.task;
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.reminder:
        return Icons.alarm;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
        return Icons.event;
      default:
        return Icons.event;
    }
  }

  /// بناء شريط الإحصائيات
  Widget _buildStatsBar() {
    return GetBuilder<app_calendar.CalendarController>(
      builder: (controller) {
        // حساب إحصائيات الأحداث
        final allEvents = controller.events;
        final todayEvents = controller.getEventsForDay(DateTime.now());
        final weekEvents = _getEventsForWeek(controller.events, DateTime.now());

        return Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(30),
            border: Border(
              bottom: BorderSide(
                color: AppColors.primary.withAlpha(100),
                width: 1,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'الكل',
                allEvents.length.toString(),
                Icons.event,
                Colors.blue,
              ),
              _buildStatItem(
                'اليوم',
                todayEvents.length.toString(),
                Icons.today,
                Colors.green,
              ),
              _buildStatItem(
                'الأسبوع',
                weekEvents.length.toString(),
                Icons.date_range,
                Colors.orange,
              ),
              _buildStatItem(
                'المهام',
                allEvents.where((e) => e.eventType == CalendarEventType.task).length.toString(),
                Icons.task,
                Colors.purple,
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String title, String count, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(30),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          count,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: color,
          ),
        ),
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  /// عرض مربع حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('بحث في الأحداث'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'عنوان الحدث',
                hintText: 'أدخل كلمات البحث',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // تنفيذ البحث
                final searchText = _searchController.text.trim();
                if (searchText.isNotEmpty) {
                  final results = _calendarController.events
                      .where((event) => event.title.contains(searchText))
                      .toList();

                  // إغلاق مربع الحوار
                  Navigator.pop(context);

                  // عرض نتائج البحث
                  _showSearchResults(results, searchText);
                }
              },
              child: const Text('بحث'),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض نتائج البحث
  void _showSearchResults(List<CalendarEvent> results, String searchText) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'نتائج البحث عن: $searchText',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),
            Expanded(
              child: results.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد نتائج',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: results.length,
                      itemBuilder: (context, index) {
                        final event = results[index];
                        return ListTile(
                          leading: Container(
                            width: 8,
                            decoration: BoxDecoration(
                              color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          title: Text(event.title),
                          subtitle: Text(
                            '${_formatDate(event.startTime)} - ${_formatTime(event.startTime)}',
                          ),
                          onTap: () {
                            // إغلاق مربع الحوار
                            Navigator.pop(context);

                            // عرض تفاصيل الحدث
                            _showEventDetails(event.id);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  /// تنسيق الوقت
  String _formatTime(int timestamp) {
    final time = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// تحويل النص إلى لون
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على أحداث الأسبوع
  List<CalendarEvent> _getEventsForWeek(List<CalendarEvent> events, DateTime date) {
    final startOfWeek = date.subtract(Duration(days: date.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return events.where((event) {
      final eventDate = DateTime.fromMillisecondsSinceEpoch(event.startTime * 1000);
      return eventDate.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
             eventDate.isBefore(endOfWeek.add(const Duration(days: 1)));
    }).toList();
  }

  /// بناء نص التلميح للأحداث
  String _buildEventsTooltipText(List<CalendarEvent> events) {
    if (events.isEmpty) return '';

    // تنظيم الأحداث حسب النوع
    final Map<CalendarEventType, List<CalendarEvent>> eventsByType = {};

    for (final event in events) {
      if (!eventsByType.containsKey(event.eventType)) {
        eventsByType[event.eventType] = [];
      }
      eventsByType[event.eventType]!.add(event);
    }

    // بناء نص التلميح
    final StringBuffer tooltipText = StringBuffer();

    // إضافة عدد الأحداث الإجمالي
    tooltipText.writeln('${events.length} أحداث:');
    tooltipText.writeln('');

    // إضافة تفاصيل الأحداث حسب النوع
    eventsByType.forEach((type, typeEvents) {
      // إضافة نوع الحدث
      switch (type) {
        case CalendarEventType.task:
          tooltipText.writeln('🔷 مهام (${typeEvents.length}):');
          break;
        case CalendarEventType.meeting:
          tooltipText.writeln('🔶 اجتماعات (${typeEvents.length}):');
          break;
        case CalendarEventType.reminder:
          tooltipText.writeln('🔔 تذكيرات (${typeEvents.length}):');
          break;
        case CalendarEventType.vacation:
          tooltipText.writeln('🏖️ إجازات (${typeEvents.length}):');
          break;
        case CalendarEventType.other:
          tooltipText.writeln('📌 أخرى (${typeEvents.length}):');
          break;
      }

      // إضافة تفاصيل الأحداث من هذا النوع (بحد أقصى 3 أحداث لكل نوع)
      for (int i = 0; i < typeEvents.length && i < 3; i++) {
        final event = typeEvents[i];
        final timeFormat = DateFormat('h:mm a');
        final eventDateTime = DateTime.fromMillisecondsSinceEpoch(event.startTime * 1000);
        final startTime = timeFormat.format(eventDateTime);
        tooltipText.writeln('- ${event.title} ($startTime)');
      }

      // إذا كان هناك المزيد من الأحداث، إضافة إشارة إلى ذلك
      if (typeEvents.length > 3) {
        tooltipText.writeln('  ... و${typeEvents.length - 3} أخرى');
      }

      tooltipText.writeln('');
    });

    return tooltipText.toString().trim();
  }

  /// بناء قائمة الأحداث
  Widget _buildEventsList(List<CalendarEvent> events) {
    if (events.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد أحداث لهذا اليوم',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: events.length,
      padding: const EdgeInsets.all(8),
      itemBuilder: (context, index) {
        final event = events[index];
        return Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
              width: 1,
            ),
          ),
          child: ListTile(
            leading: Container(
              width: 4,
              decoration: BoxDecoration(
                color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            title: Text(
              event.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${_formatDate(event.startTime)} - ${_formatTime(event.startTime)}',
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getEventTypeIcon(event.eventType),
                  color: _parseColor(event.color) ?? _getEventTypeColor(event.eventType),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  onPressed: () {
                    Get.to(() => CalendarEventForm(
                      event: event,
                      onEventUpdated: (updatedEvent) {
                        _loadEvents();
                      },
                    ));
                  },
                ),
              ],
            ),
            onTap: () {
              _showEventDetails(event.id);
            },
          ),
        );
      },
    );
  }
}
