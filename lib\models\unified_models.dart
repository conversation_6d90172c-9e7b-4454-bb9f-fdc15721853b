/// ملف تصدير موحد لجميع النماذج
/// يسهل استيراد وإدارة جميع النماذج من مكان واحد
/// 
/// تم توحيد وتحسين جميع النماذج لتتطابق 100% مع الباك اند

// ===== النماذج الأساسية =====
export 'user_model.dart';
export 'role_model.dart';
export 'department_model.dart';

// ===== نماذج الصلاحيات والأمان =====
export 'permission_models.dart';
export 'user_permission_model.dart';
export 'role_default_permission.dart';
export 'custom_role_model.dart';
export 'custom_role_permission_model.dart';
export 'user_custom_role_model.dart';
export 'custom_permission_models.dart';

// ===== نماذج الشاشات والإجراءات =====
export 'screen_model.dart';
export 'action_entity_model.dart';
export 'screen_action_model.dart';

// ===== نماذج المصادقة =====
export 'auth_models.dart';

// ===== نماذج المهام =====
export 'task_model.dart';
export 'task_models.dart';
export 'subtask_models.dart';
export 'task_comment_models.dart';
export 'task_document_model.dart';
export 'task_history_models.dart';
export 'task_message_models.dart';
export 'task_priority_models.dart';
export 'task_progress_models.dart' hide TimeTrackingEntry;
export 'task_type_models.dart';
export 'task_action_type.dart';
export 'task_contributor_model.dart' hide TaskContributor;
export 'task_report_models.dart' hide TaskProgressTracker, TimeTrackingEntry;
export 'time_tracking_models.dart';


// ===== نماذج الرسائل والدردشة =====
export 'message_models.dart';
export 'chat_models.dart';
export 'chat_group_models.dart';
export 'group_member_models.dart';
export 'message_attachment_models.dart';
export 'message_reaction_models.dart';
export 'message_read_models.dart';

// ===== نماذج الإشعارات =====
export 'notification_models.dart';

// ===== نماذج الأرشيف والمستندات =====
export 'archive_models.dart';
export 'archive_document_tag_models.dart';
export 'attachment_model.dart';
export 'text_document_model.dart';

// ===== نماذج التقارير =====
export 'report_models.dart';

export 'contribution_report_model.dart';
export 'enhanced_report_model.dart';
export 'power_bi_models.dart';
export 'report_criteria_model.dart';
export 'exported_report_info.dart';
export 'reporting/report_result_model.dart';

// ===== نماذج النظام =====
export 'system_models.dart';

export 'system_setting_models.dart';
export 'activity_log_models.dart';
export 'backup_models.dart';

// ===== نماذج التقويم واللوحات =====
export 'calendar_models.dart';
export 'dashboard_model.dart';


// ===== نماذج أخرى =====
export 'board_model.dart';
export 'chart_enums.dart';
export 'search_models.dart';
export 'advanced_filter_options.dart';

export 'database_table_model.dart';

/// فئة مساعدة لإدارة النماذج الموحدة
class UnifiedModels {
  /// قائمة بجميع النماذج الأساسية
  static const List<String> coreModels = [
    'User',
    'Role',
    'Department',
    'Permission',
    'Screen',
    'ActionEntity',
  ];

  /// قائمة بجميع نماذج المهام
  static const List<String> taskModels = [
    'Task',
    'Subtask',
    'TaskComment',
    'TaskDocument',
    'TaskHistory',
    'TaskMessage',
    'TaskPriority',
    'TaskProgress',
    'TaskType',
  ];

  /// قائمة بجميع نماذج الرسائل
  static const List<String> messageModels = [
    'Message',
    'ChatGroup',
    'GroupMember',
    'MessageAttachment',
    'MessageReaction',
    'MessageRead',
  ];

  /// قائمة بجميع نماذج النظام
  static const List<String> systemModels = [
    'SystemLog',
    'SystemSetting',
    'ActivityLog',
    'Backup',
    'Notification',
  ];

  /// التحقق من توفر نموذج معين
  static bool isModelAvailable(String modelName) {
    return coreModels.contains(modelName) ||
           taskModels.contains(modelName) ||
           messageModels.contains(modelName) ||
           systemModels.contains(modelName);
  }

  /// الحصول على قائمة بجميع النماذج
  static List<String> getAllModels() {
    return [
      ...coreModels,
      ...taskModels,
      ...messageModels,
      ...systemModels,
    ];
  }

  /// الحصول على معلومات حول النماذج الموحدة
  static Map<String, dynamic> getModelsInfo() {
    return {
      'core_models_count': coreModels.length,
      'task_models_count': taskModels.length,
      'message_models_count': messageModels.length,
      'system_models_count': systemModels.length,
      'total_models': getAllModels().length,
      'backend_compatibility': '100%',
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}

/// تعداد لأنواع النماذج المختلفة
enum ModelType {
  core,
  task,
  message,
  system,
  report,
  auth,
  archive,
  calendar,
}

/// فئة مساعدة لتصنيف النماذج
class ModelClassifier {
  /// تصنيف النماذج حسب النوع
  static Map<ModelType, List<String>> classifyModels() {
    return {
      ModelType.core: UnifiedModels.coreModels,
      ModelType.task: UnifiedModels.taskModels,
      ModelType.message: UnifiedModels.messageModels,
      ModelType.system: UnifiedModels.systemModels,
      ModelType.report: [
        'Report',
        'ReportSchedule',
        'ContributionReport',
        'PowerBiReport',
      ],
      ModelType.auth: [
        'AuthResponse',
        'LoginRequest',
        'RegisterRequest',
        'RefreshTokenRequest',
      ],
      ModelType.archive: [
        'ArchiveDocument',
        'ArchiveCategory',
        'ArchiveTag',
        'Attachment',
      ],
      ModelType.calendar: [
        'CalendarEvent',
        'Dashboard',
        'DashboardWidget',
      ],
    };
  }

  /// الحصول على نماذج نوع معين
  static List<String> getModelsByType(ModelType type) {
    final classified = classifyModels();
    return classified[type] ?? [];
  }
}

/// معلومات حول التحديثات والتحسينات
class ModelUpdatesInfo {
  static const Map<String, String> recentUpdates = {
    'Role': 'تم إضافة displayName, level, isSystemRole, isActive وتطابق 100% مع الباك اند',
    'Screen': 'تم إضافة code وتصحيح updatedAt ليكون اختياري',
    'ActionEntity': 'تم إضافة code وإزالة updatedAt غير الموجود في الباك اند',
    'Permission': 'تم التحقق من التطابق الكامل مع الباك اند',
    'User': 'تم التحقق من التطابق الكامل مع الباك اند',
    'Department': 'تم التحقق من التطابق الكامل مع الباك اند',
  };

  static const String lastUpdateDate = '2025-01-05';
  static const String version = '2.0.0';
  static const String compatibility = '100% متطابق مع ASP.NET Core API';
}
