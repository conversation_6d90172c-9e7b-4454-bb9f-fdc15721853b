import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../models/task_model.dart';
import '../../../models/task_status_enum.dart';
import '../../../constants/app_colors.dart';
import '../../../utils/mouse_event_handler.dart';
import 'unified_filter_export_widget.dart';

/// نطاق عرض مخطط جانت
enum GanttViewRange {
  /// أسبوع واحد
  week,

  /// شهر واحد
  month,

  /// ثلاثة أشهر
  quarter,

  /// ستة أشهر
  halfYear,

  /// سنة كاملة
  year,
}

/// مكون مخطط جانت محسن
///
/// يوفر مخطط جانت تفاعلي لعرض المهام على محور زمني
class EnhancedGanttChart extends StatefulWidget {
  /// قائمة المهام
  final List<Task> tasks;

  /// عنوان المخطط (اختياري)
  final String? title;

  /// نطاق العرض (اختياري)
  final GanttViewRange viewRange;

  /// تاريخ البداية (اختياري)
  final DateTime? startDate;

  /// ارتفاع شريط المهمة (اختياري)
  final double taskBarHeight;

  /// المسافة بين أشرطة المهام (اختياري)
  final double taskBarSpacing;

  /// عرض عمود اسم المهمة (اختياري)
  final double taskNameWidth;

  /// دالة عند النقر على مهمة (اختياري)
  final Function(Task)? onTaskTap;

  /// دالة عند تغيير نطاق العرض (اختياري)
  final Function(GanttViewRange)? onViewRangeChanged;

  /// قائمة الأقسام للتصفية (اختياري)
  final List<String>? departmentIds;

  /// قائمة المستخدمين للتصفية (اختياري)
  final List<String>? userIds;

  /// دالة عند تغيير التصفية (اختياري)
  final Function(List<String>?, List<String>?)? onFilterChanged;

  /// قاموس أسماء الأقسام (اختياري)
  final Map<String, String>? departmentNames;

  /// قاموس أسماء المستخدمين (اختياري)
  final Map<String, String>? userNames;

  /// إنشاء مكون مخطط جانت محسن
  const EnhancedGanttChart({
    super.key,
    required this.tasks,
    this.title,
    this.viewRange = GanttViewRange.month,
    this.startDate,
    this.taskBarHeight = 30,
    this.taskBarSpacing = 12,
    this.taskNameWidth = 200,
    this.onTaskTap,
    this.onViewRangeChanged,
    this.departmentIds,
    this.userIds,
    this.onFilterChanged,
    this.departmentNames,
    this.userNames,
  });

  @override
  State<EnhancedGanttChart> createState() => _EnhancedGanttChartState();
}

class _EnhancedGanttChartState extends State<EnhancedGanttChart> {
  late DateTime _startDate;
  late ScrollController _horizontalScrollController;
  late ScrollController _verticalScrollController;
  late ScrollController _tasksScrollController;
  late ScrollController _timelineScrollController;
  int? _hoveredTaskIndex;
  // تعيين قيمة افتراضية لعدد الأيام المعروضة
  int _daysToShow = 30; // قيمة افتراضية

  // متغير للتحقق من اكتمال التهيئة الأولية
  bool _isInitialized = false;

  // قائمة المهام بعد التصفية
  List<Task> _filteredTasks = [];

  @override
  void initState() {
    super.initState();
    debugPrint('بدء تهيئة مخطط جانت المحسن');
    debugPrint('عدد المهام المستلمة: ${widget.tasks.length}');

    // تهيئة متحكمات التمرير
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _tasksScrollController = ScrollController();
    _timelineScrollController = ScrollController();
    debugPrint('تم إنشاء متحكمات التمرير');

    // تهيئة تاريخ البداية بشكل افتراضي
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1);
    debugPrint('تم تعيين تاريخ البداية الافتراضي: $_startDate');

    // تحديث عدد الأيام المعروضة بناءً على نطاق العرض
    _updateDaysToShow();
    debugPrint('تم تحديث عدد الأيام المعروضة: $_daysToShow');

    // تصفية المهام
    _filterTasks();
    debugPrint('تم تصفية المهام: ${_filteredTasks.length}');

    // حساب نطاق التاريخ
    _calculateDateRange();
    debugPrint('تم حساب نطاق التاريخ');

    debugPrint('تم الانتهاء من تهيئة البيانات الأساسية');

    // ربط التمرير الأفقي بين العناصر
    _horizontalScrollController.addListener(() {
      if (_timelineScrollController.hasClients) {
        _timelineScrollController.jumpTo(_horizontalScrollController.offset);
      }
    });

    // ربط التمرير الرأسي بين العناصر
    _verticalScrollController.addListener(() {
      if (_tasksScrollController.hasClients) {
        _tasksScrollController.jumpTo(_verticalScrollController.offset);
      }
    });

    debugPrint('تم ربط متحكمات التمرير');
    debugPrint('اكتملت تهيئة مخطط جانت المحسن');
  }

  @override
  void didUpdateWidget(EnhancedGanttChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    debugPrint('تحديث مكون مخطط جانت المحسن');

    bool tasksChanged = oldWidget.tasks != widget.tasks;
    bool startDateChanged = oldWidget.startDate != widget.startDate;
    bool viewRangeChanged = oldWidget.viewRange != widget.viewRange;
    bool departmentIdsChanged = oldWidget.departmentIds != widget.departmentIds;
    bool userIdsChanged = oldWidget.userIds != widget.userIds;

    if (tasksChanged) {
      debugPrint(
          'تغيرت قائمة المهام - العدد القديم: ${oldWidget.tasks.length} - العدد الجديد: ${widget.tasks.length}');
    }

    if (startDateChanged) {
      debugPrint(
          'تغير تاريخ البداية - القديم: ${oldWidget.startDate} - الجديد: ${widget.startDate}');
    }

    if (viewRangeChanged) {
      debugPrint(
          'تغير نطاق العرض - القديم: ${oldWidget.viewRange} - الجديد: ${widget.viewRange}');
    }

    if (departmentIdsChanged) {
      debugPrint('تغيرت قائمة الأقسام للتصفية');
    }

    if (userIdsChanged) {
      debugPrint('تغيرت قائمة المستخدمين للتصفية');
    }

    if (tasksChanged ||
        startDateChanged ||
        viewRangeChanged ||
        departmentIdsChanged ||
        userIdsChanged) {
      debugPrint('تحديث بيانات المخطط بسبب تغييرات في الخصائص');
      _updateDaysToShow();
      _filterTasks();
      _calculateDateRange();
    } else {
      debugPrint('لم تتغير أي خاصية مؤثرة - لا حاجة لتحديث البيانات');
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    _tasksScrollController.dispose();
    _timelineScrollController.dispose();
    super.dispose();
  }

  /// تحديث عدد الأيام المعروضة بناءً على نطاق العرض
  void _updateDaysToShow() {
    debugPrint(
        'تحديث عدد الأيام المعروضة - نطاق العرض الحالي: ${widget.viewRange}');

    // حفظ القيمة القديمة للطباعة
    final oldDaysToShow = _daysToShow;

    // تعيين القيمة الجديدة بناءً على نطاق العرض
    switch (widget.viewRange) {
      case GanttViewRange.week:
        _daysToShow = 7;
        break;
      case GanttViewRange.month:
        _daysToShow = 30;
        break;
      case GanttViewRange.quarter:
        _daysToShow = 90;
        break;
      case GanttViewRange.halfYear:
        _daysToShow = 180;
        break;
      case GanttViewRange.year:
        _daysToShow = 365;
        break;
      default:
        _daysToShow = 30; // شهر افتراضي
    }

    debugPrint(
        'تم تحديث عدد الأيام المعروضة من $oldDaysToShow إلى $_daysToShow');
  }

  /// تصفية المهام حسب القسم والمستخدم
  void _filterTasks() {
    debugPrint('بدء تصفية المهام - عدد المهام الأصلية: ${widget.tasks.length}');

    // نسخ قائمة المهام الأصلية
    _filteredTasks = List<Task>.from(widget.tasks);

    // طباعة معلومات عن أول 3 مهام قبل التصفية
    for (int i = 0; i < _filteredTasks.length && i < 3; i++) {
      final task = _filteredTasks[i];
      debugPrint(
          'المهمة ${i + 1} قبل التصفية: ${task.title} - القسم: ${task.departmentId} - المستخدم: ${task.assigneeId}');
    }

    // تصفية حسب القسم
    if (widget.departmentIds != null && widget.departmentIds!.isNotEmpty) {
      debugPrint('تصفية حسب القسم: ${widget.departmentIds}');
      _filteredTasks = _filteredTasks
          .where((task) =>
              task.departmentId != null &&
              widget.departmentIds!.contains(task.departmentId.toString()))
          .toList();
      debugPrint('عدد المهام بعد تصفية القسم: ${_filteredTasks.length}');
    }

    // تصفية حسب المستخدم
    if (widget.userIds != null && widget.userIds!.isNotEmpty) {
      debugPrint('تصفية حسب المستخدم: ${widget.userIds}');
      _filteredTasks = _filteredTasks
          .where((task) =>
              task.assigneeId != null &&
              widget.userIds!.contains(task.assigneeId.toString()))
          .toList();
      debugPrint('عدد المهام بعد تصفية المستخدم: ${_filteredTasks.length}');
    }

    // طباعة معلومات عن أول 3 مهام بعد التصفية
    for (int i = 0; i < _filteredTasks.length && i < 3; i++) {
      final task = _filteredTasks[i];
      debugPrint(
          'المهمة ${i + 1} بعد التصفية: ${task.title} - القسم: ${task.departmentId} - المستخدم: ${task.assigneeId}');
    }

    // ترتيب المهام حسب تاريخ البدء
    _filteredTasks.sort((a, b) {
      final aStartDate = a.startDate != null
          ? DateTime.fromMillisecondsSinceEpoch(a.startDate! * 1000)
          : DateTime.fromMillisecondsSinceEpoch(a.createdAt * 1000);
      final bStartDate = b.startDate != null
          ? DateTime.fromMillisecondsSinceEpoch(b.startDate! * 1000)
          : DateTime.fromMillisecondsSinceEpoch(b.createdAt * 1000);
      return aStartDate.compareTo(bStartDate);
    });

    // ملاحظة: لا نقوم بتصفية المهام حسب التاريخ هنا
    // لأننا نريد عرض جميع المهام بغض النظر عن تاريخها
    // وسيتم التعامل مع المهام خارج نطاق العرض في _processTaskBar

    debugPrint(
        'انتهاء تصفية المهام - عدد المهام النهائي: ${_filteredTasks.length}');
  }

  /// حساب نطاق التاريخ للمخطط
  void _calculateDateRange() {
    // استخدام تاريخ البداية المحدد في الخصائص فقط عند التهيئة الأولية
    if (widget.startDate != null && !_isInitialized) {
      _startDate = DateTime(widget.startDate!.year, widget.startDate!.month,
          widget.startDate!.day);
      _isInitialized = true;
      debugPrint('تم استخدام تاريخ البداية المحدد في الخصائص: $_startDate');
    } else if (!_isInitialized) {
      // استخدام أول يوم في الشهر الحالي كتاريخ افتراضي عند التهيئة الأولية فقط
      final now = DateTime.now();
      _startDate = DateTime(now.year, now.month, 1);
      _isInitialized = true;
      debugPrint('تم استخدام تاريخ البداية الافتراضي: $_startDate');
    }

    // تحديث عدد الأيام المعروضة بناءً على نطاق العرض
    switch (widget.viewRange) {
      case GanttViewRange.week:
        _daysToShow = 7;
        break;
      case GanttViewRange.month:
        // حساب عدد أيام الشهر الحالي
        final lastDayOfMonth =
            DateTime(_startDate.year, _startDate.month + 1, 0).day;
        _daysToShow = lastDayOfMonth;
        break;
      case GanttViewRange.quarter:
        _daysToShow = 90; // تقريبًا 3 أشهر
        break;
      case GanttViewRange.halfYear:
        _daysToShow = 180; // تقريبًا 6 أشهر
        break;
      case GanttViewRange.year:
        _daysToShow = 365; // تقريبًا سنة
        break;
    }

    debugPrint('نطاق العرض: ${widget.viewRange} - عدد الأيام: $_daysToShow');

    // حساب تاريخ النهاية للعرض
    final endDate = DateTime(
        _startDate.year, _startDate.month, _startDate.day + _daysToShow - 1);
    debugPrint('تاريخ النهاية المتوقع: $endDate');

    // إعادة تصفية المهام بعد تغيير نطاق التاريخ
    _filterTasks();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('بدء بناء مكون مخطط جانت المحسن');
    debugPrint('عدد المهام المصفاة: ${_filteredTasks.length}');
    debugPrint('نطاق العرض: ${widget.viewRange} - عدد الأيام: $_daysToShow');

    if (_filteredTasks.isEmpty) {
      debugPrint('لا توجد مهام للعرض - عرض رسالة فارغة');
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد مهام للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إعادة تعيين الفلتر
          setState(() {
            _startDate = DateTime.now().subtract(const Duration(days: 30));
            _filterTasks();
          });

          // استدعاء دالة التصفية إذا كانت موجودة
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(['all'], null);
          }
        },
      );
    }

    debugPrint('بناء مخطط جانت مع ${_filteredTasks.length} مهمة');

    return Column(
      children: [
        // عنوان المخطط وأدوات التحكم
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Row(
            children: [
              if (widget.title != null)
                Expanded(
                  child: Text(
                    widget.title!,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleMedium?.color,
                    ),
                  ),
                ),
              // أدوات التحكم في نطاق العرض
              _buildViewRangeSelector(),
              const SizedBox(width: 16),
              // أدوات التصفية
              _buildFilterButton(context),
              const SizedBox(width: 8),
              // أدوات التصدير
              _buildExportButton(),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              _buildTimelineHeader(),
              Expanded(
                child: _buildGanttBody(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء رأس المخطط (الجدول الزمني)
  Widget _buildTimelineHeader() {
    debugPrint('بدء بناء رأس مخطط جانت (الجدول الزمني)');
    debugPrint('عدد الأيام المعروضة: $_daysToShow');
    debugPrint('تاريخ البداية: $_startDate');

    // إنشاء قائمة الأيام بترتيب معكوس للدعم الصحيح للغة العربية (RTL)
    final daysWidgets = List.generate(
      _daysToShow,
      (index) {
        // استخدام الترتيب المعكوس للدعم الصحيح للغة العربية (RTL)
        final reversedIndex = _daysToShow - 1 - index;
        final date = DateTime(
          _startDate.year,
          _startDate.month,
          _startDate.day + reversedIndex,
        );

        final isWeekend =
            date.weekday == 5 || date.weekday == 6; // الجمعة والسبت
        final isToday = _isToday(date);

        return Container(
          width: 60,
          decoration: BoxDecoration(
            color: isToday
                ? (Get.isDarkMode ? Colors.blue[900] : Colors.blue[50])
                : isWeekend
                    ? (Get.isDarkMode ? Colors.grey[800] : Colors.grey[100])
                    : null,
            border: Border(
              right: BorderSide(
                color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                DateFormat('d').format(date),
                style: TextStyle(
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                  color: isToday
                      ? (Get.isDarkMode ? Colors.white : Colors.blue[800])
                      : (Get.isDarkMode ? Colors.white : Colors.black87),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                DateFormat('E', 'ar').format(date),
                style: TextStyle(
                  fontSize: 12,
                  color: isToday
                      ? (Get.isDarkMode ? Colors.white : Colors.blue[800])
                      : (Get.isDarkMode ? Colors.white70 : Colors.black54),
                ),
              ),
            ],
          ),
        );
      },
    );

    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[850] : Colors.grey[200],
        border: Border(
          bottom: BorderSide(
            color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // عمود اسم المهمة
          Container(
            width: widget.taskNameWidth,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(
                  color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
                  width: 1,
                ),
              ),
            ),
            child: Center(
              child: Text(
                'المهام',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
            ),
          ),
          // الجدول الزمني
          Expanded(
            child: SingleChildScrollView(
              controller: _timelineScrollController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: daysWidgets,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أداة اختيار نطاق العرض
  Widget _buildViewRangeSelector() {
    // تنسيق تاريخ البداية للعرض
    final dateFormat = DateFormat('MMMM yyyy', 'en');
    final formattedDate = dateFormat.format(_startDate);

    return Row(
      children: [
        // أزرار التنقل بين الفترات
        IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          tooltip: 'الفترة السابقة'.tr,
          onPressed: _goToPreviousPeriod,
        ),
        // عرض الفترة الحالية
        Text(
          formattedDate,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.arrow_forward_ios),
          tooltip: 'الفترة التالية'.tr,
          onPressed: _goToNextPeriod,
        ),
        const SizedBox(width: 16),
        // قائمة اختيار نطاق العرض
        DropdownButton<GanttViewRange>(
          value: widget.viewRange,
          isDense: true,
          underline: Container(
            height: 1,
            color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[400],
          ),
          onChanged: (GanttViewRange? newValue) {
            if (newValue != null) {
              // تحديث نطاق العرض مباشرة في الحالة الحالية
              setState(() {
                if (widget.onViewRangeChanged != null) {
                  widget.onViewRangeChanged!(newValue);
                }
                // إعادة حساب نطاق التاريخ بعد تغيير نطاق العرض
                _calculateDateRange();
              });
            }
          },
          items: [
            DropdownMenuItem(
              value: GanttViewRange.week,
              child: const Text('أسبوع'),
            ),
            DropdownMenuItem(
              value: GanttViewRange.month,
              child: const Text('شهر'),
            ),
            DropdownMenuItem(
              value: GanttViewRange.quarter,
              child: const Text('ربع سنة'),
            ),
            DropdownMenuItem(
              value: GanttViewRange.halfYear,
              child: const Text('نصف سنة'),
            ),
            DropdownMenuItem(
              value: GanttViewRange.year,
              child: const Text('سنة'),
            ),
          ],
        ),
      ],
    );
  }

  /// الانتقال إلى الفترة السابقة
  void _goToPreviousPeriod() {
    setState(() {
      switch (widget.viewRange) {
        case GanttViewRange.week:
          _startDate = _startDate.subtract(const Duration(days: 7));
          break;
        case GanttViewRange.month:
          // الانتقال إلى الشهر السابق
          _startDate = DateTime(_startDate.year, _startDate.month - 1, 1);
          break;
        case GanttViewRange.quarter:
          // الانتقال إلى الربع السابق (3 أشهر)
          _startDate = DateTime(_startDate.year, _startDate.month - 3, 1);
          break;
        case GanttViewRange.halfYear:
          // الانتقال إلى النصف السابق (6 أشهر)
          _startDate = DateTime(_startDate.year, _startDate.month - 6, 1);
          break;
        case GanttViewRange.year:
          // الانتقال إلى السنة السابقة
          _startDate = DateTime(_startDate.year - 1, _startDate.month, 1);
          break;
      }
      // إعادة حساب نطاق التاريخ بعد التغيير
      _calculateDateRange();
    });
  }

  /// الانتقال إلى الفترة التالية
  void _goToNextPeriod() {
    setState(() {
      switch (widget.viewRange) {
        case GanttViewRange.week:
          _startDate = _startDate.add(const Duration(days: 7));
          break;
        case GanttViewRange.month:
          // الانتقال إلى الشهر التالي
          _startDate = DateTime(_startDate.year, _startDate.month + 1, 1);
          break;
        case GanttViewRange.quarter:
          // الانتقال إلى الربع التالي (3 أشهر)
          _startDate = DateTime(_startDate.year, _startDate.month + 3, 1);
          break;
        case GanttViewRange.halfYear:
          // الانتقال إلى النصف التالي (6 أشهر)
          _startDate = DateTime(_startDate.year, _startDate.month + 6, 1);
          break;
        case GanttViewRange.year:
          // الانتقال إلى السنة التالية
          _startDate = DateTime(_startDate.year + 1, _startDate.month, 1);
          break;
      }
      // إعادة حساب نطاق التاريخ بعد التغيير
      _calculateDateRange();
    });
  }

  /// بناء زر التصفية
  Widget _buildFilterButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.filter_list),
      tooltip: 'تصفية المهام',
      onPressed: () {
        _showFilterDialog();
      },
    );
  }

  /// بناء زر التصدير
  Widget _buildExportButton() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.download),
      tooltip: 'تصدير المخطط',
      onSelected: (value) {
        _exportChart(value);
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'pdf',
          child: Row(
            children: [
              const Icon(Icons.picture_as_pdf, color: Colors.red),
              const SizedBox(width: 8),
              const Text('تصدير كـ PDF'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'excel',
          child: Row(
            children: [
              const Icon(Icons.table_chart, color: Colors.green),
              const SizedBox(width: 8),
              const Text('تصدير كـ Excel'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'csv',
          child: Row(
            children: [
              const Icon(Icons.description, color: Colors.blue),
              const SizedBox(width: 8),
              const Text('تصدير كـ CSV'),
            ],
          ),
        ),
      ],
    );
  }

  /// تصدير المخطط
  void _exportChart(String format) {
    // إظهار رسالة
    ScaffoldMessenger.of(Get.context!).showSnackBar(
      SnackBar(
        content: Text('جاري تصدير المخطط بتنسيق $format...'),
        duration: const Duration(seconds: 2),
      ),
    );

    // هنا يمكن إضافة وظيفة تصدير المخطط
    // يمكن استخدام خدمة ChartExportService
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() async {
    final context = Get.context!;
    // قوائم مؤقتة للتصفية
    List<String> selectedDepartments = widget.departmentIds?.toList() ?? [];
    List<String> selectedUsers = widget.userIds?.toList() ?? [];
    DateTime? startDate = _startDate;
    DateTime? endDate = DateTime(
        _startDate.year, _startDate.month, _startDate.day + _daysToShow - 1);

    // الحصول على قوائم الأقسام والمستخدمين من المهام
    final Set<String> departmentIds = {};
    Map<String, String> departmentNames = {};
    final Set<String> userIds = {};
    Map<String, String> userNames = {};

    // استخدام أسماء الأقسام والمستخدمين المقدمة إذا كانت متوفرة
    if (widget.departmentNames != null && widget.departmentNames!.isNotEmpty) {
      departmentNames = widget.departmentNames!;
    }

    if (widget.userNames != null && widget.userNames!.isNotEmpty) {
      userNames = widget.userNames!;
    }

    for (final task in widget.tasks) {
      // إضافة القسم
      if (task.departmentId != null) {
        final deptIdStr = task.departmentId.toString();
        departmentIds.add(deptIdStr);
        // إذا لم يكن هناك اسم للقسم، استخدم المعرف
        if (!departmentNames.containsKey(deptIdStr)) {
          departmentNames[deptIdStr] = 'قسم ${task.departmentId}';
        }
      }

      // إضافة المستخدم
      if (task.assigneeId != null) {
        final userIdStr = task.assigneeId.toString();
        userIds.add(userIdStr);
        // إذا لم يكن هناك اسم للمستخدم، استخدم المعرف
        if (!userNames.containsKey(userIdStr)) {
          userNames[userIdStr] = 'مستخدم ${task.assigneeId}';
        }
      }
    }

    // عرض مربع الحوار
    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('تصفية المهام'),
              content: SizedBox(
                width: 400,
                height: 500,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // تصفية حسب الفترة الزمنية
                      const Text(
                        'الفترة الزمنية',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'من',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              readOnly: true,
                              controller: TextEditingController(
                                text: startDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .format(startDate as DateTime)
                                    : '',
                              ),
                              onTap: () async {
                                final pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: startDate ?? DateTime.now(),
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now()
                                      .add(const Duration(days: 365)),
                                );
                                if (pickedDate != null) {
                                  setState(() {
                                    startDate = pickedDate;
                                  });
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'إلى',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              readOnly: true,
                              controller: TextEditingController(
                                text: endDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .format(endDate as DateTime)
                                    : '',
                              ),
                              onTap: () async {
                                final pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: endDate ?? DateTime.now(),
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now()
                                      .add(const Duration(days: 365)),
                                );
                                if (pickedDate != null) {
                                  setState(() {
                                    endDate = pickedDate;
                                  });
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // تصفية حسب الأقسام
                      const Text(
                        'الأقسام',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: departmentIds.map((id) {
                          final isSelected = selectedDepartments.contains(id);
                          return FilterChip(
                            label: Text(departmentNames[id] ?? 'قسم غير معروف'),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  selectedDepartments.add(id);
                                } else {
                                  selectedDepartments.remove(id);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),
                      // تصفية حسب المستخدمين
                      const Text(
                        'المستخدمين',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: userIds.map((id) {
                          final isSelected = selectedUsers.contains(id);
                          return FilterChip(
                            label: Text(userNames[id] ?? 'مستخدم غير معروف'),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  selectedUsers.add(id);
                                } else {
                                  selectedUsers.remove(id);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () {
                    // تحديث تاريخ البداية والنهاية
                    if (startDate != null) {
                      setState(() {
                        _startDate = startDate!;
                        _calculateDateRange();
                      });
                    }

                    // تحديث التصفية
                    if (widget.onFilterChanged != null) {
                      widget.onFilterChanged!(
                        selectedDepartments.isEmpty
                            ? null
                            : selectedDepartments,
                        selectedUsers.isEmpty ? null : selectedUsers,
                      );
                    }
                    Navigator.of(context).pop();
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// بناء جسم المخطط (أشرطة المهام)
  Widget _buildGanttBody() {
    debugPrint('بدء بناء جسم مخطط جانت');
    debugPrint('عدد المهام المصفاة: ${_filteredTasks.length}');

    // المهام مرتبة بالفعل في دالة _filterTasks()
    final sortedTasks = _filteredTasks;

    // طباعة معلومات عن أول 3 مهام للتحقق
    for (int i = 0; i < sortedTasks.length && i < 3; i++) {
      final task = sortedTasks[i];
      final startDate = task.startDate ?? task.createdAt;
      final endDate =
          task.completedAt ?? task.dueDate ?? _calculateEstimatedEndDate(task);
      debugPrint(
          'المهمة ${i + 1}: ${task.title} - تاريخ البدء: $startDate - تاريخ الانتهاء: $endDate');

      // حساب موضع المهمة في المخطط
      final startDateTime = DateTime.fromMillisecondsSinceEpoch(startDate * 1000);
      final normalizedStartDate =
          DateTime(startDateTime.year, startDateTime.month, startDateTime.day);
      final normalizedChartStartDate =
          DateTime(_startDate.year, _startDate.month, _startDate.day);
      final startDayOffset =
          normalizedStartDate.difference(normalizedChartStartDate).inDays;

      debugPrint(
          'إزاحة البداية (بالأيام): $startDayOffset - تاريخ بداية المخطط: $_startDate');
    }

    // تغيير ترتيب العناصر لدعم LTR (من اليسار إلى اليمين) مع الحفاظ على اللغة العربية
    return Row(
      children: [
        // عمود أسماء المهام
        Container(
          width: widget.taskNameWidth,
          decoration: BoxDecoration(
            color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
            border: Border(
              right: BorderSide(
                color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[400]!,
                width: 1,
              ),
            ),
          ),
          child: ListView.builder(
            controller: _tasksScrollController,
            itemCount: sortedTasks.length,
            itemBuilder: (context, index) {
              final task = sortedTasks[index];
              return Container(
                height: widget.taskBarHeight + widget.taskBarSpacing,
                padding: EdgeInsets.only(
                  top: widget.taskBarSpacing / 2,
                  bottom: widget.taskBarSpacing / 2,
                  left: 16,
                  right: 16,
                ),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Get.isDarkMode
                          ? Colors.grey[800]!
                          : Colors.grey[200]!,
                      width: 1,
                    ),
                  ),
                ),
                child: Tooltip(
                  message: task.title,
                  child: Text(
                    task.title,
                    style: TextStyle(
                      fontSize: 13,
                      color: Get.isDarkMode ? Colors.white : Colors.black87,
                    ),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign
                        .right, // تعيين محاذاة النص إلى اليمين للغة العربية
                  ),
                ),
              );
            },
          ),
        ),
        // أشرطة المهام
        Expanded(
          child: SingleChildScrollView(
            controller: _horizontalScrollController,
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: 60.0 * _daysToShow,
              child: ListView.builder(
                controller: _verticalScrollController,
                itemCount: sortedTasks.length,
                itemBuilder: (context, index) {
                  final task = sortedTasks[index];
                  return _buildTaskBar(task, index);
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء شريط المهمة
  Widget _buildTaskBar(Task task, int index) {
    // الحصول على تواريخ المهمة
    final taskStartDateTimestamp = task.startDate ?? task.createdAt;
    final taskStartDate = DateTime.fromMillisecondsSinceEpoch(taskStartDateTimestamp * 1000);

    DateTime taskEndDate;
    if (task.completedAt != null) {
      taskEndDate = DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000);
    } else if (task.dueDate != null) {
      taskEndDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
    } else {
      taskEndDate = _calculateEstimatedEndDate(task);
    }

    debugPrint('المهمة: ${task.title} - المعرف: ${task.id}');
    debugPrint('تاريخ البدء: $taskStartDate - تاريخ الانتهاء: $taskEndDate');

    // تنسيق التواريخ لضمان المقارنة الصحيحة (إزالة الوقت)
    final normalizedStartDate =
        DateTime(taskStartDate.year, taskStartDate.month, taskStartDate.day);
    final normalizedEndDate =
        DateTime(taskEndDate.year, taskEndDate.month, taskEndDate.day);
    final normalizedChartStartDate =
        DateTime(_startDate.year, _startDate.month, _startDate.day);

    debugPrint(
        'تاريخ البدء المعياري: $normalizedStartDate - تاريخ الانتهاء المعياري: $normalizedEndDate');
    debugPrint('تاريخ بداية المخطط المعياري: $normalizedChartStartDate');

    // التحقق من صحة التواريخ
    if (normalizedEndDate.isBefore(normalizedStartDate)) {
      debugPrint('خطأ: تاريخ الانتهاء قبل تاريخ البدء للمهمة: ${task.title}');
      // استخدام تاريخ البدء + يوم واحد كتاريخ انتهاء افتراضي
      final correctedEndDate = normalizedStartDate.add(const Duration(days: 1));
      debugPrint('تصحيح تاريخ الانتهاء إلى: $correctedEndDate');
      // حساب موضع الشريط بدقة
      final startDayOffset =
          normalizedStartDate.difference(normalizedChartStartDate).inDays;
      final taskDuration = 1; // يوم واحد على الأقل
      debugPrint(
          'إزاحة البداية (بالأيام): $startDayOffset - مدة المهمة (بالأيام): $taskDuration');
      return _processTaskBar(task, index, startDayOffset, taskDuration);
    }

    // حساب موضع الشريط بدقة من اليسار
    final startDayOffset =
        normalizedStartDate.difference(normalizedChartStartDate).inDays;
    final taskDuration =
        normalizedEndDate.difference(normalizedStartDate).inDays + 1;

    // طباعة معلومات التصحيح
    debugPrint('موضع البداية من اليسار: $startDayOffset يوم');

    debugPrint(
        'إزاحة البداية (بالأيام): $startDayOffset - مدة المهمة (بالأيام): $taskDuration');

    return _processTaskBar(task, index, startDayOffset, taskDuration);
  }

  /// معالجة شريط المهمة بناءً على الإزاحة والمدة
  Widget _processTaskBar(
      Task task, int index, int startDayOffset, int taskDuration) {
    // تحسين التحقق من المهام خارج نطاق العرض
    // المهمة خارج النطاق فقط إذا كانت تنتهي قبل بداية النطاق أو تبدأ بعد نهاية النطاق
    if (startDayOffset + taskDuration <= 0) {
      debugPrint('المهمة خارج نطاق العرض (قبل الفترة) - تخطي: ${task.title}');
      return Container(
        height: widget.taskBarHeight + widget.taskBarSpacing,
      );
    }

    if (startDayOffset >= _daysToShow) {
      debugPrint('المهمة خارج نطاق العرض (بعد الفترة) - تخطي: ${task.title}');
      return Container(
        height: widget.taskBarHeight + widget.taskBarSpacing,
      );
    }

    // تعديل الموضع والمدة للمهام التي تقع جزئيًا خارج النطاق
    final adjustedStartOffset = startDayOffset < 0 ? 0 : startDayOffset;
    final adjustedDuration = startDayOffset < 0
        ? taskDuration + startDayOffset
        : (adjustedStartOffset + taskDuration > _daysToShow
            ? _daysToShow - adjustedStartOffset
            : taskDuration);

    debugPrint(
        'إزاحة البداية المعدلة: $adjustedStartOffset - المدة المعدلة: $adjustedDuration');

    // التحقق من صحة القيم المحسوبة
    if (adjustedDuration <= 0) {
      debugPrint('خطأ: المدة المعدلة صفر أو سالبة للمهمة: ${task.title}');
      return Container(
        height: widget.taskBarHeight + widget.taskBarSpacing,
      );
    }

    // لون الشريط حسب حالة المهمة
    final Color barColor = _getTaskBarColor(task);
    final bool isHovered = _hoveredTaskIndex == index;

    return Container(
      height: widget.taskBarHeight + widget.taskBarSpacing,
      padding: EdgeInsets.symmetric(vertical: widget.taskBarSpacing / 2),
      child: Stack(
        children: _buildStackChildren(task, index, adjustedStartOffset,
            adjustedDuration, barColor, isHovered),
      ),
    );
  }

  /// بناء عناصر Stack لشريط المهمة
  List<Widget> _buildStackChildren(
      Task task,
      int index,
      int adjustedStartOffset,
      int adjustedDuration,
      Color barColor,
      bool isHovered) {
    debugPrint('بناء عناصر Stack للمهمة: ${task.title}');
    debugPrint(
        'إزاحة البداية: $adjustedStartOffset - المدة: $adjustedDuration - اللون: $barColor');

    final List<Widget> children = [];

    // إضافة خط الوقت
    children.add(
      Positioned.fill(
        child: CustomPaint(
          painter: _TimelinePainter(
            daysToShow: _daysToShow,
            isDarkMode: Get.isDarkMode,
          ),
        ),
      ),
    );

    // إضافة شريط المهمة
    // تعديل موضع شريط المهمة لدعم RTL (من اليمين إلى اليسار) للغة العربية
    // حساب الموضع من اليمين بشكل صحيح (عكس الإزاحة)
    final rightPosition =
        (_daysToShow - adjustedStartOffset - adjustedDuration) * 60.0;

    children.add(
      Positioned(
        right: rightPosition, // استخدام right لدعم RTL
        top: 0,
        child: SafeMouseRegion(
          onEnter: (_) => setState(() => _hoveredTaskIndex = index),
          onExit: (_) => setState(() => _hoveredTaskIndex = null),
          child: Tooltip(
            message: _buildTaskTooltipText(task),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            textStyle: TextStyle(
              color: Get.isDarkMode ? Colors.white : Colors.black87,
              fontSize: 12,
            ),
            preferBelow: true,
            verticalOffset: 20,
            child: GestureDetector(
              onTap: () {
                if (widget.onTaskTap != null) {
                  widget.onTaskTap!(task);
                }
              },
              child: Container(
                width: adjustedDuration * 60,
                height: widget.taskBarHeight,
                decoration: BoxDecoration(
                  color: barColor.withAlpha(isHovered ? 230 : 179),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: barColor,
                    width: 1,
                  ),
                  boxShadow: isHovered
                      ? [
                          BoxShadow(
                            color: barColor.withAlpha(102),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Stack(
                  children: [
                    // شريط نسبة الإكمال - تعديل لدعم RTL
                    if (task.completionPercentage > 0)
                      Positioned(
                        right: 0, // استخدام right لدعم RTL
                        top: 0,
                        bottom: 0,
                        width: (task.completionPercentage / 100) *
                            adjustedDuration *
                            60,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white
                                .withAlpha(150), // لون أفتح لشريط التقدم
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(4),
                              bottomRight: Radius.circular(4),
                              topLeft: task.completionPercentage >= 99
                                  ? Radius.circular(4)
                                  : Radius.zero,
                              bottomLeft: task.completionPercentage >= 99
                                  ? Radius.circular(4)
                                  : Radius.zero,
                            ),
                          ),
                        ),
                      ),

                    // إضافة نمط مخطط للمهام المكتملة
                    if (TaskStatus.fromName(task.status) == TaskStatus.completed)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.white.withAlpha(50),
                                Colors.white.withAlpha(100),
                              ],
                              stops: const [0.4, 0.6],
                              tileMode: TileMode.repeated,
                            ),
                          ),
                        ),
                      ),
                    // عنوان المهمة
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // أيقونة حالة المهمة
                          if (adjustedDuration > 2)
                            Container(
                              margin: const EdgeInsets.only(left: 4),
                              child: Icon(
                                _getTaskStatusIcon(TaskStatus.fromName(task.status)),
                                color: Colors.white,
                                size: 14,
                                shadows: const [
                                  Shadow(
                                    color: Colors.black54,
                                    offset: Offset(1, 1),
                                    blurRadius: 2,
                                  ),
                                ],
                              ),
                            ),

                          // نسبة الإكمال
                          if (task.completionPercentage > 0)
                            Container(
                              margin: const EdgeInsets.only(left: 4),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: Colors.black26,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '${task.completionPercentage.toInt()}%',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                          // عنوان المهمة
                          if (adjustedDuration > 4)
                            Flexible(
                              child: Text(
                                task.title,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black54,
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign
                                    .right, // تعيين محاذاة النص إلى اليمين للغة العربية
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );

    // إضافة علامة تاريخ الاستحقاق
    if (task.dueDate != null) {
      final dueDateDateTime = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
      final normalizedDueDate =
          DateTime(dueDateDateTime.year, dueDateDateTime.month, dueDateDateTime.day);
      final normalizedStartDate =
          DateTime(_startDate.year, _startDate.month, _startDate.day);
      final daysOffset =
          normalizedDueDate.difference(normalizedStartDate).inDays;

      // تعديل موضع علامة تاريخ الاستحقاق لدعم RTL (من اليمين إلى اليسار)
      final rightDueDatePosition = (_daysToShow - daysOffset - 1) * 60.0;

      if (daysOffset >= 0 &&
          daysOffset < _daysToShow &&
          TaskStatus.fromName(task.status) != TaskStatus.completed) {
        children.add(
          Positioned(
            right: rightDueDatePosition, // استخدام right لدعم RTL
            top: 0,
            child: Container(
              width: 2,
              height: widget.taskBarHeight,
              color: Colors.red,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Positioned(
                    top: -8,
                    right: -4, // استخدام right لدعم RTL
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    }

    return children;
  }

  /// الحصول على لون شريط المهمة حسب حالتها
  Color _getTaskBarColor(Task task) {
    final status = TaskStatus.fromName(task.status);
    switch (status) {
      case TaskStatus.completed:
        return Colors.blue.shade600; // لون أزرق للمهام المنجزة
      case TaskStatus.inProgress:
        return AppColors.statusInProgress;
      case TaskStatus.waitingForInfo:
        return AppColors.statusWaitingForInfo;
      case TaskStatus.pending:
        return AppColors.statusPending;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة حالة المهمة
  IconData _getTaskStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.inProgress:
        return Icons.play_circle_outline;
      case TaskStatus.waitingForInfo:
        return Icons.help_outline;
      case TaskStatus.pending:
        return Icons.hourglass_empty;
      case TaskStatus.cancelled:
        return Icons.cancel_outlined;
      case TaskStatus.news:
        return Icons.fiber_new;
      default:
        return Icons.circle_outlined;
    }
  }

  /// حساب تاريخ الانتهاء المقدر للمهمة
  DateTime _calculateEstimatedEndDate(Task task) {
    debugPrint('حساب تاريخ الانتهاء المقدر للمهمة: ${task.title}');

    // إذا كانت المهمة لها تاريخ استحقاق، استخدمه
    if (task.dueDate != null) {
      debugPrint('استخدام تاريخ الاستحقاق المحدد: ${task.dueDate}');
      return DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
    }

    // وإلا، قدر مدة المهمة بناءً على الأولوية
    int estimatedDays;
    final priority = TaskPriority.fromName(task.priority);
    switch (priority) {
      case TaskPriority.urgent:
        estimatedDays = 1;
        break;
      case TaskPriority.high:
        estimatedDays = 3;
        break;
      case TaskPriority.medium:
        estimatedDays = 7;
        break;
      case TaskPriority.low:
        estimatedDays = 14;
        break;
      default:
        estimatedDays = 7;
    }

    debugPrint(
        'تقدير المدة بناءً على الأولوية (${task.priority}): $estimatedDays يوم');

    final startDateTimestamp = task.startDate ?? task.createdAt;
    final startDate = DateTime.fromMillisecondsSinceEpoch(startDateTimestamp * 1000);
    final estimatedEndDate = startDate.add(Duration(days: estimatedDays));
    debugPrint(
        'تاريخ البدء: $startDate - تاريخ الانتهاء المقدر: $estimatedEndDate');

    return estimatedEndDate;
  }

  /// بناء نص التلميح للمهمة
  String _buildTaskTooltipText(Task task) {
    final taskStartDateTimestamp = task.startDate ?? task.createdAt;
    final taskStartDate = DateTime.fromMillisecondsSinceEpoch(taskStartDateTimestamp * 1000);

    DateTime taskEndDate;
    if (task.completedAt != null) {
      taskEndDate = DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000);
    } else if (task.dueDate != null) {
      taskEndDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
    } else {
      taskEndDate = _calculateEstimatedEndDate(task);
    }

    final startDateStr = DateFormat('yyyy/MM/dd').format(taskStartDate);
    final endDateStr = DateFormat('yyyy/MM/dd').format(taskEndDate);

    final duration = taskEndDate.difference(taskStartDate).inDays + 1;

    String statusText;
    final status = TaskStatus.fromName(task.status);
    switch (status) {
      case TaskStatus.completed:
        statusText = 'مكتملة';
        break;
      case TaskStatus.inProgress:
        statusText = 'قيد التنفيذ';
        break;
      case TaskStatus.waitingForInfo:
        statusText = 'في انتظار معلومات';
        break;
      case TaskStatus.pending:
        statusText = 'قيد الانتظار';
        break;
      case TaskStatus.cancelled:
        statusText = 'ملغاة';
        break;
      case TaskStatus.news:
        statusText = 'جديدة';
        break;
      default:
        statusText = 'غير معروفة';
    }

    String priorityText;
    final priority = TaskPriority.fromName(task.priority);
    switch (priority) {
      case TaskPriority.urgent:
        priorityText = 'عاجلة';
        break;
      case TaskPriority.high:
        priorityText = 'مرتفعة';
        break;
      case TaskPriority.medium:
        priorityText = 'متوسطة';
        break;
      case TaskPriority.low:
        priorityText = 'منخفضة';
        break;
      default:
        priorityText = 'غير محددة';
    }

    return 'العنوان: ${task.title}\n'
        'الحالة: $statusText\n'
        'الأولوية: $priorityText\n'
        'نسبة الإنجاز: ${task.completionPercentage.toInt()}%\n'
        'تاريخ البدء: $startDateStr\n'
        'تاريخ الانتهاء: $endDateStr\n'
        'المدة: $duration يوم';
  }

  /// التحقق مما إذا كان التاريخ هو اليوم
  bool _isToday(DateTime date) {
    // استخدام تاريخ اليوم الحالي للمقارنة
    final now = DateTime.now();

    // إذا كان تاريخ بداية المخطط في المستقبل (مثل 2025)
    // نقارن مع اليوم الأول من الشهر في تاريخ بداية المخطط
    if (_startDate.year > now.year) {
      debugPrint('تاريخ بداية المخطط في المستقبل: $_startDate');
      // اعتبار اليوم الأول من الشهر هو "اليوم"
      return date.year == _startDate.year &&
          date.month == _startDate.month &&
          date.day == _startDate.day;
    }

    // المقارنة العادية مع اليوم الحالي
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}

/// رسام خط الوقت
class _TimelinePainter extends CustomPainter {
  final int daysToShow;
  final bool isDarkMode;

  _TimelinePainter({
    required this.daysToShow,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isDarkMode ? Colors.grey[700]! : Colors.grey[300]!
      ..strokeWidth = 1;

    // رسم خطوط عمودية لكل يوم
    for (int i = 0; i <= daysToShow; i++) {
      final x = i * 60.0;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(_TimelinePainter oldDelegate) {
    return oldDelegate.daysToShow != daysToShow ||
        oldDelegate.isDarkMode != isDarkMode;
  }
}
