﻿// reporttask.dart
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/professional_reports/enhanced_task_api_service_new.dart' as api;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pdfWidgets;

// Placeholder imports for dependencies (replace with actual imports)
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:flutter_application_2/models/subtask_models.dart';
import 'package:flutter_application_2/models/task_comment_models.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/task_report_models.dart';
import 'package:flutter_application_2/models/task_history_models.dart';


// Placeholder for DateTimeHelpers (replace with actual implementation)
class DateTimeHelpers {
  static String formatTimestamp(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  static String formatTimestampWithTime(int timestamp) {
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  static String formatDateTime(DateTime dateTime) {
    try {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
    } catch (e) {
      return 'غير محدد';
    }
  }
}

// Constants for consistent styling
class PdfConstants {
  static const PdfColor primaryColor = PdfColor.fromInt(0xFF0D1A26); // أزرق داكن رسمي
  static const PdfColor accentColor = PdfColor.fromInt(0xFF2196F3); // أزرق فاتح مميز
  static const PdfColor sectionBg = PdfColor.fromInt(0xFFF5F7FA); // رمادي فاتح جدًا
  static const PdfColor white = PdfColors.white;
  static const PdfColor textColor = PdfColors.black;
  static const PdfColor textSecondary = PdfColors.grey700;
  static const PdfColor borderColor = PdfColors.grey300;
  static const PdfColor warning = PdfColors.amber400;
  static const PdfColor error = PdfColors.red400;
  static const double defaultMargin = 20.0;
  static const double sectionSpacing = 15.0;
  static const double fontSizeTitle = 16.0;
  static const double fontSizeText = 12.0;
}

// Class for Table of Contents entries
class TocEntry {
  final String title;
  int pageNumber;

  TocEntry(this.title, this.pageNumber);
}

/// Generates a comprehensive PDF report for a single task with enhanced error handling and Arabic support.
///
/// [task] - The task for which to generate the report.
/// [onProgress] - Optional callback to report progress during generation.
/// [username] - Optional username of the user requesting the report.
/// [includeFullHistory] - Whether to include the full task history (default: true).
/// [includedSections] - Set of section identifiers to include in the report.
/// Returns a [Future] containing the generated [pdfWidgets.Document].
/// Throws an [Exception] if report generation fails.
Future<pdfWidgets.Document> generateSingleTaskReportPdf(
  Task task, {
  Function(String)? onProgress,
  String? username,
  bool includeFullHistory = true,
  Set<String> includedSections = const {
    'summary',
    'contributors',
    'transfers',
    'progress',
    'time',
    'comments',
    'attachments',
    'subtasks',
    'statistics',
    'history',
  },
}) async {
  final pdf = pdfWidgets.Document(compress: true); // Enable PDF compression
  final enhancedApiService = api.EnhancedTaskApiService();

  try {
    onProgress?.call('🚀 بدء إنشاء التقرير الاحترافي الشامل للمهمة...');

    // Load fonts
    final fonts = await loadEnhancedArabicFonts();
    onProgress?.call('📝 تحميل الخطوط العربية المحسنة...');

    // Fetch report data with fallback
    onProgress?.call('📊 جلب البيانات الشاملة للمهمة من جميع المصادر...');
    final reportData = await _fetchReportDataWithFallback(task, enhancedApiService);

    // Build report model with fallback
    final reportModel = await _buildReportModelWithFallback(task, reportData, enhancedApiService);
    onProgress?.call('✅ تم إنشاء نموذج التقرير الشامل من البيانات المحسنة');

    // Initialize report helper
    final reportHelper = _EnhancedReportHelper(fonts);

    // Build report pages
    pdf.addPage(_buildEnhancedCoverPage(task, username, reportHelper));
    final tocEntries = <TocEntry>[
      TocEntry('1. ملخص المهمة الشامل', 1),
      TocEntry('2. المساهمون ومساهماتهم التفصيلية', 3),
      TocEntry('3. التحويلات والتخويلات الشاملة', 3),
      TocEntry('4. متتبعات التقدم', 3),
      TocEntry('5. سجلات الوقت', 3),
      TocEntry('6. التعليقات والملاحظات الشاملة', 3),
      TocEntry('7. المرفقات والوثائق التفصيلية', 3),
      TocEntry('8. المهام الفرعية الشاملة', 3),
      TocEntry('9. الإحصائيات الشاملة', 3),
      TocEntry('10. سجل الأحداث التفصيلي الشامل', 3),
    ];
    pdf.addPage(_buildTableOfContents(task, reportModel, reportHelper, tocEntries));

    // Add all sections to a single MultiPage
    final contentSections = _buildComprehensiveContentSections(
      task,
      reportModel,
      reportData,
      reportHelper,
      tocEntries,
      includedSections,
    );
    pdf.addPage(
      pdfWidgets.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pdfWidgets.EdgeInsets.all(PdfConstants.defaultMargin),
        header: (context) => _buildPageHeader(reportHelper, 'تقرير المهمة الشامل'),
        footer: (context) => _buildPageFooter(reportHelper, context),
        build: (context) => contentSections,
      ),
    );

    onProgress?.call('🎉 تم إنشاء التقرير الاحترافي الشامل بنجاح!');
    return pdf;
  } catch (e, stackTrace) {
    if (kDebugMode) print('❌ خطأ في إنشاء التقرير: $e\n$stackTrace');
    onProgress?.call('⚠️ تعذر إنشاء التقرير، يرجى المحاولة مرة أخرى');
    throw Exception('تعذر إنشاء التقرير: $e');
  }
}

/// Fetches comprehensive report data with fallback to basic task data.
///
/// [task] - The task to fetch data for.
/// [apiService] - The API service to fetch data.
/// Returns a [Map] containing report data.
Future<Map<String, dynamic>> _fetchReportDataWithFallback(Task task, api.EnhancedTaskApiService apiService) async {
  try {
    final reportData = await apiService.getTaskComprehensiveReportData(task.id);
    reportData['accessUsers'] = reportData['accessUsers'] ?? buildAccessUsersFromTask(task);
    if (kDebugMode) {
      print('✅ تم جلب البيانات الشاملة:');
      print('   - المساهمون: ${reportData['accessUsers']?.length ?? 0}');
      print('   - متتبعات التقدم: ${reportData['progressTrackers']?.length ?? 0}');
    }
    return reportData;
  } catch (e) {
    if (kDebugMode) print('⚠️ فشل في جلب البيانات الشاملة: $e');
    return _createBasicReportDataFromTask(task);
  }
}

/// Builds a report model with fallback to basic task data.
///
/// [task] - The task to build the model for.
/// [reportData] - The comprehensive report data.
/// [apiService] - The API service for creating the model.
/// Returns a [TaskReportModel].
Future<TaskReportModel> _buildReportModelWithFallback(Task task, Map<String, dynamic> reportData, api.EnhancedTaskApiService apiService) async {
  try {
    return apiService.createEnhancedTaskReportModelSafe(reportData, task);
  } catch (e) {
    if (kDebugMode) print('⚠️ فشل في إنشاء النموذج المحسن: $e');
    return TaskReportModel.fromTask(task);
  }
}

/// Builds accessUsers from task data, avoiding duplicates.
///
/// [task] - The task to extract user data from.
/// Returns a [List] of user maps.
List<Map<String, dynamic>> buildAccessUsersFromTask(Task task) {
  final accessUsers = <Map<String, dynamic>>[];
  final addedUserIds = <int>{};

  // Add creator
  if (task.creator != null && !addedUserIds.contains(task.creator!.id)) {
    accessUsers.add({
      'id': task.creator!.id,
      'name': task.creator!.name,
      'email': task.creator!.email,
      'role': 'المنشئ',
    });
    addedUserIds.add(task.creator!.id);
  }

  // Add assignee
  if (task.assignee != null && !addedUserIds.contains(task.assignee!.id)) {
    accessUsers.add({
      'id': task.assignee!.id,
      'name': task.assignee!.name,
      'email': task.assignee!.email,
      'role': 'المكلف',
    });
    addedUserIds.add(task.assignee!.id);
  }

  // Add commenters
  for (var comment in task.comments) {
    if (comment.user != null && !addedUserIds.contains(comment.user!.id)) {
      accessUsers.add({
        'id': comment.user!.id,
        'name': comment.user!.name,
        'email': comment.user!.email,
        'role': 'معلق',
      });
      addedUserIds.add(comment.user!.id);
    }
  }

  // Add file uploaders
  for (var attachment in task.attachments) {
    if (attachment.uploadedByUser != null && !addedUserIds.contains(attachment.uploadedByUser!.id)) {
      accessUsers.add({
        'id': attachment.uploadedByUser!.id,
        'name': attachment.uploadedByUser!.name,
        'email': attachment.uploadedByUser!.email,
        'role': 'رافع ملفات',
      });
      addedUserIds.add(attachment.uploadedByUser!.id);
    }
  }

  if (kDebugMode) print('✅ تم بناء ${accessUsers.length} مستخدم من البيانات الأساسية');
  return accessUsers;
}

/// Creates basic report data from a task.
///
/// [task] - The task to generate data from.
/// Returns a [Map] containing basic report data.
Map<String, dynamic> _createBasicReportDataFromTask(Task task) {
  final reportData = <String, dynamic>{};
  final accessUsers = buildAccessUsersFromTask(task);
  reportData['accessUsers'] = accessUsers;

  // Build contributors
  final contributors = accessUsers.map((user) {
    final commentsCount = task.comments.where((c) => c.user?.id == user['id']).length;
    final attachmentsCount = task.attachments.where((a) => a.uploadedByUser?.id == user['id']).length;
    final totalContributions = commentsCount + attachmentsCount + (user['role'] == 'المنشئ' ? 1 : 0);
    return {
      'userId': user['id'],
      'userName': user['name'],
      'userEmail': user['email'],
      'role': user['role'],
      'commentsCount': commentsCount,
      'attachmentsCount': attachmentsCount,
      'activitiesCount': totalContributions,
      'totalContributions': totalContributions,
    };
  }).toList();
  reportData['contributors'] = contributors;

  // Build task histories
  final taskHistories = <Map<String, dynamic>>[];
  if (task.creator != null) {
    taskHistories.add({
      'id': 1,
      'taskId': task.id,
      'userId': task.creator!.id,
      'actionType': 'created',
      'actionDescription': 'تم إنشاء المهمة',
      'details': 'تم إنشاء المهمة بواسطة ${task.creator!.name}',
      'timestamp': task.createdAt != null ? DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000).toIso8601String() : DateTime.now().toIso8601String(),
      'user': {
        'id': task.creator!.id,
        'name': task.creator!.name,
        'email': task.creator!.email,
      },
    });
  }

  // Add comment events
  task.comments.asMap().forEach((i, comment) {
    if (comment.user != null) {
      taskHistories.add({
        'id': i + 2,
        'taskId': task.id,
        'userId': comment.user!.id,
        'actionType': 'commented',
        'actionDescription': 'تم إضافة تعليق',
        'details': 'تعليق: ${comment.content.length > 50 ? comment.content.substring(0, 50) + "..." : comment.content}',
        'timestamp': DateTime.fromMillisecondsSinceEpoch(comment.createdAt * 1000).toIso8601String(),
        'user': {
          'id': comment.user!.id,
          'name': comment.user!.name,
          'email': comment.user!.email,
        },
      });
    }
  });

  // Add attachment events
  task.attachments.asMap().forEach((i, attachment) {
    if (attachment.uploadedByUser != null) {
      taskHistories.add({
        'id': task.comments.length + i + 2,
        'taskId': task.id,
        'userId': attachment.uploadedByUser!.id,
        'actionType': 'attachment_added',
        'actionDescription': 'تم إضافة مرفق',
        'details': 'ملف: ${attachment.fileName}',
        'timestamp': DateTime.fromMillisecondsSinceEpoch(attachment.uploadedAt * 1000).toIso8601String(),
        'user': {
          'id': attachment.uploadedByUser!.id,
          'name': attachment.uploadedByUser!.name,
          'email': attachment.uploadedByUser!.email,
        },
      });
    }
  });

  reportData['taskHistories'] = taskHistories;
  reportData['progressTrackers'] = <Map<String, dynamic>>[];
  reportData['timeTrackingEntries'] = <Map<String, dynamic>>[];
  reportData['transfers'] = <Map<String, dynamic>>[];

  if (kDebugMode) {
    print('✅ تم إنشاء بيانات تقرير أساسية:');
    print('   - accessUsers: ${accessUsers.length}');
    print('   - contributors: ${contributors.length}');
    print('   - taskHistories: ${taskHistories.length}');
  }
  return reportData;
}

/// تحميل الخطوط العربية المحسنة لاستخدامها في جميع عناصر التقرير
/// تأكد من إضافة ملفات الخطوط إلى assets/fonts وتسجيلها في pubspec.yaml
Future<Map<String, pdfWidgets.Font>> loadEnhancedArabicFonts() async {
  try {
    // تحميل خط Noto Naskh Arabic (يدعم العربية بشكل ممتاز)
    final fontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Regular.ttf');
    final boldFontData = await rootBundle.load('assets/fonts/NotoNaskhArabic-Bold.ttf');
    return {
      'regular': pdfWidgets.Font.ttf(fontData),
      'bold': pdfWidgets.Font.ttf(boldFontData),
      'commentUser': pdfWidgets.Font.ttf(fontData), // خط خاص لاسم المستخدم في التعليق
    };
  } catch (e) {
    if (kDebugMode) print('⚠️ فشل تحميل الخطوط العربية: $e');
    // fallback للخط الافتراضي في حال فشل التحميل
    return {
      'regular': pdfWidgets.Font.helvetica(),
      'bold': pdfWidgets.Font.helveticaBold(),
      'commentUser': pdfWidgets.Font.helvetica(),
    };
  }
}

/// Safely formats a timestamp, handling null or invalid values.
///
/// [timestamp] - The timestamp in seconds since epoch.
/// Returns a formatted string or 'غير محدد' if invalid.
String _formatSafeTimestamp(int? timestamp) {
  if (timestamp == null || timestamp == 0) return 'غير محدد';
  try {
    return DateTimeHelpers.formatTimestampWithTime(timestamp);
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في تنسيق التاريخ: $timestamp - $e');
    return 'تاريخ غير صحيح';
  }
}

/// Enhanced report helper for building PDF elements with consistent styling.
class _EnhancedReportHelper {
  final Map<String, pdfWidgets.Font> fonts;
  _EnhancedReportHelper(this.fonts);

  pdfWidgets.Widget buildSectionHeader(String title, PdfColor? color) {
    return pdfWidgets.Container(
      padding: const pdfWidgets.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: pdfWidgets.BoxDecoration(
        color: PdfConstants.accentColor,
      ),
      child: pdfWidgets.Text(
        title,
        style: pdfWidgets.TextStyle(
          font: fonts['bold'],
          fontSize: PdfConstants.fontSizeTitle,
          color: PdfConstants.white,
        ),
        textAlign: pdfWidgets.TextAlign.right,
        textDirection: pdfWidgets.TextDirection.rtl,
    ),
   
    );
  }

  pdfWidgets.Widget buildTableOfContentsItem(String title, String pageNumber) {
    return pdfWidgets.Padding(
      padding: const pdfWidgets.EdgeInsets.symmetric(vertical: 4),
      child: pdfWidgets.Row(
        mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
        children: [
          pdfWidgets.Text(
            title,
            style: pdfWidgets.TextStyle(
              font: fonts['regular'],
              fontSize: PdfConstants.fontSizeText,
              color: PdfConstants.textSecondary,
            ),
            textAlign: pdfWidgets.TextAlign.right,
            textDirection: pdfWidgets.TextDirection.rtl,
          ),
          pdfWidgets.Text(
            pageNumber,
            style: pdfWidgets.TextStyle(
              font: fonts['bold'],
              fontSize: PdfConstants.fontSizeText,
              color: PdfColors.grey700,
            ),
            textAlign: pdfWidgets.TextAlign.center,
            textDirection: pdfWidgets.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  pdfWidgets.Widget buildEnhancedInfoContainer({
    required String title,
    required PdfColor color,
    PdfColor? borderColor,
    required pdfWidgets.Widget child,
    PdfColor? shadowColor,
  }) {
    return pdfWidgets.Container(
      margin: const pdfWidgets.EdgeInsets.only(bottom: PdfConstants.sectionSpacing),
      padding: const pdfWidgets.EdgeInsets.all(10),
      decoration: pdfWidgets.BoxDecoration(
        color: color,
        border: pdfWidgets.Border.all(color: PdfColors.black, width: 1), // إطار واحد فقط حول القسم
        boxShadow: shadowColor != null
            ? [pdfWidgets.BoxShadow(color: shadowColor, blurRadius: 4)]
            : null,
      ),
      child: pdfWidgets.Column(
        crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
        children: [
          pdfWidgets.Text(
            title,
            style: pdfWidgets.TextStyle(
              font: fonts['bold'],
              fontSize: PdfConstants.fontSizeText,
              color: PdfConstants.primaryColor,
            ),
            textAlign: pdfWidgets.TextAlign.right,
            textDirection: pdfWidgets.TextDirection.rtl,
          ),
          pdfWidgets.SizedBox(height: 8),
          child,
        ],
      ),
    );
  }

  // تعديل الجداول والعناصر الداخلية لإزالة الإطارات
  pdfWidgets.Widget buildTableHeader(String text) {
    return pdfWidgets.Container(
      padding: const pdfWidgets.EdgeInsets.all(8),
      child: pdfWidgets.Text(
        text,
        style: pdfWidgets.TextStyle(
          font: fonts['bold'],
          fontSize: PdfConstants.fontSizeText,
          color: PdfConstants.textColor,
        ),
        textAlign: pdfWidgets.TextAlign.center,
        textDirection: pdfWidgets.TextDirection.rtl,
      ),
    );
  }

  pdfWidgets.Widget buildTableCell(String text, {bool isBold = false}) {
    return pdfWidgets.Container(
      padding: const pdfWidgets.EdgeInsets.all(8),
      child: pdfWidgets.Text(
        text,
        style: pdfWidgets.TextStyle(
          font: isBold ? fonts['bold'] : fonts['regular'],
          fontSize: PdfConstants.fontSizeText,
          color: PdfConstants.textColor,
        ),
        textAlign: pdfWidgets.TextAlign.center,
        textDirection: pdfWidgets.TextDirection.rtl,
      ),
    );
  }

  pdfWidgets.Widget buildInfoText(String text, {double fontSize = PdfConstants.fontSizeText, PdfColor? color, bool isBold = false}) {
    return pdfWidgets.Text(
      text,
      style: pdfWidgets.TextStyle(
        font: isBold ? fonts['bold'] : fonts['regular'],
        fontSize: fontSize,
        color: color ?? PdfConstants.textColor,
      ),
      textAlign: pdfWidgets.TextAlign.right,
      textDirection: pdfWidgets.TextDirection.rtl,
    );
  }

  pdfWidgets.Widget buildInfoRow(String label, String value, {bool isTitle = false}) {
    return pdfWidgets.Padding(
      padding: const pdfWidgets.EdgeInsets.symmetric(vertical: 2),
      child: pdfWidgets.Row(
        mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
        children: [
          pdfWidgets.Text(
            value,
            style: pdfWidgets.TextStyle(
              font: isTitle ? fonts['bold'] : fonts['regular'],
              fontSize: PdfConstants.fontSizeText,
              color: PdfConstants.textColor,
            ),
            textAlign: pdfWidgets.TextAlign.right,
            textDirection: pdfWidgets.TextDirection.rtl,
          ),
          pdfWidgets.Text(
            label,
            style: pdfWidgets.TextStyle(
              font: fonts['regular'],
              fontSize: PdfConstants.fontSizeText,
              color: PdfConstants.textSecondary,
            ),
            textAlign: pdfWidgets.TextAlign.right,
            textDirection: pdfWidgets.TextDirection.rtl,
          ),
        ],
      ),
    );
  }

  pdfWidgets.Table buildTable({
    required List<pdfWidgets.TableRow> rows,
    pdfWidgets.TableBorder? border,
    pdfWidgets.TableColumnWidth? defaultColumnWidth,
    List<pdfWidgets.TableColumnWidth>? columnWidths,
    pdfWidgets.TableCellVerticalAlignment? defaultVerticalAlignment,
    bool? tableWidth,
  }) 
  {
    return pdfWidgets.Table(
      children: rows,
      border: pdfWidgets.TableBorder.all(color: PdfColors.black, width: 0.7), // حدود واضحة افتراضية لأي جدول
      defaultColumnWidth: defaultColumnWidth ?? const pdfWidgets.FlexColumnWidth(),
      columnWidths: columnWidths != null
          ? Map<int, pdfWidgets.TableColumnWidth>.fromIterables(
              List.generate(columnWidths.length, (i) => i),
              columnWidths,
            )
          : null,
      defaultVerticalAlignment: defaultVerticalAlignment ?? pdfWidgets.TableCellVerticalAlignment.middle,
    );
  }
}


/// Builds the professional cover page for the report.
///
/// [task] - The task for the report.
/// [username] - The username of the report requester.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Page].
pdfWidgets.Page _buildEnhancedCoverPage(Task task, String? username, _EnhancedReportHelper helper) {
  return pdfWidgets.Page(
    pageFormat: PdfPageFormat.a4,
    textDirection: pdfWidgets.TextDirection.rtl,
    build: (pdfWidgets.Context context) {
      return pdfWidgets.Container(
        decoration: pdfWidgets.BoxDecoration(
          gradient: pdfWidgets.LinearGradient(
            begin: pdfWidgets.Alignment.topCenter,
            end: pdfWidgets.Alignment.bottomCenter,
            colors: [PdfColors.teal50, PdfColors.white],
          ),
        ),
        child: pdfWidgets.Padding(
          padding: const pdfWidgets.EdgeInsets.all(40),
          child: pdfWidgets.Column(
            mainAxisAlignment: pdfWidgets.MainAxisAlignment.center,
            crossAxisAlignment: pdfWidgets.CrossAxisAlignment.center,
            children: [
              pdfWidgets.Container(
                padding: const pdfWidgets.EdgeInsets.all(20),
                decoration: pdfWidgets.BoxDecoration(
                  color: PdfConstants.primaryColor,
                  boxShadow: [
                    pdfWidgets.BoxShadow(
                      color: PdfColors.grey400,
                      offset: const PdfPoint(0, 4),
                      blurRadius: 8,
                    ),
                  ],
                ),
                child: pdfWidgets.Column(
                  children: [
                    helper.buildInfoText('بسم الله الرحمن الرحيم', fontSize: 16, color: PdfColors.white),
                    pdfWidgets.SizedBox(height: 8),
                    helper.buildInfoText('نظام إدارة المهام المتقدم', fontSize: 24, color: PdfColors.white, isBold: true),
                    pdfWidgets.SizedBox(height: 4),
                    helper.buildInfoText('تقرير المهمة الاحترافي الشامل', fontSize: 18, color: PdfConstants.accentColor),
                  ],
                ),
              ),
              pdfWidgets.SizedBox(height: 30),
              helper.buildEnhancedInfoContainer(
                title: 'بيانات المهمة الأساسية',
                color: PdfConstants.white,
                shadowColor: PdfConstants.borderColor,
                child: pdfWidgets.Column(
                  crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
                  children: [
                    helper.buildInfoRow('العنوان:', task.title, isTitle: true),
                    helper.buildInfoRow('رقم المهمة:', '#${task.id}'),
                    helper.buildInfoRow('القسم:', task.department?.name ?? 'غير محدد'),
                    helper.buildInfoRow('النوع:', task.taskType?.name ?? 'غير محدد'),
                    helper.buildInfoRow('الحالة:', task.status),
                    helper.buildInfoRow('الأولوية:', task.priority),
                    // helperさまざまな
                    helper.buildInfoRow('نسبة الإنجاز:', '${task.completionPercentage}%'),
                    helper.buildInfoRow('تاريخ الإنشاء:', DateTimeHelpers.formatTimestamp(task.createdAt)),
                    if (task.dueDate != null)
                      helper.buildInfoRow('تاريخ الاستحقاق:', DateTimeHelpers.formatTimestamp(task.dueDate!)),
                    if (username != null)
                      helper.buildInfoRow('طالب التقرير:', username),
                  ],
                ),
              ),
              pdfWidgets.SizedBox(height: 30),
              pdfWidgets.Container(
                padding: const pdfWidgets.EdgeInsets.all(15),
                decoration: pdfWidgets.BoxDecoration(
                  color: PdfColors.grey100,
                  // borderRadius: pdfWidgets.BorderRadius.circular(10),
                  // border: pdfWidgets.Border.all(color: PdfColors.grey300),
                ),
                child: pdfWidgets.Column(
                  children: [
                    helper.buildInfoText('تاريخ إصدار التقرير: ${DateTimeHelpers.formatDateTime(DateTime.now())}',
                        fontSize: 12, color: PdfColors.grey700, isBold: true),
                    pdfWidgets.SizedBox(height: 4),
                    helper.buildInfoText('نوع التقرير: تقرير مهمة شامل ومفصل',
                        fontSize: 10, color: PdfColors.grey600),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}

/// Builds the table of contents page.
///
/// [task] - The task for the report.
/// [reportModel] - The report model.
/// [helper] - The enhanced report helper.
/// [tocEntries] - List of table of contents entries.
/// Returns a [pdfWidgets.Page].
pdfWidgets.Page _buildTableOfContents(Task task, TaskReportModel reportModel, _EnhancedReportHelper helper, List<TocEntry> tocEntries) {
  return pdfWidgets.Page(
    pageFormat: PdfPageFormat.a4,
    textDirection: pdfWidgets.TextDirection.rtl,
    build: (pdfWidgets.Context context) {
      return pdfWidgets.Padding(
        padding: const pdfWidgets.EdgeInsets.all(PdfConstants.defaultMargin),
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildSectionHeader('فهرس المحتويات', PdfConstants.primaryColor),
            pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
            ...tocEntries.map((entry) => helper.buildTableOfContentsItem(entry.title, entry.pageNumber.toString())),
            pdfWidgets.SizedBox(height: 30),
            helper.buildEnhancedInfoContainer(
              title: 'ملخص سريع',
              color: PdfColors.blue50,
              borderColor: PdfColors.blue200,
              child: pdfWidgets.Column(
                crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
                children: [
                  helper.buildInfoText('• عدد المساهمين: ${reportModel.contributors.length}'),
                  helper.buildInfoText('• عدد التحويلات: ${reportModel.transfers.length}'),
                  helper.buildInfoText('• عدد التعليقات: ${task.comments.length}'),
                  helper.buildInfoText('• عدد المرفقات: ${task.attachments.length}'),
                  helper.buildInfoText('• عدد المهام الفرعية: ${task.subtasks.length}'),
                  helper.buildInfoText('• نسبة الإنجاز: ${task.completionPercentage}%'),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}




// /// Builds the comprehensive task summary section.
///
/// [task] - The task for the report.
/// [reportModel] - The report model.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildComprehensiveTaskSummary(Task task, TaskReportModel reportModel, _EnhancedReportHelper helper) {
  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('1. ملخص المهمة الشامل', PdfConstants.primaryColor),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'البيانات الأساسية',
        color: PdfColors.teal50,
        borderColor: PdfColors.teal200,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('العنوان:', task.title, isTitle: true),
            helper.buildInfoRow('الوصف:', task.description ?? 'لا يوجد وصف'),
            helper.buildInfoRow('الملاحظات:', task.note ?? 'لا توجد ملاحظات'),
            helper.buildInfoRow('الوارد:', task.incoming ?? 'غير محدد'),
            helper.buildInfoRow('رقم المهمة:', '#${task.id}'),
            helper.buildInfoRow('المنشئ:', task.creator?.name ?? 'غير محدد'),
            helper.buildInfoRow('المكلف:', task.assignee?.name ?? 'غير محدد'),
          ],
        ),
      ),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'التواريخ والأوقات',
        color: PdfColors.blue50,
        borderColor: PdfColors.blue200,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('تاريخ الإنشاء:', _formatSafeTimestamp(task.createdAt)),
            helper.buildInfoRow('تاريخ البداية:', _formatSafeTimestamp(task.startDate)),
            helper.buildInfoRow('تاريخ الاستحقاق:', _formatSafeTimestamp(task.dueDate)),
            helper.buildInfoRow('تاريخ الإنجاز:', _formatSafeTimestamp(task.completedAt)),
            helper.buildInfoRow('الوقت المقدر:', task.estimatedTime != null ? '${task.estimatedTime} دقيقة' : 'غير محدد'),
            helper.buildInfoRow('الوقت الفعلي:', task.actualTime != null ? '${task.actualTime} دقيقة' : 'غير محدد'),
          ],
        ),
      ),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'الحالة والأولوية',
        color: PdfColors.orange50,
        borderColor: PdfColors.orange200,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('الحالة الحالية:', task.status),
            helper.buildInfoRow('الأولوية:', task.priority),
            helper.buildInfoRow('نسبة الإنجاز:', '${task.completionPercentage}%'),
            helper.buildInfoRow('القسم:', task.department?.name ?? 'غير محدد'),
            helper.buildInfoRow('نوع المهمة:', task.taskType?.name ?? 'غير محدد'),
            helper.buildInfoRow('حالة الحذف:', task.isDeleted ? 'محذوفة' : 'نشطة'),
          ],
        ),
      ),
    ],
  );
}

/// Builds the contributors section as a Widget (not MultiPage).
pdfWidgets.Widget _buildContributorsSection(
  List<TaskContributor> contributors,
  Task task,
  Map<String, dynamic> reportData,
  _EnhancedReportHelper helper,
) {
  final widgets = <pdfWidgets.Widget>[];
  final validContributors = contributors.isNotEmpty ? contributors : _buildContributorsFromTask(task);
  widgets.add(helper.buildSectionHeader('2. المساهمون ومساهماتهم التفصيلية', PdfConstants.primaryColor));
  widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
  if (validContributors.isEmpty) {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'لا توجد بيانات مساهمين',
      color: PdfColors.grey100,
      borderColor: PdfConstants.borderColor,
      child: helper.buildInfoText('لم يتم العثور على بيانات مساهمين لهذه المهمة', color: PdfColors.grey600),
    ));
  } else {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'ملخص المساهمين',
      color: PdfConstants.sectionBg,
      borderColor: PdfConstants.accentColor,
      child: pdfWidgets.Column(
        crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
        children: [
          helper.buildInfoRow('إجمالي المساهمات:', '${validContributors.fold(0, (sum, c) => sum + c.totalContributions)}'),
        ],
      ),
    ));
    widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'تفاصيل المساهمين',
      color: PdfConstants.white,
      borderColor: PdfConstants.accentColor,
      child: pdfWidgets.Table(
        border: pdfWidgets.TableBorder.all(color: PdfColors.black, width: 0.7), // حدود واضحة للجداول
        columnWidths: {
          0: const pdfWidgets.FlexColumnWidth(2), // الاسم
          1: const pdfWidgets.FlexColumnWidth(2), // الدور
          2: const pdfWidgets.FlexColumnWidth(2), // البريد
          3: const pdfWidgets.FlexColumnWidth(2), // عدد التعليقات
          4: const pdfWidgets.FlexColumnWidth(2), // عدد المرفقات
          5: const pdfWidgets.FlexColumnWidth(2), // إجمالي المساهمات
          6: const pdfWidgets.FlexColumnWidth(2), // النسبة
        },
        children: [
          pdfWidgets.TableRow(
            decoration: const pdfWidgets.BoxDecoration(color: PdfColors.blue100),
            children: [
              helper.buildTableHeader('الاسم'),
              helper.buildTableHeader('الدور'),
              helper.buildTableHeader('البريد الإلكتروني'),
              helper.buildTableHeader('عدد التعليقات'),
              helper.buildTableHeader('عدد المرفقات'),
              helper.buildTableHeader('إجمالي المساهمات'),
              helper.buildTableHeader('النسبة %'),
            ],
          ),
          ...validContributors.map((c) => pdfWidgets.TableRow(
                children: [
                  helper.buildTableCell(c.userName ?? '', isBold: true),
                  helper.buildTableCell(c.role ?? ''),
                  helper.buildTableCell(c.userEmail ?? ''),
                  helper.buildTableCell('${c.commentsCount ?? ''}'),
                  helper.buildTableCell('${c.attachmentsCount ?? ''}'),
                  helper.buildTableCell('${c.totalContributions ?? ''}'),
                  helper.buildTableCell(c.percentage != null ? c.percentage.toStringAsFixed(1) : ''),
                ],
              )),
        ],
      ),
    ));
  }
  return pdfWidgets.Column(children: widgets);
}

/// Builds the transfers section as a Widget (not MultiPage).
pdfWidgets.Widget _buildTransfersSection(
  List<TaskTransfer> transfers,
  Task task,
  _EnhancedReportHelper helper,
) {
  final realTransfers = transfers.isNotEmpty ? transfers : _buildTransfersFromTask(task);
  final widgets = <pdfWidgets.Widget>[];
  widgets.add(helper.buildSectionHeader('3. التحويلات والتخويلات الشاملة', PdfColors.green800));
  widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
  if (realTransfers.isEmpty) {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'لا توجد تحويلات',
      color: PdfColors.grey100,
      borderColor: PdfColors.green300,
      child: helper.buildInfoText('لم يتم تسجيل أي تحويلات لهذه المهمة', color: PdfColors.grey600),
    ));
  } else {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'ملخص التحويلات',
      color: PdfColors.green50,
      borderColor: PdfColors.green200,
      child: pdfWidgets.Column(
        crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
        children: [
          helper.buildInfoRow('إجمالي التحويلات:', '${realTransfers.length}'),
          helper.buildInfoRow('التحويلات الأولية:', '${realTransfers.where((t) => t.type == TransferType.initial).length}'),
          helper.buildInfoRow('إعادة التكليف:', '${realTransfers.where((t) => t.type == TransferType.reassignment).length}'),
        ],
      ),
    ));
    widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'تفاصيل التحويلات',
      color: PdfColors.white,
      borderColor: PdfColors.green200,
      child: pdfWidgets.Table(
        border: pdfWidgets.TableBorder.all(color: PdfColors.black, width: 0.7), // حدود واضحة للجداول
        columnWidths: {
          0: const pdfWidgets.FlexColumnWidth(2.5), // من
          1: const pdfWidgets.FlexColumnWidth(2.5), // إلى
          2: const pdfWidgets.FlexColumnWidth(3),   // السبب
          3: const pdfWidgets.FlexColumnWidth(2),   // المنفذ
          4: const pdfWidgets.FlexColumnWidth(2.5), // التاريخ
        },
        children: [
          pdfWidgets.TableRow(
            decoration: const pdfWidgets.BoxDecoration(color: PdfColors.green100),
            children: [
              helper.buildTableHeader('من'),
              helper.buildTableHeader('إلى'),
              helper.buildTableHeader('السبب'),
              helper.buildTableHeader('المنفذ'),
              helper.buildTableHeader('التاريخ'),
            ],
          ),
          ...realTransfers.map((t) => pdfWidgets.TableRow(
                children: [
                  helper.buildTableCell(t.fromUser ?? '', isBold: true),
                  helper.buildTableCell(t.toUser ?? '', isBold: true),
                  helper.buildTableCell(t.reason ?? ''),
                  helper.buildTableCell(t.executor ?? ''),
                  helper.buildTableCell(_formatSafeTimestamp(t.timestamp)),
                ],
              )),
        ],
    )
    )
    );
  }
  return pdfWidgets.Column(children: widgets);
}

/// Builds the comments section as a Widget (not MultiPage).
pdfWidgets.Widget _buildCommentsSection(List<TaskComment> comments, _EnhancedReportHelper helper) {
  final widgets = <pdfWidgets.Widget>[];
  widgets.add(helper.buildSectionHeader('6. التعليقات والملاحظات الشاملة', PdfConstants.primaryColor));
  widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
  if (comments.isEmpty) {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'لا توجد تعليقات',
      color: PdfColors.grey100,
      borderColor: PdfConstants.borderColor,
      child: helper.buildInfoText('لم يتم إضافة أي تعليقات على هذه المهمة', color: PdfColors.grey600),
    ));
  } else {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'ملخص التعليقات',
      color: PdfConstants.sectionBg,
      borderColor: PdfConstants.accentColor,
      child: pdfWidgets.Column(
        crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
        children: [
          helper.buildInfoRow('إجمالي التعليقات:', '${comments.length}'),
          helper.buildInfoRow('أول تعليق:', DateTimeHelpers.formatTimestamp(comments.first.createdAt)),
          helper.buildInfoRow('آخر تعليق:', DateTimeHelpers.formatTimestamp(comments.last.createdAt)),
        ],
      ),
    ));
    widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
    for (var i = 0; i < comments.length; i++) {
      final comment = comments[i];
      final user = comment.user;
      String commenterName = 'مستخدم';
      if (user != null && user.name.trim().isNotEmpty) {
        commenterName = user.name.trim();
      } else {
        if (kDebugMode) {
          print('⚠️ تعليق بدون اسم مستخدم أو اسم غير صالح: comment index=$i, user=${user}');
        }
      }
      widgets.add(
        pdfWidgets.Container(
          margin: const pdfWidgets.EdgeInsets.only(bottom: 8), // تقليل من 12 إلى 8 (33% تقليل)
          padding: const pdfWidgets.EdgeInsets.all(8), // تقليل من 12 إلى 8 (33% تقليل)
          decoration: pdfWidgets.BoxDecoration(
            color: PdfColors.purple100,
            borderRadius: pdfWidgets.BorderRadius.circular(7), // تقليل من 10 إلى 7 (30% تقليل)
            border: pdfWidgets.Border.all(color: PdfColors.purple200, width: 1),
            boxShadow: [
              pdfWidgets.BoxShadow(
                color: PdfColors.purple50,
                blurRadius: 1.5, // تقليل من 2 إلى 1.5 (25% تقليل)
              ),
            ],
          ),
          child: pdfWidgets.Row(
            crossAxisAlignment: pdfWidgets.CrossAxisAlignment.start,
            children: [
              pdfWidgets.Container(
                margin: const pdfWidgets.EdgeInsets.only(left: 6, right: 0, top: 1), // تقليل من 8,0,2 إلى 6,0,1
                child: pdfWidgets.Icon(
                  pdfWidgets.IconData(0xe87f), // chat bubble icon
                  color: PdfColors.purple600,
                  size: 16, // تقليل من 22 إلى 16 (27% تقليل)
                ),
              ),
              pdfWidgets.Expanded(
                child: pdfWidgets.Column(
                  crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
                  children: [
                    pdfWidgets.Row(
                      mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
                      children: [
                        pdfWidgets.Text(
                          commenterName,
                          style: pdfWidgets.TextStyle(
                            font: helper.fonts['commentUser'] ?? helper.fonts['bold'],
                            fontSize: 11, // تقليل من 13 إلى 11 (15% تقليل)
                            color: PdfColors.purple900,
                          ),
                          textDirection: pdfWidgets.TextDirection.rtl,
                        ),
                        pdfWidgets.Text(
                          DateTimeHelpers.formatTimestampWithTime(comment.createdAt),
                          style: pdfWidgets.TextStyle(
                            font: helper.fonts['regular'],
                            fontSize: 9, // تقليل من 10 إلى 9 (10% تقليل)
                            color: PdfColors.purple700,
                          ),
                          textDirection: pdfWidgets.TextDirection.rtl,
                        ),
                      ],
                    ),
                    pdfWidgets.SizedBox(height: 4), // تقليل من 6 إلى 4 (33% تقليل)
                    pdfWidgets.Text(
                      comment.content,
                      style: pdfWidgets.TextStyle(
                        font: helper.fonts['regular'],
                        fontSize: 10, // تقليل من 12 إلى 10 (17% تقليل)
                        color: PdfConstants.textColor,
                      ),
                      textDirection: pdfWidgets.TextDirection.rtl,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
  return pdfWidgets.Column(children: widgets);
}

/// Builds the attachments section as a Widget (not MultiPage).
pdfWidgets.Widget _buildAttachmentsSection(List<Attachment> attachments, _EnhancedReportHelper helper, Map<String, dynamic> reportData) {
  final widgets = <pdfWidgets.Widget>[];
  widgets.add(helper.buildSectionHeader('7. المرفقات والوثائق التفصيلية', PdfConstants.primaryColor));
  widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));
  if (attachments.isEmpty) {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'لا توجد مرفقات',
      color: PdfColors.grey100,
      borderColor: PdfColors.grey300,
      child: helper.buildInfoText('لم يتم رفع أي مرفقات لهذه المهمة', color: PdfColors.grey600),
    ));
  } else {
    widgets.add(helper.buildEnhancedInfoContainer(
      title: 'ملخص المرفقات',
      color: PdfConstants.sectionBg,
      borderColor: PdfConstants.accentColor,
      child: pdfWidgets.Column(
        crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
        children: [
          helper.buildInfoRow('إجمالي المرفقات:', '${attachments.length}'),
          helper.buildInfoRow('أنواع الملفات:', _getFileTypes(attachments)),
        ],
      ),
    ));
    widgets.add(pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing));

    // تقسيم المرفقات إلى مجموعات صغيرة (8 في كل جدول)
    const int chunkSize = 6;
    for (int i = 0; i < attachments.length; i += chunkSize) {
      final chunk = attachments.skip(i).take(chunkSize).toList();
      widgets.add(helper.buildEnhancedInfoContainer(
        title: 'تفاصيل المرفقات' + (attachments.length > chunkSize ? ' (${i + 1} - ${i + chunk.length})' : ''),
        color: PdfColors.white,
        borderColor: PdfColors.orange200,
        child: pdfWidgets.Table(
          border: pdfWidgets.TableBorder.all(color: PdfColors.black, width: 0.7), // حدود واضحة للجداول
          columnWidths: {
            0: const pdfWidgets.FlexColumnWidth(3), // اسم الملف
            1: const pdfWidgets.FlexColumnWidth(1.5), // النوع
            2: const pdfWidgets.FlexColumnWidth(1.5), // الحجم
            3: const pdfWidgets.FlexColumnWidth(2),   // تاريخ الرفع
            4: const pdfWidgets.FlexColumnWidth(2),   // رافع الملف
          },
          children: [
            pdfWidgets.TableRow(
              decoration: const pdfWidgets.BoxDecoration(color: PdfColors.orange100),
              children: [
                helper.buildTableHeader('اسم الملف'),
                helper.buildTableHeader('النوع'),
                helper.buildTableHeader('الحجم'),
                helper.buildTableHeader('تاريخ الرفع'),
                helper.buildTableHeader('رافع الملف'),
              ],
            ),
            ...chunk.map((a) => pdfWidgets.TableRow(
                  children: [
                    helper.buildTableCell(a.fileName ?? '', isBold: true),
                    helper.buildTableCell(a.fileType ?? ''),
                    helper.buildTableCell('${a.fileSize ?? ''} KB'),
                    helper.buildTableCell(_formatSafeTimestamp(a.uploadedAt)),
                    helper.buildTableCell(_getUploaderNameSimple(a, reportData)),
                ],
              )),
          ],
        ),
      ));
      widgets.add(pdfWidgets.SizedBox(height: 8));
    }
  }
  return pdfWidgets.Column(children: widgets);
}

/// Builds the progress trackers section (placeholder implementation).
///
/// [progressTrackers] - List of progress trackers.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildDetailedProgressTrackersSection(List<dynamic> progressTrackers, _EnhancedReportHelper helper) {
  return helper.buildEnhancedInfoContainer(
    title: 'متتبعات التقدم',
    color: PdfColors.grey100,
    borderColor: PdfConstants.borderColor,
    child: helper.buildInfoText('بيانات متتبعات التقدم غير متوفرة حالياً', color: PdfColors.grey600),
  );
}

/// Builds the time tracking section (placeholder implementation).
///
/// [timeTrackingEntries] - List of time tracking entries.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildDetailedTimeTrackingSection(List<dynamic> timeTrackingEntries, _EnhancedReportHelper helper) {
  return helper.buildEnhancedInfoContainer(
    title: 'سجلات الوقت',
    color: PdfColors.grey100,
    borderColor: PdfConstants.borderColor,
    child: helper.buildInfoText('بيانات سجلات الوقت غير متوفرة حالياً', color: PdfColors.grey600),
  );
}

/// Builds the subtasks section.
///
/// [subtasks] - List of subtasks.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildComprehensiveSubtasksSection(List<Subtask> subtasks, _EnhancedReportHelper helper) {
  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('8. المهام الفرعية الشاملة', PdfConstants.primaryColor),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      if (subtasks.isEmpty)
        helper.buildEnhancedInfoContainer(
          title: 'لا توجد مهام فرعية',
          color: PdfColors.grey100,
          borderColor: PdfConstants.borderColor,
          child: helper.buildInfoText('لم يتم إضافة أي مهام فرعية لهذه المهمة', color: PdfColors.grey600),
        )
      else
        helper.buildEnhancedInfoContainer(
          title: 'تفاصيل المهام الفرعية',
          color: PdfColors.white,
          borderColor: PdfConstants.accentColor,
          child: pdfWidgets.Table(
            border: pdfWidgets.TableBorder.all(color: PdfColors.black, width: 0.7), // حدود واضحة للجداول
            columnWidths: {
              0: const pdfWidgets.FlexColumnWidth(3),
              1: const pdfWidgets.FlexColumnWidth(2),
              2: const pdfWidgets.FlexColumnWidth(2),
            },
            children: [
              pdfWidgets.TableRow(
                decoration: const pdfWidgets.BoxDecoration(color: PdfColors.blue100),
                children: [
                  helper.buildTableHeader('العنوان'),
                  helper.buildTableHeader('الحالة'),
                  helper.buildTableHeader('تاريخ الإنشاء'),
                ],
              ),
              ...subtasks.map((subtask) => pdfWidgets.TableRow(
                children: [
                  helper.buildTableCell(subtask.title ?? '', isBold: true),
                  helper.buildTableCell(subtask.isCompleted ? 'مكتملة' : 'غير مكتملة'),
                  helper.buildTableCell(DateTimeHelpers.formatTimestamp(subtask.createdAt)),
                ],
              )),
            ],
          ),
        ),
    ],
  );
}

/// Builds the statistics section.
///
/// [statistics] - The report statistics.
/// [task] - The task for the report.
/// [reportModel] - The report model.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildComprehensiveStatisticsSection(TaskReportStatistics statistics, Task task, TaskReportModel reportModel, _EnhancedReportHelper helper) {
  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('9. الإحصائيات الشاملة', PdfConstants.primaryColor),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'ملخص الإحصائيات',
        color: PdfConstants.sectionBg,
        borderColor: PdfConstants.accentColor,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('عدد التعليقات:', '${statistics.totalComments}'),
            helper.buildInfoRow('عدد المرفقات:', '${statistics.totalAttachments}'),
            helper.buildInfoRow('عدد المهام الفرعية:', '${statistics.totalSubtasks}'),
            helper.buildInfoRow('المهام الفرعية المكتملة:', '${statistics.completedSubtasks}'),
          ],
        ),
      ),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'تحليل الأداء',
        color: PdfConstants.sectionBg,
        borderColor: PdfConstants.accentColor,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: [
            helper.buildInfoRow('معدل الإنجاز:', '${statistics.completionRate.toStringAsFixed(1)}%'),
            helper.buildInfoRow('الأيام النشطة:', '${statistics.daysActive} يوم'),
            helper.buildInfoRow('أكثر المساهمين نشاطاً:', statistics.mostActiveContributor),
            helper.buildInfoRow('آخر تحويل:', statistics.latestTransfer),
            helper.buildInfoRow('متوسط التعليقات يومياً:', statistics.daysActive > 0 ? (statistics.totalComments / statistics.daysActive).toStringAsFixed(1) : '0'),
          ],
        ),
      ),
    ],
  );
}

/// Builds the task history section.
///
/// [task] - The task for the report.
/// [reportData] - The comprehensive report data.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildComprehensiveTaskHistorySection(Task task, Map<String, dynamic> reportData, _EnhancedReportHelper helper) {
  final histories = (reportData['taskHistories'] as List<dynamic>?)?.map((e) => TaskHistory.fromJson(e)).toList() ?? _buildBasicTaskHistory(task);
  if (histories.isEmpty) {
    histories.add(TaskHistory(
      id: 1,
      taskId: task.id,
      userId: task.creator?.id ?? 0,
      action: 'إنشاء المهمة',
      details: 'تم إنشاء المهمة "${task.title}"',
      timestamp: task.createdAt,
      changedByNavigation: task.creator,
      user: task.creator,
    ));
  }

  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('10. سجل الأحداث التفصيلي الشامل', PdfConstants.primaryColor),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      if (histories.isNotEmpty)
        helper.buildEnhancedInfoContainer(
          title: 'ملخص الأحداث',
          color: PdfColors.grey50,
          borderColor: PdfConstants.borderColor,
          child: pdfWidgets.Column(
            crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
            children: [
              helper.buildInfoRow('إجمالي الأحداث:', '${histories.length} حدث'),
              helper.buildInfoRow('أول حدث:', DateTimeHelpers.formatDateTime(histories.first.timestampDateTime)),
              helper.buildInfoRow('آخر حدث:', DateTimeHelpers.formatDateTime(histories.last.timestampDateTime)),
            ],
          ),
        ),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      ...histories.map((history) => pdfWidgets.Container(
            margin: const pdfWidgets.EdgeInsets.only(bottom: 8),
            padding: const pdfWidgets.EdgeInsets.all(10),
            decoration: pdfWidgets.BoxDecoration(
              color: PdfColors.grey50,
              borderRadius: pdfWidgets.BorderRadius.circular(6),
              border: pdfWidgets.Border.all(color: PdfColors.black),
            ),
            child: pdfWidgets.Column(
              crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
              children: [
                pdfWidgets.Row(
                  mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
                  children: [
                    helper.buildInfoText(DateTimeHelpers.formatDateTime(history.timestampDateTime), fontSize: 10, color: PdfColors.grey600),
                    helper.buildInfoText(history.changedByNavigation?.name ?? history.user?.name ?? 'النظام', fontSize: 11, isBold: true, color: PdfColors.grey800),
                  ],
                ),
                pdfWidgets.SizedBox(height: 3),
                helper.buildInfoText(history.actionDescription, fontSize: 11, color: PdfColors.grey700),
                if (history.details != null && history.details!.isNotEmpty)
                  helper.buildInfoText('التفاصيل: ${history.details}', fontSize: 10, color: PdfColors.grey600),
              ],
            ),
          ),
          ),
    ],
  );
}

/// Builds a basic task history section as a fallback.
///
/// [task] - The task for the report.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildBasicTaskHistorySection(Task task, _EnhancedReportHelper helper) {
  final histories = _buildBasicTaskHistory(task);
  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader('10. سجل الأحداث التفصيلي الشامل', PdfConstants.primaryColor),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'الأحداث الأساسية',
        color: PdfColors.grey50,
        borderColor: PdfConstants.borderColor,
        child: pdfWidgets.Column(
          crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
          children: histories.isEmpty
              ? [helper.buildInfoText('لا توجد أحداث مسجلة', color: PdfColors.grey600)]
              : histories.map((history) => pdfWidgets.Container(
                    margin: const pdfWidgets.EdgeInsets.only(bottom: 8),
                    padding: const pdfWidgets.EdgeInsets.all(8),
                    decoration: pdfWidgets.BoxDecoration(
                      color: PdfColors.teal50,
                      borderRadius: pdfWidgets.BorderRadius.circular(4),
                      border: pdfWidgets.Border.all(color: PdfColors.teal200),
                    ),
                    child: pdfWidgets.Column(
                      crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
                      children: [
                        helper.buildInfoRow('الحدث:', history.actionDescription),
                        helper.buildInfoRow('بواسطة:', history.user?.name ?? 'النظام'),
                        helper.buildInfoRow('التاريخ:', DateTimeHelpers.formatDateTime(history.timestampDateTime)),
                      ],
                    ),
                  )).toList(),
        ),
      ),
    ],
  );
}

/// Builds contributors from task data.
///
/// [task] - The task to extract contributors from.
/// Returns a [List] of [TaskContributor].
List<TaskContributor> _buildContributorsFromTask(Task task) {
  final contributors = <TaskContributor>[];
  final addedUserIds = <int>{};

  try {
    if (task.creator != null && !addedUserIds.contains(task.creator!.id)) {
      contributors.add(TaskContributor(
        userId: task.creator!.id,
        userName: task.creator!.name,
        userEmail: task.creator!.email,
        role: 'المنشئ',
        commentsCount: task.comments.where((c) => c.user?.id == task.creator!.id).length,
        attachmentsCount: task.attachments.where((a) => a.uploadedByUser?.id == task.creator!.id).length,
        activitiesCount: 1,
        totalContributions: 0,
        level: ContributorLevel.medium,
      ));
      addedUserIds.add(task.creator!.id);
    }

    if (task.assignee != null && !addedUserIds.contains(task.assignee!.id)) {
      contributors.add(TaskContributor(
        userId: task.assignee!.id,
        userName: task.assignee!.name,
        userEmail: task.assignee!.email,
        role: 'المكلف',
        commentsCount: task.comments.where((c) => c.user?.id == task.assignee!.id).length,
        attachmentsCount: task.attachments.where((a) => a.uploadedByUser?.id == task.assignee!.id).length,
        activitiesCount: 1,
        totalContributions: 0,
        level: ContributorLevel.medium,
      ));
      addedUserIds.add(task.assignee!.id);
    }

    for (var comment in task.comments) {
      if (comment.user != null && !addedUserIds.contains(comment.user!.id)) {
        final commentsCount = task.comments.where((c) => c.user?.id == comment.user!.id).length;
        contributors.add(TaskContributor(
          userId: comment.user!.id,
          userName: comment.user!.name,
          userEmail: comment.user!.email,
          role: 'معلق',
          commentsCount: commentsCount,
          attachmentsCount: task.attachments.where((a) => a.uploadedByUser?.id == comment.user!.id).length,
          activitiesCount: commentsCount,
          totalContributions: 0,
          level: ContributorLevel.medium,
        ));
        addedUserIds.add(comment.user!.id);
      }
    }

    for (var attachment in task.attachments) {
      if (attachment.uploadedByUser != null && !addedUserIds.contains(attachment.uploadedByUser!.id)) {
        final attachmentsCount = task.attachments.where((a) => a.uploadedByUser?.id == attachment.uploadedByUser!.id).length;
        contributors.add(TaskContributor(
          userId: attachment.uploadedByUser!.id,
          userName: attachment.uploadedByUser!.name,
          userEmail: attachment.uploadedByUser!.email,
          role: 'رافع ملفات',
          commentsCount: task.comments.where((c) => c.user?.id == attachment.uploadedByUser!.id).length,
          attachmentsCount: attachmentsCount,
          activitiesCount: attachmentsCount,
          totalContributions: 0,
          level: ContributorLevel.medium,
        ));
        addedUserIds.add(attachment.uploadedByUser!.id);
      }
    }

    final updatedContributors = contributors.map((contributor) {
      final totalContributions = contributor.commentsCount + contributor.attachmentsCount + contributor.activitiesCount;
      return TaskContributor(
        userId: contributor.userId,
        userName: contributor.userName,
        userEmail: contributor.userEmail,
        role: contributor.role,
        commentsCount: contributor.commentsCount,
        attachmentsCount: contributor.attachmentsCount,
        activitiesCount: contributor.activitiesCount,
        totalContributions: totalContributions,
        level: ContributorLevel.fromContributions(totalContributions),
      );
    }).toList();

    updatedContributors.sort((a, b) => b.totalContributions.compareTo(a.totalContributions));
    final totalContributions = updatedContributors.fold<int>(0, (sum, c) => sum + c.totalContributions);

    return totalContributions > 0
        ? updatedContributors.map((contributor) {
            final percentage = (contributor.totalContributions / totalContributions) * 100;
            return TaskContributor(
              userId: contributor.userId,
              userName: contributor.userName,
              userEmail: contributor.userEmail,
              role: contributor.role,
              commentsCount: contributor.commentsCount,
              attachmentsCount: contributor.attachmentsCount,
              activitiesCount: contributor.activitiesCount,
                           totalContributions: contributor.totalContributions,
              level: contributor.level,
              percentage: percentage,
            );
          }).toList()
        : updatedContributors;
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في بناء المساهمين: $e');
    return [];
  }
}

/// Builds transfers from task data.
///
/// [task] - The task to extract transfers from.
/// Returns a [List] of [TaskTransfer].
List<TaskTransfer> _buildTransfersFromTask(Task task) {
  final transfers = <TaskTransfer>[];
  try {
    if (task.creator != null && task.assignee != null && task.creator!.id != task.assignee!.id) {
      transfers.add(TaskTransfer(
        fromUser: task.creator!.name,
        toUser: task.assignee!.name,
        reason: 'تكليف أولي للمهمة',
        executor: task.creator!.name,
        timestamp: task.createdAt,
        type: TransferType.initial,
      ));
    }
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في بناء التحويلات: $e');
  }
  return transfers;
}

/// Builds basic task history from task data.
///
/// [task] - The task to extract history from.
/// Returns a [List] of [TaskHistory].
List<TaskHistory> _buildBasicTaskHistory(Task task) {
  final histories = <TaskHistory>[];
  try {
    histories.add(TaskHistory(
      id: 1,
      taskId: task.id,
      userId: task.creator?.id ?? 0,
      action: 'تم إنشاء المهمة',
      details: 'تم إنشاء المهمة "${task.title}" بواسطة ${task.creator?.name ?? "النظام"}',
      timestamp: task.createdAt,
      changedByNavigation: task.creator,
      user: task.creator,
    ));

    if (task.assignee != null && task.assignee!.id != task.creator?.id) {
      histories.add(TaskHistory(
        id: 2,
        taskId: task.id,
        userId: task.creator?.id ?? 0,
        action: 'تم تكليف المهمة',
        details: 'تم تكليف المهمة إلى ${task.assignee!.name}',
        timestamp: task.createdAt,
        changedByNavigation: task.creator,
        user: task.creator,
      ));
    }

    task.comments.asMap().forEach((i, comment) {
      histories.add(TaskHistory(
        id: 3 + i,
        taskId: task.id,
        userId: comment.user?.id ?? 0,
        action: 'تم إضافة تعليق',
        details: 'تعليق: "${comment.content.length > 50 ? comment.content.substring(0, 50) + "..." : comment.content}"',
        timestamp: comment.createdAt,
        changedByNavigation: comment.user,
        user: comment.user,
      ));
    });

    task.attachments.asMap().forEach((i, attachment) {
      histories.add(TaskHistory(
        id: 100 + i,
        taskId: task.id,
        userId: attachment.uploadedByUser?.id ?? 0,
        action: 'تم رفع مرفق',
        details: 'ملف: ${attachment.fileName}',
        timestamp: attachment.uploadedAt,
        changedByNavigation: attachment.uploadedByUser,
        user: attachment.uploadedByUser,
      ));
    });

    if (task.completedAt != null && task.completedAt! > 0) {
      histories.add(TaskHistory(
        id: 200,
        taskId: task.id,
        userId: task.assignee?.id ?? 0,
        action: 'تم إنجاز المهمة',
        details: 'تم إنجاز المهمة بنسبة ${task.completionPercentage}%',
        timestamp: task.completedAt!,
        changedByNavigation: task.assignee,
        user: task.assignee,
      ));
    }

    histories.sort((a, b) => a.timestampDateTime.compareTo(b.timestampDateTime));
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في بناء سجل الأحداث: $e');
  }
  return histories;
}

/// Gets the uploader name for an attachment with simple logic.
///
/// [attachment] - The attachment to get the uploader name for.
/// [reportData] - The report data containing user information.
/// Returns the uploader name or a fallback string.
String _getUploaderNameSimple(Attachment attachment, Map<String, dynamic> reportData) {
  try {
    // 1. أولاً: محاولة الحصول على الاسم من uploadedByUser مباشرة
    if (attachment.uploadedByUser?.name != null && 
        attachment.uploadedByUser!.name.trim().isNotEmpty) {
      return attachment.uploadedByUser!.name.trim();
    }

    // 2. ثانياً: إذا كان معرف المستخدم 1، فهو المدير
    if (attachment.uploadedBy == 1) {
      return 'المدير';
    }

    // 3. ثالثاً: البحث في البيانات الأساسية للمهمة
    if (reportData.containsKey('task')) {
      final taskData = reportData['task'];
      if (taskData is Map<String, dynamic>) {
        // فحص المنشئ
        if (taskData.containsKey('creator') && taskData['creator'] is Map<String, dynamic>) {
          final creator = taskData['creator'] as Map<String, dynamic>;
          if (creator['id'] == attachment.uploadedBy) {
            return creator['name']?.toString().trim() ?? 'المنشئ';
          }
        }
        
        // فحص المكلف
        if (taskData.containsKey('assignee') && taskData['assignee'] is Map<String, dynamic>) {
          final assignee = taskData['assignee'] as Map<String, dynamic>;
          if (assignee['id'] == attachment.uploadedBy) {
            return assignee['name']?.toString().trim() ?? 'المكلف';
          }
        }
      }
    }

    // 4. رابعاً: البحث في قائمة accessUsers بطريقة مبسطة
    if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
      final accessUsers = reportData['accessUsers'] as List;
      for (var user in accessUsers) {
        if (user is Map<String, dynamic> && 
            user['id'] == attachment.uploadedBy && 
            user['name'] != null) {
          return user['name'].toString().trim();
        }
      }
    }

    // 5. أخيراً: نص بديل واضح
    return 'مستخدم ${attachment.uploadedBy}';
    
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في الحصول على اسم رافع الملف: $e');
    return 'غير محدد';
  }
}

/// Gets the uploader name for an attachment with enhanced fallback logic.
///
/// [attachment] - The attachment to get the uploader name for.
/// [reportData] - The report data containing user information.
/// Returns the uploader name or a fallback string.
String _getUploaderName(Attachment attachment, Map<String, dynamic> reportData) {
  try {
    if (kDebugMode) {
      print('🔍 البحث عن اسم رافع الملف: ${attachment.fileName}');
      print('   - معرف رافع الملف: ${attachment.uploadedBy}');
      print('   - uploadedByUser: ${attachment.uploadedByUser?.name}');
    }

    // 1. محاولة الحصول على الاسم من uploadedByUser مباشرة
    if (attachment.uploadedByUser?.name != null && 
        attachment.uploadedByUser!.name.trim().isNotEmpty) {
      if (kDebugMode) print('✅ تم العثور على الاسم من uploadedByUser: ${attachment.uploadedByUser!.name}');
      return attachment.uploadedByUser!.name.trim();
    }

    // 2. البحث في المهمة نفسها (creator و assignee)
    if (reportData.containsKey('task') && reportData['task'] is Map<String, dynamic>) {
      final taskData = reportData['task'] as Map<String, dynamic>;
      
      // البحث في creator
      if (taskData.containsKey('creator') && taskData['creator'] is Map<String, dynamic>) {
        final creator = taskData['creator'] as Map<String, dynamic>;
        if (creator['id'] == attachment.uploadedBy && creator['name'] != null) {
          if (kDebugMode) print('✅ تم العثور على الاسم من creator: ${creator['name']}');
          return creator['name'].toString().trim();
        }
      }
      
      // البحث في assignee
      if (taskData.containsKey('assignee') && taskData['assignee'] is Map<String, dynamic>) {
        final assignee = taskData['assignee'] as Map<String, dynamic>;
        if (assignee['id'] == attachment.uploadedBy && assignee['name'] != null) {
          if (kDebugMode) print('✅ تم العثور على الاسم من assignee: ${assignee['name']}');
          return assignee['name'].toString().trim();
        }
      }
    }

    // 3. البحث في قائمة accessUsers
    if (reportData.containsKey('accessUsers') && reportData['accessUsers'] is List) {
      final accessUsers = reportData['accessUsers'] as List;
      if (kDebugMode) print('🔍 البحث في accessUsers: ${accessUsers.length} مستخدم');
      
      for (var userMap in accessUsers) {
        if (userMap is Map<String, dynamic>) {
          if (kDebugMode) print('   - فحص مستخدم: id=${userMap['id']}, name=${userMap['name']}');
          if (userMap['id'] == attachment.uploadedBy &&
              userMap['name'] != null && 
              userMap['name'].toString().trim().isNotEmpty) {
            if (kDebugMode) print('✅ تم العثور على الاسم من accessUsers: ${userMap['name']}');
            return userMap['name'].toString().trim();
          }
        }
      }
    }

    // 4. البحث في التعليقات للعثور على المستخدم
    if (reportData.containsKey('comments') && reportData['comments'] is List) {
      final comments = reportData['comments'] as List;
      if (kDebugMode) print('🔍 البحث في التعليقات: ${comments.length} تعليق');
      
      for (var commentMap in comments) {
        if (commentMap is Map<String, dynamic> && 
            commentMap.containsKey('user') &&
            commentMap['user'] is Map<String, dynamic>) {
          final userMap = commentMap['user'] as Map<String, dynamic>;
          if (userMap['id'] == attachment.uploadedBy && 
              userMap['name'] != null && 
              userMap['name'].toString().trim().isNotEmpty) {
            if (kDebugMode) print('✅ تم العثور على الاسم من التعليقات: ${userMap['name']}');
            return userMap['name'].toString().trim();
          }
        }
      }
    }

    // 5. البحث في قائمة contributors
    if (reportData.containsKey('contributors') && reportData['contributors'] is List) {
      final contributors = reportData['contributors'] as List;
      if (kDebugMode) print('🔍 البحث في contributors: ${contributors.length} مساهم');
      
      for (var contributorMap in contributors) {
        if (contributorMap is Map<String, dynamic>) {
          if (kDebugMode) print('   - فحص مساهم: userId=${contributorMap['userId']}, userName=${contributorMap['userName']}');
          if (contributorMap['userId'] == attachment.uploadedBy &&
              contributorMap['userName'] != null && 
              contributorMap['userName'].toString().trim().isNotEmpty) {
            if (kDebugMode) print('✅ تم العثور على الاسم من contributors: ${contributorMap['userName']}');
            return contributorMap['userName'].toString().trim();
          }
        }
      }
    }

    // 6. البحث في المرفقات الأخرى للعثور على نفس المستخدم
    if (reportData.containsKey('attachments') && reportData['attachments'] is List) {
      final attachments = reportData['attachments'] as List;
      if (kDebugMode) print('🔍 البحث في المرفقات الأخرى: ${attachments.length} مرفق');
      for (var attachmentMap in attachments) {
        if (attachmentMap is Map<String, dynamic> && 
            attachmentMap['uploadedBy'] == attachment.uploadedBy &&
            attachmentMap.containsKey('uploadedByUser') &&
            attachmentMap['uploadedByUser'] is Map<String, dynamic>) {
          final userMap = attachmentMap['uploadedByUser'] as Map<String, dynamic>;
          if (userMap['name'] != null && userMap['name'].toString().trim().isNotEmpty) {
            if (kDebugMode) print('✅ تم العثور على الاسم من مرفق آخر: ${userMap['name']}');
            return userMap['name'].toString().trim();
          }
        }
      }
    }

    // 7. البحث في taskHistories للعثور على المستخدم
    if (reportData.containsKey('taskHistories') && reportData['taskHistories'] is List) {
      final histories = reportData['taskHistories'] as List;
      if (kDebugMode) print('🔍 البحث في taskHistories: ${histories.length} سجل');
      
      for (var historyMap in histories) {
        if (historyMap is Map<String, dynamic> && 
            historyMap['userId'] == attachment.uploadedBy &&
            historyMap['user'] is Map<String, dynamic>) {
          final userMap = historyMap['user'] as Map<String, dynamic>;
          if (userMap['name'] != null && userMap['name'].toString().trim().isNotEmpty) {
            if (kDebugMode) print('✅ تم العثور على الاسم من taskHistories: ${userMap['name']}');
            return userMap['name'].toString().trim();
          }
        }
      }
    }

    // 7. إذا لم نجد الاسم، طباعة تشخيص وإرجاع نص بديل
    if (kDebugMode) {
      print('⚠️ لم يتم العثور على اسم رافع الملف');
      print('   - البيانات المتاحة في reportData:');
      reportData.keys.forEach((key) {
        if (reportData[key] is List) {
          print('     - $key: ${(reportData[key] as List).length} عنصر');
        } else {
          print('     - $key: ${reportData[key].runtimeType}');
        }
      });
    }
    
    if (attachment.uploadedBy != null && attachment.uploadedBy > 0) {
      return 'مستخدم غير معروف';
    } else {
      return 'غير محدد';
    }
  } catch (e) {
    if (kDebugMode) print('⚠️ خطأ في الحصول على اسم رافع الملف: $e');
    return 'غير محدد';
  }
}

/// Builds a fallback section for failed data loading.
///
/// [title] - The section title.
/// [message] - The error message.
/// [helper] - The enhanced report helper.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildFallbackSection(String title, String message, _EnhancedReportHelper helper) {
  return pdfWidgets.Column(
    crossAxisAlignment: pdfWidgets.CrossAxisAlignment.end,
    children: [
      helper.buildSectionHeader(title, PdfColors.grey600),
      pdfWidgets.SizedBox(height: PdfConstants.sectionSpacing),
      helper.buildEnhancedInfoContainer(
        title: 'تنبيه',
        color: PdfColors.orange50,
        borderColor: PdfColors.orange300,
        child: helper.buildInfoText(message, color: PdfColors.orange700),
      ),
    ],
  );
}

/// Builds the page header for MultiPage sections.
///
/// [helper] - The enhanced report helper.
/// [sectionTitle] - The title of the section.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildPageHeader(_EnhancedReportHelper helper, String sectionTitle) {
  return pdfWidgets.Container(
    padding: const pdfWidgets.EdgeInsets.only(bottom: 10),
    decoration: const pdfWidgets.BoxDecoration(
      border: pdfWidgets.Border(bottom: pdfWidgets.BorderSide(color: PdfConstants.borderColor, width: 1)),
    ),
    child: pdfWidgets.Row(
      mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
      children: [
        pdfWidgets.Text(
          DateTimeHelpers.formatDateTime(DateTime.now()),
          style: pdfWidgets.TextStyle(
            font: helper.fonts['regular'],
            fontSize: 10,
            color: PdfColors.grey600,
          ),
          textAlign: pdfWidgets.TextAlign.right,
          textDirection: pdfWidgets.TextDirection.rtl,
        ),
        pdfWidgets.Text(
          sectionTitle,
          style: pdfWidgets.TextStyle(
            font: helper.fonts['bold'],
            fontSize: 12,
            color: PdfColors.grey800,
          ),
          textAlign: pdfWidgets.TextAlign.center,
          textDirection: pdfWidgets.TextDirection.rtl,
        ),
        pdfWidgets.Text(
          'تقرير المهمة الشامل',
          style: pdfWidgets.TextStyle(
            font: helper.fonts['regular'],
            fontSize: 10,
            color: PdfColors.grey600,
          ),
          textAlign: pdfWidgets.TextAlign.right,
          textDirection: pdfWidgets.TextDirection.rtl,
        ),
      ],
    ),
  );
}

/// Builds the page footer for MultiPage sections.
///
/// [helper] - The enhanced report helper.
/// [context] - The PDF context.
/// Returns a [pdfWidgets.Widget].
pdfWidgets.Widget _buildPageFooter(_EnhancedReportHelper helper, pdfWidgets.Context context) {
  return pdfWidgets.Container(
    padding: const pdfWidgets.EdgeInsets.only(top: 10),
    decoration: const pdfWidgets.BoxDecoration(
      border: pdfWidgets.Border(top: pdfWidgets.BorderSide(color: PdfConstants.borderColor, width: 1)),
    ),
    child: pdfWidgets.Row(
      mainAxisAlignment: pdfWidgets.MainAxisAlignment.spaceBetween,
      children: [
        pdfWidgets.Text(
          'تم إنشاؤه بواسطة نظام إدارة المهام',
          style: pdfWidgets.TextStyle(
            font: helper.fonts['regular'],
            fontSize: 9,
            color: PdfColors.grey500,
          ),
          textAlign: pdfWidgets.TextAlign.right,
          textDirection: pdfWidgets.TextDirection.rtl,
        ),
        pdfWidgets.Text(
          'صفحة ${context.pageNumber}',
          style: pdfWidgets.TextStyle(
            font: helper.fonts['bold'],
            fontSize: 10,
            color: PdfColors.grey700,
          ),
          textAlign: pdfWidgets.TextAlign.center,
          textDirection: pdfWidgets.TextDirection.rtl,
        ),
      ],
    ),
  );
}

/// Gets file types from attachments.
///
/// [attachments] - List of attachments.
/// Returns a comma-separated string of file types.
String _getFileTypes(List<Attachment> attachments) {
  final types = attachments.map((a) => a.fileType).whereType<String>().toSet().toList();
  return types.isEmpty ? 'غير معروف' : types.join(', ');
}

/// Builds all content sections as widgets for a single MultiPage.
List<pdfWidgets.Widget> _buildComprehensiveContentSections(
  Task task,
  TaskReportModel reportModel,
  Map<String, dynamic> reportData,
  _EnhancedReportHelper helper,
  List<TocEntry> tocEntries,
  Set<String> includedSections,
) {
  final sections = <pdfWidgets.Widget>[];
  // ملخص المهمة
  if (includedSections.contains('summary')) {
    sections.add(_buildComprehensiveTaskSummary(task, reportModel, helper));
  }
  // المساهمون
  if (includedSections.contains('contributors')) {
    sections.add(_buildContributorsSection(reportModel.contributors, task, reportData, helper));
  }
  // التحويلات
  if (includedSections.contains('transfers')) {
    sections.add(_buildTransfersSection(reportModel.transfers, task, helper));
  }
  // التعليقات
  if (includedSections.contains('comments')) {
    sections.add(_buildCommentsSection(task.comments, helper));
  }
  // المرفقات
  if (includedSections.contains('attachments')) {
    sections.add(_buildAttachmentsSection(task.attachments, helper, reportData));
  }
  // المهام الفرعية
  if (includedSections.contains('subtasks')) {
    sections.add(_buildComprehensiveSubtasksSection(task.subtasks, helper));
  }
  // الإحصائيات
  if (includedSections.contains('statistics')) {
    sections.add(_buildComprehensiveStatisticsSection(reportModel.statistics, task, reportModel, helper));
  }
  // السجل التاريخي
  // if (includedSections.contains('history')) {
  //   sections.add(_buildComprehensiveTaskHistorySection(task, reportData, helper));
  // }
  return sections;
}