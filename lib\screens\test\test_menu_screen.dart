import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../routes/app_routes.dart';

/// شاشة قائمة الاختبارات - تحتوي على روابط لجميع صفحات الاختبار
class TestMenuScreen extends StatelessWidget {
  const TestMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة الاختبارات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                children: [
                  Icon(
                    Icons.science,
                    size: 48,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'صفحات الاختبار والتطوير',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'هذه الصفحة تحتوي على روابط لجميع صفحات الاختبار في التطبيق',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // قائمة الاختبارات
            Expanded(
              child: ListView(
                children: [
                  // لوحة التحكم المحسنة الجديدة ⭐
                  _buildTestCard(
                    title: 'لوحة التحكم المحسنة',
                    description: 'النظام الجديد المطور بالكامل مع Material Design 3',
                    icon: Icons.dashboard_rounded,
                    color: Colors.blue,
                    onTap: () => Get.toNamed(AppRoutes.enhancedAdmin),
                    isNew: true,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // اختبار لوحة التحكم المحسنة
                  _buildTestCard(
                    title: 'اختبار لوحة التحكم المحسنة',
                    description: 'صفحة اختبار ومقارنة بين النظام القديم والجديد',
                    icon: Icons.science_rounded,
                    color: Colors.orange,
                    onTap: () => Get.toNamed(AppRoutes.testEnhancedAdmin),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // اختبار المحرر المحسن
                  _buildTestCard(
                    title: 'اختبار المحرر المحسن',
                    description: 'اختبار محرر Flutter Quill مع إصلاح مشكلة ترتيب الأسطر',
                    icon: Icons.edit_document,
                    color: Colors.green,
                    onTap: () => Get.toNamed(AppRoutes.testEditor),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // اختبار المستخدمين
                  // _buildTestCard(
                  //   title: 'اختبار المستخدمين',
                  //   description: 'اختبار إنشاء وإدارة المستخدمين في النظام',
                  //   icon: Icons.people,
                  //   color: Colors.orange,
                  //   // onTap: () => Get.toNamed(AppRoutes.testUsers),
                  // ),
                  
                  const SizedBox(height: 12),
                  
                  // اختبار تخزين الملفات
                  _buildTestCard(
                    title: 'اختبار تخزين الملفات',
                    description: 'اختبار رفع وتحميل الملفات في النظام',
                    icon: Icons.file_upload,
                    color: Colors.purple,
                    onTap: () => Get.toNamed(AppRoutes.fileStorageTest),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // اختبار التشخيص
                  _buildTestCard(
                    title: 'تشخيص النظام',
                    description: 'فحص حالة النظام والاتصالات',
                    icon: Icons.health_and_safety,
                    color: Colors.red,
                    onTap: () => Get.toNamed(AppRoutes.diagnostics),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // ملاحظة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info,
                    color: Colors.amber,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'هذه الصفحات مخصصة للاختبار والتطوير فقط',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.amber,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة اختبار
  Widget _buildTestCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isNew = false,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // النص
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              
              // سهم
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}