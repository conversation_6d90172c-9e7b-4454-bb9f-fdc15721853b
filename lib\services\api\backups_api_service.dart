import 'package:flutter/foundation.dart';
import '../../models/system_models.dart';
import 'api_service.dart';

/// خدمة API موحدة للنسخ الاحتياطية - متطابقة مع ASP.NET Core API
/// تجمع جميع وظائف إدارة النسخ الاحتياطية في خدمة واحدة محسنة
class BackupsApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع النسخ الاحتياطية
  Future<List<Backup>> getAllBackups() async {
    try {
      final response = await _apiService.get('/api/Backups');
      return _apiService.handleListResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل النسخ الاحتياطية: $e');
    }
  }

  /// الحصول على نسخة احتياطية بالمعرف
  Future<Backup> getBackupById(int id) async {
    try {
      final response = await _apiService.get('/api/Backups/$id');
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في تحميل النسخة الاحتياطية: $e');
    }
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<Backup> createBackup(String description) async {
    try {
      final response = await _apiService.post('/api/Backups/create', {
        'description': description,
        'fileName': 'backup_${DateTime.now().millisecondsSinceEpoch}.sql',
        'filePath': '/backups/',
        'fileSize': 0,
        'createdBy': 1, // TODO: استخدام معرف المستخدم الحالي
        'isAutoBackup': false,
      });
      return _apiService.handleResponse<Backup>(
        response,
        (json) => Backup.fromJson(json),
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء النسخة الاحتياطية: $e');
    }
  }

  /// استعادة نسخة احتياطية
  Future<void> restoreBackup(int backupId) async {
    try {
      await _apiService.post('/api/Backups/$backupId/restore', {});
    } catch (e) {
      throw Exception('خطأ في استعادة النسخة الاحتياطية: $e');
    }
  }

  /// حذف نسخة احتياطية
  Future<void> deleteBackup(int backupId) async {
    try {
      await _apiService.delete('/api/Backups/$backupId');
    } catch (e) {
      throw Exception('خطأ في حذف النسخة الاحتياطية: $e');
    }
  }

  /// تحميل نسخة احتياطية
  Future<String> downloadBackup(int backupId) async {
    try {
      final response = await _apiService.get('/api/Backups/$backupId/download');
      return _apiService.handleResponse<String>(
        response,
        (json) => json['downloadUrl'] as String,
      );
    } catch (e) {
      throw Exception('خطأ في تحميل النسخة الاحتياطية: $e');
    }
  }

  /// الحصول على حالة النسخ الاحتياطي
  Future<Map<String, dynamic>> getBackupStatus() async {
    try {
      final response = await _apiService.get('/api/Backups/status');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على حالة النسخ الاحتياطي: $e');
    }
  }

  /// جدولة نسخة احتياطية تلقائية
  Future<void> scheduleBackup(Map<String, dynamic> scheduleData) async {
    try {
      await _apiService.post('/api/Backups/schedule', scheduleData);
    } catch (e) {
      throw Exception('خطأ في جدولة النسخة الاحتياطية: $e');
    }
  }

  /// إلغاء جدولة النسخ الاحتياطي
  Future<void> cancelScheduledBackup(int scheduleId) async {
    try {
      await _apiService.delete('/api/Backups/schedule/$scheduleId');
    } catch (e) {
      throw Exception('خطأ في إلغاء جدولة النسخة الاحتياطية: $e');
    }
  }

  /// الحصول على إعدادات النسخ الاحتياطي
  Future<Map<String, dynamic>> getBackupSettings() async {
    try {
      final response = await _apiService.get('/api/Backups/settings');
      return _apiService.handleResponse<Map<String, dynamic>>(
        response,
        (json) => json,
      );
    } catch (e) {
      throw Exception('خطأ في الحصول على إعدادات النسخ الاحتياطي: $e');
    }
  }

  /// تحديث إعدادات النسخ الاحتياطي
  Future<void> updateBackupSettings(Map<String, dynamic> settings) async {
    try {
      await _apiService.put('/api/Backups/settings', settings);
    } catch (e) {
      throw Exception('خطأ في تحديث إعدادات النسخ الاحتياطي: $e');
    }
  }

  /// التحقق من صحة النسخة الاحتياطية
  Future<bool> validateBackup(int backupId) async {
    try {
      final response = await _apiService.post('/api/Backups/$backupId/validate', {});
      return _apiService.handleResponse<bool>(
        response,
        (json) => json['isValid'] as bool,
      );
    } catch (e) {
      throw Exception('خطأ في التحقق من صحة النسخة الاحتياطية: $e');
    }
  }
}
