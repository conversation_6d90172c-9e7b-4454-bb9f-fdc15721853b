import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/task_report_models.dart';
import 'package:flutter_application_2/services/api/api_service.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:convert';

/// مساعد تنسيق التواريخ
class DateTimeHelpers {
  static String formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
  }
  
  static String formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
  
  static String formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute}';
  }
}

/// خدمة إنشاء التقرير الشامل للمهمة - بيانات حقيقية فقط
/// 
/// 🎯 المبادئ الأساسية:
/// - لا بيانات افتراضية أو تجريبية
/// - جميع البيانات من قاعدة البيانات
/// - استخدام جميع ميزات مكتبة PDF المتقدمة
/// - دعم كامل للعربية وRTL
/// - تقرير شامل ومفصل
class ComprehensiveTaskReportService {
  final ApiService _apiService = ApiService();
  
  /// إنشاء تقرير شامل للمهمة بالبيانات الحقيقية فقط
  /// 
  /// [taskId] - معرف المهمة
  /// [onProgress] - دالة لتتبع التقدم
  /// [includeAttachmentPreviews] - تضمين معاينات المرفقات
  /// [generateStatistics] - إنشاء إحصائيات تفصيلية
  Future<pw.Document> generateComprehensiveReport(
    int taskId, {
    Function(String message, double progress)? onProgress,
    bool includeAttachmentPreviews = false,
    bool generateStatistics = true,
  }) async {
    try {
      onProgress?.call('🚀 بدء إنشاء التقرير الشامل...', 0.0);
      
      // 1. تحميل البيانات الشاملة من API
      final comprehensiveData = await _fetchComprehensiveTaskData(taskId);
      onProgress?.call('📊 تم تحميل البيانات الشاملة', 0.1);
      
      // 2. تحميل الخطوط العربية
      final fonts = await _loadArabicFonts();
      onProgress?.call('🔤 تم تحميل الخطوط العربية', 0.2);
      
      // 3. معالجة وتنظيم البيانات
      final processedData = await _processTaskData(comprehensiveData);
      onProgress?.call('⚙️ تم معالجة وتنظيم البيانات', 0.3);
      
      // 4. إنشاء المستند بالبيانات الحقيقية
      final pdf = await _buildComprehensivePdfDocument(
        processedData,
        fonts,
        onProgress,
        includeAttachmentPreviews,
        generateStatistics,
      );
      
      onProgress?.call('✅ تم إنشاء التقرير الشامل بنجاح', 1.0);
      return pdf;
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إنشاء التقرير الشامل: $e');
      }
      rethrow;
    }
  }
  
  /// تحميل البيانات الشاملة للمهمة من API
  Future<Map<String, dynamic>> _fetchComprehensiveTaskData(int taskId) async {
    try {
      if (kDebugMode) {
        print('🔄 جلب البيانات الشاملة للمهمة $taskId من API...');
      }
      
      final response = await _apiService.get('/api/Tasks/$taskId/comprehensive-report-data');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (kDebugMode) {
          print('✅ تم جلب البيانات الشاملة بنجاح');
          print('📊 المهمة: ${data['task']?['title'] ?? 'غير محدد'}');
          print('👥 المساهمون: ${data['accessUsers']?.length ?? 0}');
          print('📝 التعليقات: ${data['task']?['taskComments']?.length ?? 0}');
          print('📎 المرفقات: ${data['task']?['attachments']?.length ?? 0}');
          print('📊 متتبعات التقدم: ${data['taskProgressTrackers']?.length ?? 0}');
          print('⏱️ سجلات الوقت: ${data['timeTrackingEntries']?.length ?? 0}');
          print('📚 السجل التاريخي: ${data['task']?['taskHistories']?.length ?? 0}');
        }
        
        return data;
      } else {
        throw Exception('فشل في جلب البيانات: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب البيانات الشاملة: $e');
      }
      throw Exception('تعذر جلب بيانات المهمة: $e');
    }
  }
  
  /// معالجة وتنظيم البيانات المستلمة
  Future<ProcessedTaskData> _processTaskData(Map<String, dynamic> rawData) async {
    if (kDebugMode) {
      print('⚙️ بدء معالجة البيانات...');
    }
    
    // استخراج المهمة الأساسية
    final taskData = rawData['task'] as Map<String, dynamic>? ?? {};
    final task = Task.fromJson(taskData);
    
    // استخراج المساهمين من accessUsers
    final contributors = <TaskContributor>[];
    final accessUsers = rawData['accessUsers'] as List? ?? [];
    
    for (var userJson in accessUsers) {
      if (userJson is Map<String, dynamic>) {
        final contributor = TaskContributor(
          userId: userJson['id'] ?? 0,
          userName: userJson['name'] ?? 'مستخدم غير معروف',
          userEmail: userJson['email'] ?? '',
          role: userJson['role'] ?? 'مساهم',
          commentsCount: _countUserContributions(userJson['id'], taskData['taskComments']),
          attachmentsCount: _countUserAttachments(userJson['id'], taskData['attachments']),
          activitiesCount: _countUserActivities(userJson['id'], rawData['activityLogs']),
          totalContributions: 0, // سيتم حسابه
          level: ContributorLevel.basic,
        );
        
        // المساهمات ومستوى النشاط يُحسبا تلقائياً في TaskContributor.fromAccessUser
        
        contributors.add(contributor);
      }
    }
    
    // استخراج متتبعات التقدم
    final progressTrackers = <TaskProgressTracker>[];
    final trackersData = rawData['taskProgressTrackers'] as List? ?? [];
    
    for (var trackerJson in trackersData) {
      if (trackerJson is Map<String, dynamic>) {
        final tracker = TaskProgressTracker.fromJson(trackerJson);
        progressTrackers.add(tracker);
      }
    }
    
    // استخراج سجلات الوقت
    final timeEntries = <TimeTrackingEntry>[];
    final timeData = rawData['timeTrackingEntries'] as List? ?? [];
    
    for (var entryJson in timeData) {
      if (entryJson is Map<String, dynamic>) {
        final entry = TimeTrackingEntry.fromJson(entryJson);
        timeEntries.add(entry);
      }
    }
    
    // استخراج التحويلات من TaskHistories
    final transfers = <TaskTransfer>[];
    final histories = taskData['taskHistories'] as List? ?? [];
    
    for (var historyJson in histories) {
      if (historyJson is Map<String, dynamic>) {
        final action = (historyJson['action'] ?? '').toString().toLowerCase();
        
        // التحقق من كون الإجراء تحويل
        if (action.contains('assign') || action.contains('transfer') || 
            action.contains('تحويل') || action.contains('تكليف')) {
          
          final transfer = TaskTransfer(
            fromUser: historyJson['oldValue'] ?? 'النظام',
            toUser: historyJson['newValue'] ?? 'غير معروف',
            reason: historyJson['details'] ?? 'تحويل المهمة',
            timestamp: historyJson['timestamp'] ?? 0,
            executor: historyJson['user']?['name'] ?? 'النظام',
            type: TransferType.manual,
          );
          transfers.add(transfer);
        }
      }
    }
    
    // حساب الإحصائيات من البيانات الحقيقية
    final statistics = TaskReportStatistics.calculate(
      task, contributors, transfers, progressTrackers, timeEntries
    );
    
    if (kDebugMode) {
      print('✅ تم معالجة البيانات:');
      print('   - المساهمون: ${contributors.length}');
      print('   - متتبعات التقدم: ${progressTrackers.length}');
      print('   - سجلات الوقت: ${timeEntries.length}');
      print('   - التحويلات: ${transfers.length}');
    }
    
    return ProcessedTaskData(
      task: task,
      contributors: contributors,
      transfers: transfers,
      progressTrackers: progressTrackers,
      timeTrackingEntries: timeEntries,
      statistics: statistics,
      rawData: rawData,
    );
  }
  
  /// بناء مستند PDF شامل ومتقدم
  Future<pw.Document> _buildComprehensivePdfDocument(
    ProcessedTaskData data,
    Map<String, pw.Font> fonts,
    Function(String, double)? onProgress,
    bool includeAttachmentPreviews,
    bool generateStatistics,
  ) async {
    final pdf = pw.Document(
      title: 'التقرير الشامل للمهمة: ${data.task.title}',
      author: 'نظام إدارة المهام',
      subject: 'تقرير مفصل شامل',
      creator: 'ComprehensiveTaskReportService',
      producer: 'Flutter PDF Library',
      keywords: 'مهمة, تقرير, شامل, إدارة',
    );
    
    // إعداد الثيم الموحد للمستند
    final theme = pw.ThemeData.withFont(
      base: fonts['regular']!,
      bold: fonts['bold']!,
    );
    
    onProgress?.call('📄 إنشاء صفحة الغلاف المتقدمة...', 0.4);
    
    // 1. صفحة الغلاف المتقدمة
    pdf.addPage(_buildAdvancedCoverPage(data, theme));
    
    onProgress?.call('📋 إنشاء فهرس المحتويات...', 0.5);
    
    // 2. فهرس المحتويات المفصل
    pdf.addPage(_buildDetailedTableOfContents(data, theme));
    
    onProgress?.call('📊 إنشاء ملخص المهمة التنفيذي...', 0.6);
    
    // 3. الملخص التنفيذي للمهمة
    pdf.addPage(_buildExecutiveSummary(data, theme));
    
    onProgress?.call('👥 إنشاء تحليل المساهمين المفصل...', 0.7);
    
    // 4. تحليل المساهمين المفصل مع الرسوم البيانية
    if (data.contributors.isNotEmpty) {
      pdf.addPage(_buildAdvancedContributorsAnalysis(data, theme));
    }
    
    onProgress?.call('📈 إنشاء تحليل التقدم والأداء...', 0.8);
    
    // 5. تحليل التقدم والأداء
    if (data.progressTrackers.isNotEmpty) {
      pdf.addPage(_buildProgressAnalysis(data, theme));
    }
    
    // 6. تحليل الوقت والإنتاجية
    if (data.timeTrackingEntries.isNotEmpty) {
      pdf.addPage(_buildTimeAnalysis(data, theme));
    }
    
    // 7. سجل الأحداث التفصيلي
    if (data.transfers.isNotEmpty) {
      pdf.addPage(_buildDetailedHistoryLog(data, theme));
    }
    
    // 8. التعليقات والتفاعل
    if (data.task.comments.isNotEmpty) {
      pdf.addPage(_buildCommentsAnalysis(data, theme));
    }
    
    // 9. المرفقات والوثائق
    if (data.task.attachments.isNotEmpty) {
      pdf.addPage(_buildAttachmentsSection(data, theme, includeAttachmentPreviews));
    }
    
    // 10. الإحصائيات والتحليلات المتقدمة
    if (generateStatistics) {
      pdf.addPage(_buildAdvancedStatistics(data, theme));
    }
    
    onProgress?.call('📝 إنشاء الملحقات والتوصيات...', 0.9);
    
    // 11. التوصيات والملحقات
    pdf.addPage(_buildRecommendationsAndAppendices(data, theme));
    
    return pdf;
  }
  
  /// صفحة غلاف متقدمة مع تصميم احترافي
  pw.Page _buildAdvancedCoverPage(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Container(
        decoration: pw.BoxDecoration(
          gradient: pw.LinearGradient(
            begin: pw.Alignment.topRight,
            end: pw.Alignment.bottomLeft,
            colors: [
              PdfColors.blue900,
              PdfColors.blue700,
              PdfColors.blue500,
            ],
          ),
        ),
        child: pw.Column(
          children: [
            // الرأس العلوي
            pw.Container(
              height: 150,
              child: pw.Center(
                child: pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'التقرير الشامل للمهمة',
                      style: pw.TextStyle(
                        fontSize: 32,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Container(
                      width: 200,
                      height: 3,
                      color: PdfColors.orange,
                    ),
                  ],
                ),
              ),
            ),
            
            // المحتوى الرئيسي
            pw.Expanded(
              child: pw.Container(
                margin: pw.EdgeInsets.all(40),
                padding: pw.EdgeInsets.all(30),
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  borderRadius: pw.BorderRadius.circular(15),
                  boxShadow: [
                    pw.BoxShadow(
                      color: PdfColors.grey400,
                      // offset: const pw.Offset(0.0, 10.0), // معطل مؤقتاً
                      blurRadius: 20,
                    ),
                  ],
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    // عنوان المهمة
                    pw.Container(
                      width: double.infinity,
                      padding: pw.EdgeInsets.all(20),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.grey100,
                        borderRadius: pw.BorderRadius.circular(10),
                        border: pw.Border.all(color: PdfColors.blue200),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            'عنوان المهمة',
                            style: pw.TextStyle(
                              fontSize: 14,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.grey600,
                            ),
                          ),
                          pw.SizedBox(height: 8),
                          pw.Text(
                            data.task.title,
                            style: pw.TextStyle(
                              fontSize: 24,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.blue900,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    pw.SizedBox(height: 30),
                    
                    // معلومات أساسية في شكل كروت
                    pw.Row(
                      children: [
                        pw.Expanded(
                          child: _buildInfoCard(
                            'رقم المهمة',
                            '#${data.task.id}',
                            PdfColors.green,
                          ),
                        ),
                        pw.SizedBox(width: 15),
                        pw.Expanded(
                          child: _buildInfoCard(
                            'الحالة',
                            data.task.status,
                            PdfColors.orange,
                          ),
                        ),
                        pw.SizedBox(width: 15),
                        pw.Expanded(
                          child: _buildInfoCard(
                            'الأولوية',
                            data.task.priority,
                            PdfColors.red,
                          ),
                        ),
                      ],
                    ),
                    
                    pw.SizedBox(height: 20),
                    
                    pw.Row(
                      children: [
                        pw.Expanded(
                          child: _buildInfoCard(
                            'نسبة الإنجاز',
                            '${data.task.completionPercentage}%',
                            PdfColors.blue,
                          ),
                        ),
                        pw.SizedBox(width: 15),
                        pw.Expanded(
                          child: _buildInfoCard(
                            'المساهمون',
                            '${data.contributors.length} شخص',
                            PdfColors.purple,
                          ),
                        ),
                        pw.SizedBox(width: 15),
                        pw.Expanded(
                          child: _buildInfoCard(
                            'التعليقات',
                            '${data.task.comments.length}',
                            PdfColors.teal,
                          ),
                        ),
                      ],
                    ),
                    
                    pw.Spacer(),
                    
                    // معلومات التقرير
                    pw.Container(
                      width: double.infinity,
                      padding: pw.EdgeInsets.all(15),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue50,
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            'معلومات التقرير',
                            style: pw.TextStyle(
                              fontSize: 16,
                              fontWeight: pw.FontWeight.bold,
                              color: PdfColors.blue900,
                            ),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            'تاريخ الإنشاء: ${DateTimeHelpers.formatDateTime(DateTime.now())}',
                            style: pw.TextStyle(fontSize: 12),
                          ),
                          pw.Text(
                            'نوع التقرير: تقرير شامل ومفصل',
                            style: pw.TextStyle(fontSize: 12),
                          ),
                          pw.Text(
                            'مصدر البيانات: قاعدة البيانات المباشرة',
                            style: pw.TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// بناء كارت معلومات ملون
  pw.Widget _buildInfoCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }
  
  // سيتم إضافة باقي الوظائف في التحديث التالي...
  
  /// تحميل الخطوط العربية
  Future<Map<String, pw.Font>> _loadArabicFonts() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
      return {
        'regular': pw.Font.ttf(fontData),
        'bold': pw.Font.ttf(boldFontData),
      };
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ تعذر تحميل الخطوط العربية، استخدام الخطوط الافتراضية: $e');
      }
      return {
        'regular': pw.Font.helvetica(),
        'bold': pw.Font.helveticaBold(),
      };
    }
  }
  
  /// حساب عدد مساهمات المستخدم في التعليقات
  int _countUserContributions(int userId, dynamic comments) {
    if (comments == null || comments is! List) return 0;
    return comments.where((c) => c['userId'] == userId).length;
  }
  
  /// حساب عدد مرفقات المستخدم
  int _countUserAttachments(int userId, dynamic attachments) {
    if (attachments == null || attachments is! List) return 0;
    return attachments.where((a) => a['uploadedBy'] == userId).length;
  }
  
  /// حساب عدد أنشطة المستخدم
  int _countUserActivities(int userId, dynamic activities) {
    if (activities == null || activities is! List) return 0;
    return activities.where((a) => a['userId'] == userId).length;
  }
  
  // تم حذف الدالة غير المستخدمة
  
  // تم حذف الدالة غير المستخدمة _calculateRealStatistics
  
  double _calculateAverageProgress(List<TaskProgressTracker> trackers) {
    if (trackers.isEmpty) return 0.0;
    return trackers.map((t) => t.progress).reduce((a, b) => a + b) / trackers.length;
  }
  
  String _findMostActiveContributor(List<TaskContributor> contributors) {
    if (contributors.isEmpty) return 'لا يوجد';
    contributors.sort((a, b) => b.totalContributions.compareTo(a.totalContributions));
    return contributors.first.userName;
  }
  
  /// فهرس المحتويات المفصل
  pw.Page _buildDetailedTableOfContents(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          // رأس الصفحة
          _buildSectionHeader('فهرس المحتويات', PdfColors.blue900),
          
          pw.SizedBox(height: 20),
          
          // قائمة المحتويات
          pw.Expanded(
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildTableOfContentsItem('1. صفحة الغلاف', '1'),
                _buildTableOfContentsItem('2. فهرس المحتويات', '2'),
                _buildTableOfContentsItem('3. الملخص التنفيذي', '3'),
                if (data.contributors.isNotEmpty)
                  _buildTableOfContentsItem('4. تحليل المساهمين', '4'),
                if (data.progressTrackers.isNotEmpty)
                  _buildTableOfContentsItem('5. تحليل التقدم والأداء', '5'),
                if (data.timeTrackingEntries.isNotEmpty)
                  _buildTableOfContentsItem('6. تحليل الوقت والإنتاجية', '6'),
                if (data.transfers.isNotEmpty)
                  _buildTableOfContentsItem('7. سجل الأحداث التفصيلي', '7'),
                if (data.task.comments.isNotEmpty)
                  _buildTableOfContentsItem('8. تحليل التعليقات والتفاعل', '8'),
                if (data.task.attachments.isNotEmpty)
                  _buildTableOfContentsItem('9. المرفقات والوثائق', '9'),
                if (data.task.subtasks.isNotEmpty)
                  _buildTableOfContentsItem('10. المهام الفرعية', '10'),
                _buildTableOfContentsItem('11. الإحصائيات والتحليلات المتقدمة', '11'),
                _buildTableOfContentsItem('12. التوصيات والملحقات', '12'),
              ],
            ),
          ),
          
          // معلومات إضافية
          pw.Container(
            padding: pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  'ملاحظات التقرير:',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 5),
                pw.Text('• جميع البيانات مستمدة من قاعدة البيانات مباشرة'),
                pw.Text('• التقرير يحتوي على تحليلات شاملة ومفصلة'),
                pw.Text('• الإحصائيات محسوبة من البيانات الحقيقية فقط'),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// عنصر في فهرس المحتويات
  pw.Widget _buildTableOfContentsItem(String title, String pageNumber) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        children: [
          pw.Text(pageNumber, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(width: 10),
          pw.Expanded(
            child: pw.Container(
              height: 1,
              decoration: pw.BoxDecoration(
                border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey400)),
              ),
            ),
          ),
          pw.SizedBox(width: 10),
          pw.Text(title),
        ],
      ),
    );
  }
  
  /// الملخص التنفيذي للمهمة
  pw.Page _buildExecutiveSummary(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('الملخص التنفيذي', PdfColors.blue900),
          
          pw.SizedBox(height: 20),
          
          // نظرة عامة على المهمة
          _buildSubSection(
            'نظرة عامة على المهمة',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildSummaryRow('عنوان المهمة:', data.task.title),
                _buildSummaryRow('الوصف:', data.task.description ?? 'لا يوجد وصف'),
                _buildSummaryRow('الحالة الحالية:', data.task.status),
                _buildSummaryRow('الأولوية:', data.task.priority),
                _buildSummaryRow('نسبة الإنجاز:', '${data.task.completionPercentage}%'),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // الإنجازات الرئيسية
          _buildSubSection(
            'الإنجازات الرئيسية',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text('• تم إنجاز ${data.task.completionPercentage}% من المهمة'),
                pw.Text('• ${data.contributors.length} مساهم يعمل على المهمة'),
                pw.Text('• ${data.task.comments.length} تعليق وملاحظة'),
                pw.Text('• ${data.task.attachments.length} مرفق ووثيقة'),
                if (data.timeTrackingEntries.isNotEmpty)
                  pw.Text('• ${data.statistics.totalTimeSpent} دقيقة من العمل المسجل'),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // مؤشرات الأداء الرئيسية
          _buildSubSection(
            'مؤشرات الأداء الرئيسية (KPIs)',
            pw.Container(
              child: pw.Wrap(
                spacing: 10,
                runSpacing: 10,
                children: [
                  _buildKPICard('إجمالي المساهمين', '${data.contributors.length}', PdfColors.blue),
                  _buildKPICard('التعليقات', '${data.task.comments.length}', PdfColors.green),
                  _buildKPICard('المرفقات', '${data.task.attachments.length}', PdfColors.orange),
                  _buildKPICard('نسبة الإنجاز', '${data.task.completionPercentage}%', PdfColors.purple),
                ],
              ),
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // التحديات والملاحظات
          _buildSubSection(
            'التحديات والملاحظات',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                if (data.task.completionPercentage < 50)
                  pw.Text('⚠️ المهمة تحتاج لمزيد من التقدم (أقل من 50%)', 
                    style: pw.TextStyle(color: PdfColors.orange)),
                if (data.transfers.length > 3)
                  pw.Text('⚠️ تم تحويل المهمة عدة مرات (${data.transfers.length} مرات)',
                    style: pw.TextStyle(color: PdfColors.orange)),
                if (data.task.note != null && data.task.note!.isNotEmpty)
                  pw.Text('📝 ملاحظة المهمة: ${data.task.note}'),
                if (data.task.incoming != null && data.task.incoming!.isNotEmpty)
                  pw.Text('📨 مصدر المهمة: ${data.task.incoming}'),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// تحليل المساهمين المتقدم
  pw.Page _buildAdvancedContributorsAnalysis(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('تحليل المساهمين المفصل', PdfColors.green600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات المساهمين
          _buildSubSection(
            'إحصائيات عامة',
            pw.Row(
              children: [
                pw.Expanded(
                  child: _buildStatCard(
                    'إجمالي المساهمين',
                    '${data.contributors.length}',
                    PdfColors.blue,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'المساهم الأكثر نشاطاً',
                    data.statistics.mostActiveContributor,
                    PdfColors.green,
                  ),
                ),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // جدول المساهمين التفصيلي
          _buildSubSection(
            'قائمة المساهمين التفصيلية',
            pw.Container(
              child: pw.TableHelper.fromTextArray(
                context: context,
                data: [
                  ['المساهم', 'البريد الإلكتروني', 'التعليقات', 'المرفقات', 'إجمالي المساهمات', 'مستوى النشاط'],
                  ...data.contributors.map((contributor) => [
                    contributor.userName,
                    contributor.userEmail,
                    '${contributor.commentsCount}',
                    '${contributor.attachmentsCount}',
                    '${contributor.totalContributions}',
                    _getContributorLevelText(contributor.level),
                  ]).toList(),
                ],
                headerStyle: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                  fontSize: 10,
                ),
                headerDecoration: pw.BoxDecoration(color: PdfColors.green600),
                cellStyle: pw.TextStyle(fontSize: 9),
                cellAlignments: {
                  0: pw.Alignment.centerRight,
                  1: pw.Alignment.center,
                  2: pw.Alignment.center,
                  3: pw.Alignment.center,
                  4: pw.Alignment.center,
                  5: pw.Alignment.center,
                },
                columnWidths: {
                  0: pw.FlexColumnWidth(2.5),
                  1: pw.FlexColumnWidth(2.5),
                  2: pw.FlexColumnWidth(1),
                  3: pw.FlexColumnWidth(1),
                  4: pw.FlexColumnWidth(1.5),
                  5: pw.FlexColumnWidth(1.5),
                },
              ),
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // تحليل مستويات النشاط
          _buildSubSection(
            'توزيع مستويات النشاط',
            _buildActivityLevelsChart(data.contributors),
          ),
        ],
      ),
    );
  }
  
  /// تحليل التقدم والأداء
  pw.Page _buildProgressAnalysis(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('تحليل التقدم والأداء', PdfColors.purple600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات التقدم
          _buildSubSection(
            'مؤشرات التقدم',
            pw.Row(
              children: [
                pw.Expanded(
                  child: _buildStatCard(
                    'النسبة الحالية',
                    '${data.task.completionPercentage}%',
                    PdfColors.blue,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'تحديثات التقدم',
                    '${data.progressTrackers.length}',
                    PdfColors.green,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'متوسط التقدم',
                    '${data.statistics.averageProgressPerDay.toStringAsFixed(1)}%',
                    PdfColors.orange,
                  ),
                ),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // جدول متتبعات التقدم
          if (data.progressTrackers.isNotEmpty)
            _buildSubSection(
              'سجل متتبعات التقدم',
              pw.Container(
                child: pw.TableHelper.fromTextArray(
                  context: context,
                  data: [
                    ['التاريخ', 'نسبة التقدم', 'المحدث بواسطة', 'الملاحظات'],
                    ...data.progressTrackers.take(10).map((tracker) => [
                      DateTimeHelpers.formatDateTime(tracker.updatedAt),
                      '${tracker.progress.toStringAsFixed(1)}%',
                      'مستخدم ${tracker.updatedBy}',
                      tracker.notes.isNotEmpty ? tracker.notes : 'لا توجد ملاحظات',
                    ]).toList(),
                  ],
                  headerStyle: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                    fontSize: 10,
                  ),
                  headerDecoration: pw.BoxDecoration(color: PdfColors.purple600),
                  cellStyle: pw.TextStyle(fontSize: 9),
                  cellAlignments: {
                    0: pw.Alignment.center,
                    1: pw.Alignment.center,
                    2: pw.Alignment.centerRight,
                    3: pw.Alignment.centerRight,
                  },
                ),
              ),
            ),
          
          pw.SizedBox(height: 15),
          
          // تحليل اتجاه التقدم
          _buildSubSection(
            'تحليل اتجاه التقدم',
            _buildProgressTrendAnalysis(data.progressTrackers),
          ),
        ],
      ),
    );
  }
  
  /// تحليل الوقت والإنتاجية
  pw.Page _buildTimeAnalysis(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('تحليل الوقت والإنتاجية', PdfColors.orange600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات الوقت
          _buildSubSection(
            'إحصائيات الوقت العامة',
            pw.Row(
              children: [
                pw.Expanded(
                  child: _buildStatCard(
                    'إجمالي الوقت المسجل',
                    '${(data.statistics.totalTimeSpent / 60).toStringAsFixed(1)} ساعة',
                    PdfColors.blue,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'عدد الجلسات',
                    '${data.timeTrackingEntries.length}',
                    PdfColors.green,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'متوسط الجلسة',
                    data.timeTrackingEntries.isNotEmpty 
                      ? '${(data.statistics.totalTimeSpent / data.timeTrackingEntries.length).toStringAsFixed(0)} دقيقة'
                      : '0 دقيقة',
                    PdfColors.purple,
                  ),
                ),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // مقارنة الوقت المقدر مع الفعلي
          if (data.task.estimatedTime != null)
            _buildSubSection(
              'مقارنة الوقت المقدر مع الفعلي',
              _buildTimeComparisonChart(
                data.task.estimatedTime!,
                data.statistics.totalTimeSpent,
              ),
            ),
          
          pw.SizedBox(height: 15),
          
          // جدول سجلات الوقت
          if (data.timeTrackingEntries.isNotEmpty)
            _buildSubSection(
              'سجل الوقت التفصيلي',
              pw.Container(
                child: pw.TableHelper.fromTextArray(
                  context: context,
                  data: [
                    ['التاريخ', 'المستخدم', 'وقت البداية', 'وقت النهاية', 'المدة', 'الوصف'],
                    ...data.timeTrackingEntries.take(10).map((entry) => [
                      DateTimeHelpers.formatDate(entry.startTime),
                      entry.userName,
                      DateTimeHelpers.formatTime(entry.startTime),
                      DateTimeHelpers.formatTime(entry.endTime),
                      '${entry.durationMinutes} دقيقة',
                      entry.description.isNotEmpty ? entry.description : 'لا يوجد وصف',
                    ]).toList(),
                  ],
                  headerStyle: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                    fontSize: 9,
                  ),
                  headerDecoration: pw.BoxDecoration(color: PdfColors.orange600),
                  cellStyle: pw.TextStyle(fontSize: 8),
                  cellAlignments: {
                    0: pw.Alignment.center,
                    1: pw.Alignment.centerRight,
                    2: pw.Alignment.center,
                    3: pw.Alignment.center,
                    4: pw.Alignment.center,
                    5: pw.Alignment.centerRight,
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  /// سجل الأحداث التفصيلي
  pw.Page _buildDetailedHistoryLog(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('سجل الأحداث التفصيلي', PdfColors.teal600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات الأحداث
          _buildSubSection(
            'إحصائيات الأحداث',
            pw.Row(
              children: [
                pw.Expanded(
                  child: _buildStatCard(
                    'إجمالي الأحداث',
                    '${data.transfers.length}',
                    PdfColors.blue,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'التحويلات',
                    '${data.transfers.length}',
                    PdfColors.orange,
                  ),
                ),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // جدول الأحداث التفصيلي
          if (data.transfers.isNotEmpty)
            _buildSubSection(
              'جدول الأحداث الشامل',
              pw.Container(
                child: pw.TableHelper.fromTextArray(
                  context: context,
                  data: [
                    ['التاريخ', 'المستخدم', 'الإجراء', 'التفاصيل', 'القيمة القديمة', 'القيمة الجديدة'],
                    ...data.transfers.take(15).map((transfer) => [
                      DateTimeHelpers.formatDateTime(
                        DateTime.fromMillisecondsSinceEpoch(transfer.timestamp * 1000)
                      ),
                      transfer.executor,
                      transfer.reason,
                      'تحويل المهمة',
                      transfer.fromUser,
                      transfer.toUser,
                    ]).toList(),
                  ],
                  headerStyle: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                    fontSize: 8,
                  ),
                  headerDecoration: pw.BoxDecoration(color: PdfColors.teal600),
                  cellStyle: pw.TextStyle(fontSize: 7),
                  cellAlignments: {
                    0: pw.Alignment.center,
                    1: pw.Alignment.centerRight,
                    2: pw.Alignment.centerRight,
                    3: pw.Alignment.centerRight,
                    4: pw.Alignment.center,
                    5: pw.Alignment.center,
                  },
                ),
              ),
            ),
          
          pw.SizedBox(height: 15),
          
          // تحليل التحويلات
          if (data.transfers.isNotEmpty)
            _buildSubSection(
              'تحليل التحويلات',
              _buildTransfersAnalysis(data.transfers),
            ),
        ],
      ),
    );
  }
  
  /// تحليل التعليقات والتفاعل
  pw.Page _buildCommentsAnalysis(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('تحليل التعليقات والتفاعل', PdfColors.indigo600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات التعليقات
          _buildSubSection(
            'إحصائيات التعليقات',
            pw.Row(
              children: [
                pw.Expanded(
                  child: _buildStatCard(
                    'إجمالي التعليقات',
                    '${data.task.comments.length}',
                    PdfColors.blue,
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: _buildStatCard(
                    'المعلقون الفريدون',
                    '${data.task.comments.map((c) => c.userId).toSet().length}',
                    PdfColors.green,
                  ),
                ),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // آخر التعليقات
          if (data.task.comments.isNotEmpty)
            _buildSubSection(
              'آخر التعليقات',
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: data.task.comments.take(8).map((comment) => 
                  _buildCommentCard(comment)
                ).toList(),
              ),
            ),
        ],
      ),
    );
  }
  
  /// قسم المرفقات والوثائق
  pw.Page _buildAttachmentsSection(ProcessedTaskData data, pw.ThemeData theme, bool includePreviews) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('المرفقات والوثائق', PdfColors.red600),
          
          pw.SizedBox(height: 20),
          
          // إحصائيات المرفقات
          _buildSubSection(
            'إحصائيات المرفقات',
            _buildAttachmentsStats(data.task.attachments),
          ),
          
          pw.SizedBox(height: 15),
          
          // قائمة المرفقات
          if (data.task.attachments.isNotEmpty)
            _buildSubSection(
              'قائمة المرفقات الشاملة',
              pw.Container(
                child: pw.TableHelper.fromTextArray(
                  context: context,
                  data: [
                    ['اسم الملف', 'النوع', 'الحجم', 'الرافع', 'تاريخ الرفع'],
                    ...data.task.attachments.map((attachment) => [
                      attachment.fileName ?? 'ملف غير معروف',
                      _getFileExtension(attachment.fileName ?? ''),
                      _formatFileSize(attachment.fileSize ?? 0),
                      attachment.uploadedBy?.toString() ?? 'غير معروف',
                      DateTimeHelpers.formatDateTime(
                        DateTime.fromMillisecondsSinceEpoch((attachment.uploadedAt ?? 0) * 1000)
                      ),
                    ]).toList(),
                  ],
                  headerStyle: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.white,
                    fontSize: 10,
                  ),
                  headerDecoration: pw.BoxDecoration(color: PdfColors.red600),
                  cellStyle: pw.TextStyle(fontSize: 9),
                  cellAlignments: {
                    0: pw.Alignment.centerRight,
                    1: pw.Alignment.center,
                    2: pw.Alignment.center,
                    3: pw.Alignment.centerRight,
                    4: pw.Alignment.center,
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  /// الإحصائيات والتحليلات المتقدمة
  pw.Page _buildAdvancedStatistics(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('الإحصائيات والتحليلات المتقدمة', PdfColors.deepPurple600),
          
          pw.SizedBox(height: 20),
          
          // ملخص الإحصائيات الرئيسية
          _buildSubSection(
            'مؤشرات الأداء الرئيسية (KPIs)',
            pw.Container(
              child: pw.Wrap(
                spacing: 10,
                runSpacing: 10,
                children: [
                  _buildKPICard('عمر المهمة', '${data.statistics.taskAge} يوم', PdfColors.blue),
                  _buildKPICard('المساهمون', '${data.statistics.totalContributors}', PdfColors.green),
                  _buildKPICard('التعليقات', '${data.statistics.totalComments}', PdfColors.orange),
                  _buildKPICard('المرفقات', '${data.statistics.totalAttachments}', PdfColors.purple),
                  _buildKPICard('التحويلات', '${data.statistics.totalTransfers}', PdfColors.red),
                  _buildKPICard('الوقت المسجل', '${(data.statistics.totalTimeSpent / 60).toStringAsFixed(1)} ساعة', PdfColors.teal),
                ],
              ),
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // تحليل الاتجاهات
          _buildSubSection(
            'تحليل الاتجاهات والأنماط',
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildTrendAnalysis(data),
                
                pw.SizedBox(height: 10),
                
                // توقعات الإنجاز
                _buildCompletionPrediction(data),
              ],
            ),
          ),
          
          pw.SizedBox(height: 15),
          
          // مقارنات زمنية
          _buildSubSection(
            'المقارنات الزمنية',
            _buildTimeComparisons(data),
          ),
        ],
      ),
    );
  }
  
  /// التوصيات والملحقات
  pw.Page _buildRecommendationsAndAppendices(ProcessedTaskData data, pw.ThemeData theme) {
    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      textDirection: pw.TextDirection.rtl,
      theme: theme,
      build: (context) => pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildSectionHeader('التوصيات والملحقات', PdfColors.brown600),
          
          pw.SizedBox(height: 20),
          
          // التوصيات المبنية على البيانات
          _buildSubSection(
            'التوصيات المبنية على تحليل البيانات',
            _buildDataDrivenRecommendations(data),
          ),
          
          pw.SizedBox(height: 15),
          
          // نقاط التحسين
          _buildSubSection(
            'نقاط التحسين المقترحة',
            _buildImprovementPoints(data),
          ),
          
          pw.SizedBox(height: 15),
          
          // معلومات التقرير التقنية
          _buildSubSection(
            'معلومات التقرير التقنية',
            _buildTechnicalInfo(data),
          ),
          
          pw.Spacer(),
          
          // خاتمة التقرير
          pw.Container(
            width: double.infinity,
            padding: pw.EdgeInsets.all(20),
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [PdfColors.brown600, PdfColors.brown400],
              ),
              borderRadius: pw.BorderRadius.circular(10),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Text(
                  'تم إنشاء هذا التقرير بواسطة',
                  style: pw.TextStyle(
                    color: PdfColors.white,
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'نظام إدارة المهام المتقدم',
                  style: pw.TextStyle(
                    color: PdfColors.white,
                    fontSize: 12,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  DateTimeHelpers.formatDateTime(DateTime.now()),
                  style: pw.TextStyle(
                    color: PdfColors.grey300,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// فئة لتنظيم البيانات المعالجة
class ProcessedTaskData {
  final Task task;
  final List<TaskContributor> contributors;
  final List<TaskTransfer> transfers;
  final List<TaskProgressTracker> progressTrackers;
  final List<TimeTrackingEntry> timeTrackingEntries;
  final TaskReportStatistics statistics;
  final Map<String, dynamic> rawData;
  
  ProcessedTaskData({
    required this.task,
    required this.contributors,
    required this.transfers,
    required this.progressTrackers,
    required this.timeTrackingEntries,
    required this.statistics,
    required this.rawData,
  });
}

// ======= الدوال المساعدة للتقرير =======

extension ComprehensiveReportHelpers on ComprehensiveTaskReportService {
  
  /// بناء رأس القسم
  pw.Widget _buildSectionHeader(String title, PdfColor color) {
    return pw.Container(
      width: double.infinity,
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: color,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Text(
        title,
        style: pw.TextStyle(
          fontSize: 18,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }
  
  /// بناء قسم فرعي
  pw.Widget _buildSubSection(String title, pw.Widget content) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        pw.Container(
          width: double.infinity,
          padding: pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey200,
            borderRadius: pw.BorderRadius.circular(5),
          ),
          child: pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
        ),
        pw.SizedBox(height: 8),
        content,
      ],
    );
  }
  
  /// صف في الملخص
  pw.Widget _buildSummaryRow(String label, String value) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 5),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: pw.TextStyle(fontSize: 12),
            ),
          ),
          pw.SizedBox(width: 10),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.grey700,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// كارت KPI
  pw.Widget _buildKPICard(String title, String value, PdfColor color) {
    return pw.Container(
      width: 120,
      padding: pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        border: pw.Border.all(color: color),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: color,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// كارت إحصائية
  pw.Widget _buildStatCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: pw.FontWeight.bold,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }
  
  /// نص مستوى المساهم
  String _getContributorLevelText(ContributorLevel level) {
    switch (level) {
      case ContributorLevel.veryActive:
        return 'نشط جداً';
      case ContributorLevel.active:
        return 'نشط';
      case ContributorLevel.medium:
        return 'متوسط';
      case ContributorLevel.basic:
        return 'أساسي';
    }
  }
  
  /// رسم بياني لمستويات النشاط
  pw.Widget _buildActivityLevelsChart(List<TaskContributor> contributors) {
    final levels = <ContributorLevel, int>{};
    for (var contributor in contributors) {
      levels[contributor.level] = (levels[contributor.level] ?? 0) + 1;
    }
    
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: levels.entries.map((entry) => 
          pw.Container(
            margin: pw.EdgeInsets.only(bottom: 8),
            child: pw.Row(
              children: [
                pw.Text('${entry.value}', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(width: 10),
                pw.Expanded(
                  child: pw.Container(
                    height: 20,
                    decoration: pw.BoxDecoration(
                      color: _getColorForLevel(entry.key),
                      borderRadius: pw.BorderRadius.circular(10),
                    ),
                  ),
                ),
                pw.SizedBox(width: 10),
                pw.Text(_getContributorLevelText(entry.key)),
              ],
            ),
          )
        ).toList(),
      ),
    );
  }
  
  /// لون مستوى المساهم
  PdfColor _getColorForLevel(ContributorLevel level) {
    switch (level) {
      case ContributorLevel.veryActive:
        return PdfColors.green;
      case ContributorLevel.active:
        return PdfColors.blue;
      case ContributorLevel.medium:
        return PdfColors.orange;
      case ContributorLevel.basic:
        return PdfColors.grey;
    }
  }
  
  /// تحليل اتجاه التقدم
  pw.Widget _buildProgressTrendAnalysis(List<TaskProgressTracker> trackers) {
    if (trackers.isEmpty) {
      return pw.Text('لا توجد بيانات تقدم متاحة');
    }
    
    final sortedTrackers = List<TaskProgressTracker>.from(trackers)
      ..sort((a, b) => a.updatedAt.compareTo(b.updatedAt));
    
    String trend = 'ثابت';
    if (sortedTrackers.length >= 2) {
      final lastProgress = sortedTrackers.last.progress;
      final firstProgress = sortedTrackers.first.progress;
      
      if (lastProgress > firstProgress) {
        trend = 'متصاعد 📈';
      } else if (lastProgress < firstProgress) {
        trend = 'متراجع 📉';
      }
    }
    
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'اتجاه التقدم العام: $trend',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          pw.Text('آخر تحديث: ${DateTimeHelpers.formatDateTime(sortedTrackers.last.updatedAt)}'),
          pw.Text('التقدم الحالي: ${sortedTrackers.last.progress.toStringAsFixed(1)}%'),
        ],
      ),
    );
  }
  
  /// مقارنة الوقت
  pw.Widget _buildTimeComparisonChart(int estimatedMinutes, int actualMinutes) {
    final estimatedHours = estimatedMinutes / 60;
    final actualHours = actualMinutes / 60;
    final efficiency = estimatedMinutes > 0 ? (estimatedMinutes / actualMinutes * 100) : 0;
    
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.orange50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          _buildTimeComparisonRow('الوقت المقدر:', '${estimatedHours.toStringAsFixed(1)} ساعة'),
          _buildTimeComparisonRow('الوقت الفعلي:', '${actualHours.toStringAsFixed(1)} ساعة'),
          _buildTimeComparisonRow('كفاءة الوقت:', '${efficiency.toStringAsFixed(1)}%'),
          pw.SizedBox(height: 10),
          pw.Text(
            efficiency >= 100 ? 'الأداء ضمن التوقعات ✅' : 
            efficiency >= 80 ? 'الأداء مقبول ⚠️' : 'يحتاج تحسين ❌',
            style: pw.TextStyle(
              fontWeight: pw.FontWeight.bold,
              color: efficiency >= 100 ? PdfColors.green : 
                     efficiency >= 80 ? PdfColors.orange : PdfColors.red,
            ),
          ),
        ],
      ),
    );
  }
  
  pw.Widget _buildTimeComparisonRow(String label, String value) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        children: [
          pw.Expanded(child: pw.Text(value)),
          pw.SizedBox(width: 10),
          pw.Text(label, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
        ],
      ),
    );
  }
  
  /// تحليل التحويلات
  pw.Widget _buildTransfersAnalysis(List<TaskTransfer> transfers) {
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.red50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'تحليل نشاط التحويلات',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          pw.Text('إجمالي التحويلات: ${transfers.length}'),
          if (transfers.length > 3)
            pw.Text(
              '⚠️ عدد كبير من التحويلات قد يؤثر على الإنتاجية',
              style: pw.TextStyle(color: PdfColors.orange),
            ),
          pw.SizedBox(height: 8),
          ...transfers.take(3).map((transfer) => 
            pw.Text(
              '• ${transfer.fromUser} ← ${transfer.toUser}',
              style: pw.TextStyle(fontSize: 10),
            )
          ).toList(),
        ],
      ),
    );
  }
  
  /// كارت تعليق
  pw.Widget _buildCommentCard(dynamic comment) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 8),
      padding: pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Row(
            children: [
              pw.Text(
                DateTimeHelpers.formatDateTime(
                  DateTime.fromMillisecondsSinceEpoch((comment.createdAt ?? 0) * 1000)
                ),
                style: pw.TextStyle(fontSize: 9, color: PdfColors.grey600),
              ),
              pw.Spacer(),
              pw.Text(
                comment.userName ?? 'مستخدم غير معروف',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
              ),
            ],
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            comment.comment ?? 'تعليق فارغ',
            style: pw.TextStyle(fontSize: 11),
          ),
        ],
      ),
    );
  }
  
  /// إحصائيات المرفقات
  pw.Widget _buildAttachmentsStats(List<dynamic> attachments) {
    final totalSize = attachments.fold<int>(0, (sum, att) {
      try {
        final fileSize = att.fileSize;
        if (fileSize is int) {
          return sum + fileSize;
        } else if (fileSize is double) {
          return sum + fileSize.toInt();
        } else if (fileSize is String) {
          return sum + (int.tryParse(fileSize) ?? 0);
        } else {
          return sum;
        }
      } catch (e) {
        return sum;
      }
    });
    final types = <String, int>{};
    
    for (var attachment in attachments) {
      final extension = _getFileExtension(attachment.fileName ?? '');
      types[extension] = (types[extension] ?? 0) + 1;
    }
    
    return pw.Row(
      children: [
        pw.Expanded(
          child: _buildStatCard(
            'إجمالي المرفقات',
            '${attachments.length}',
            PdfColors.blue,
          ),
        ),
        pw.SizedBox(width: 10),
        pw.Expanded(
          child: _buildStatCard(
            'الحجم الإجمالي',
            _formatFileSize(totalSize),
            PdfColors.green,
          ),
        ),
        pw.SizedBox(width: 10),
        pw.Expanded(
          child: _buildStatCard(
            'أنواع الملفات',
            '${types.length}',
            PdfColors.orange,
          ),
        ),
      ],
    );
  }
  
  /// امتداد الملف
  String _getFileExtension(String fileName) {
    if (fileName.isEmpty) return 'غير معروف';
    final parts = fileName.split('.');
    return parts.length > 1 ? parts.last.toUpperCase() : 'بدون امتداد';
  }
  
  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
  
  /// تحليل الاتجاهات
  pw.Widget _buildTrendAnalysis(ProcessedTaskData data) {
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.purple50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'تحليل الأنماط والاتجاهات',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          _buildTrendItem('نشاط المساهمين', _analyzeCommunicationTrend(data)),
          _buildTrendItem('التقدم في العمل', _analyzeProgressTrend(data)),
          _buildTrendItem('إدارة الوقت', _analyzeTimeManagementTrend(data)),
        ],
      ),
    );
  }
  
  pw.Widget _buildTrendItem(String category, String analysis) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 4),
      child: pw.Row(
        children: [
          pw.Expanded(child: pw.Text(analysis, style: pw.TextStyle(fontSize: 10))),
          pw.SizedBox(width: 10),
          pw.Text('$category:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
        ],
      ),
    );
  }
  
  String _analyzeCommunicationTrend(ProcessedTaskData data) {
    if (data.task.comments.length > 10) return 'نشاط تواصل عالي';
    if (data.task.comments.length > 5) return 'نشاط تواصل متوسط';
    return 'نشاط تواصل منخفض';
  }
  
  String _analyzeProgressTrend(ProcessedTaskData data) {
    if (data.task.completionPercentage >= 80) return 'تقدم ممتاز';
    if (data.task.completionPercentage >= 50) return 'تقدم جيد';
    return 'يحتاج تسريع';
  }
  
  String _analyzeTimeManagementTrend(ProcessedTaskData data) {
    if (data.task.estimatedTime != null && data.statistics.totalTimeSpent > 0) {
      final efficiency = data.task.estimatedTime! / data.statistics.totalTimeSpent;
      if (efficiency >= 0.8) return 'إدارة وقت ممتازة';
      if (efficiency >= 0.6) return 'إدارة وقت جيدة';
      return 'تحتاج تحسين';
    }
    return 'بيانات غير كافية';
  }
  
  /// توقعات الإنجاز
  pw.Widget _buildCompletionPrediction(ProcessedTaskData data) {
    final currentProgress = data.task.completionPercentage;
    final remainingProgress = 100 - currentProgress;
    final daysElapsed = data.statistics.taskAge;
    
    String prediction = 'غير محدد';
    if (currentProgress > 0 && daysElapsed > 0) {
      final dailyProgress = currentProgress / daysElapsed;
      final remainingDays = (remainingProgress / dailyProgress).round();
      prediction = '$remainingDays يوم تقريباً';
    }
    
    return pw.Container(
      padding: pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(6),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'توقعات الإنجاز',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 5),
          pw.Text('الوقت المتوقع للإنجاز: $prediction'),
        ],
      ),
    );
  }
  
  /// المقارنات الزمنية
  pw.Widget _buildTimeComparisons(ProcessedTaskData data) {
    return pw.Container(
      padding: pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.teal50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'المقارنات الزمنية',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          _buildComparisonRow('عمر المهمة:', '${data.statistics.taskAge} يوم'),
          if (data.task.estimatedTime != null)
            _buildComparisonRow('الوقت المقدر:', '${data.task.estimatedTime} دقيقة'),
          _buildComparisonRow('الوقت المسجل:', '${data.statistics.totalTimeSpent} دقيقة'),
          _buildComparisonRow('متوسط العمل اليومي:', '${(data.statistics.totalTimeSpent / data.statistics.taskAge).toStringAsFixed(1)} دقيقة/يوم'),
        ],
      ),
    );
  }
  
  pw.Widget _buildComparisonRow(String label, String value) {
    return pw.Container(
      margin: pw.EdgeInsets.only(bottom: 3),
      child: pw.Row(
        children: [
          pw.Expanded(child: pw.Text(value, style: pw.TextStyle(fontSize: 10))),
          pw.SizedBox(width: 10),
          pw.Text(label, style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10)),
        ],
      ),
    );
  }
  
  /// التوصيات المبنية على البيانات
  pw.Widget _buildDataDrivenRecommendations(ProcessedTaskData data) {
    final recommendations = <String>[];
    
    // تحليل التقدم
    if (data.task.completionPercentage < 30) {
      recommendations.add('• تسريع وتيرة العمل - المهمة تحتاج دفعة قوية');
    }
    
    // تحليل التواصل
    if (data.task.comments.length < 3) {
      recommendations.add('• زيادة التواصل والتنسيق بين أعضاء الفريق');
    }
    
    // تحليل التحويلات
    if (data.transfers.length > 3) {
      recommendations.add('• مراجعة توزيع المهام لتقليل التحويلات المتكررة');
    }
    
    // تحليل الوقت
    if (data.task.estimatedTime != null && data.statistics.totalTimeSpent > data.task.estimatedTime!) {
      recommendations.add('• مراجعة تقديرات الوقت وتحسين الكفاءة');
    }
    
    // تحليل المساهمين
    if (data.contributors.length < 2) {
      recommendations.add('• إشراك المزيد من المساهمين لتوزيع العبء');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('• الأداء ضمن المعايير المقبولة - استمر على نفس النهج');
    }
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: recommendations.map((rec) => 
        pw.Container(
          margin: pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(rec, style: pw.TextStyle(fontSize: 11)),
        )
      ).toList(),
    );
  }
  
  /// نقاط التحسين
  pw.Widget _buildImprovementPoints(ProcessedTaskData data) {
    final improvements = <String>[
      '• تحديث متتبعات التقدم بانتظام لمراقبة أفضل',
      '• توثيق جميع الأعمال المنجزة في سجلات الوقت',
      '• استخدام التعليقات لتوضيح التحديات والحلول',
      '• رفع الوثائق المهمة كمرفقات للرجوع إليها لاحقاً',
    ];
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: improvements.map((improvement) => 
        pw.Container(
          margin: pw.EdgeInsets.only(bottom: 5),
          child: pw.Text(improvement, style: pw.TextStyle(fontSize: 11)),
        )
      ).toList(),
    );
  }
  
  /// المعلومات التقنية
  pw.Widget _buildTechnicalInfo(ProcessedTaskData data) {
    return pw.Container(
      padding: pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(6),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'معلومات التقرير التقنية',
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
          ),
          pw.SizedBox(height: 5),
          pw.Text('تاريخ الإنشاء: ${DateTimeHelpers.formatDateTime(DateTime.now())}', style: pw.TextStyle(fontSize: 9)),
          pw.Text('مصدر البيانات: قاعدة البيانات المباشرة', style: pw.TextStyle(fontSize: 9)),
          pw.Text('نوع التقرير: شامل ومفصل', style: pw.TextStyle(fontSize: 9)),
          pw.Text('معرف المهمة: ${data.task.id}', style: pw.TextStyle(fontSize: 9)),
          pw.Text('إجمالي نقاط البيانات: ${_countDataPoints(data)}', style: pw.TextStyle(fontSize: 9)),
        ],
      ),
    );
  }
  
  int _countDataPoints(ProcessedTaskData data) {
    return 1 + // المهمة الأساسية
           data.contributors.length +
           data.transfers.length +
           data.progressTrackers.length +
           data.timeTrackingEntries.length +
           data.task.comments.length +
           data.task.attachments.length +
           data.task.subtasks.length;
  }
}