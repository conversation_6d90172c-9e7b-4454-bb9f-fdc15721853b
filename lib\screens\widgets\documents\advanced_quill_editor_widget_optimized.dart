// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_quill/flutter_quill.dart';
// import 'dart:convert';
// import 'dart:async';
// import 'dart:io';
// import 'dart:typed_data';
// import 'package:printing/printing.dart';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
// import 'package:file_picker/file_picker.dart';
// import 'package:image_picker/image_picker.dart';
// import '../../../services/api/unified_document_api_service.dart';
// import '../../../services/upload_service.dart';

// /// محرر Flutter Quill المحسن والمبسط
// /// تم تحسين الأداء وإزالة التعقيدات غير الضرورية
// /// 
// /// التحسينات المطبقة:
// /// - استخدام debounce للحفظ التلقائي
// /// - إزالة المعالجة المتكررة للمحتوى
// /// - الاعتماد على ميزات Quill المدمجة
// /// - تبسيط إدارة الذاكرة
// /// - استخدام MultiPage للـ PDF بدلاً من التقسيم اليدوي
// class AdvancedQuillEditorWidget extends StatefulWidget {
//   final String initialContent;
//   final bool isReadOnly;
//   final Function(String)? onContentChanged;
//   final VoidCallback? onSave;
//   final String? placeholder;
//   final bool showToolbar;
//   final double? minHeight;
//   final double? maxHeight;

//   // خصائص التكامل مع النظام
//   final int? documentId;
//   final bool enableAutoSave;
//   final Function(String)? onAutoSave;
//   final Function(String, String)? onExport;
//   final bool enableComments;
//   final Function(String, int, int)? onAddComment;
//   final bool enableVersionHistory;
//   final Function()? onShowVersionHistory;

//   // معلومات المستند
//   final String? documentTitle;
//   final String? documentType;
//   final String? documentDescription;
//   final String? authorName;

//   const AdvancedQuillEditorWidget({
//     super.key,
//     this.initialContent = '',
//     this.isReadOnly = false,
//     this.onContentChanged,
//     this.onSave,
//     this.placeholder = 'ابدأ الكتابة هنا...',
//     this.showToolbar = true,
//     this.minHeight = 300,
//     this.maxHeight,
//     this.documentId,
//     this.enableAutoSave = true,
//     this.onAutoSave,
//     this.onExport,
//     this.enableComments = false,
//     this.onAddComment,
//     this.enableVersionHistory = false,
//     this.onShowVersionHistory,
//     this.documentTitle,
//     this.documentType = 'مستند عام',
//     this.documentDescription,
//     this.authorName,
//   });

//   @override
//   State<AdvancedQuillEditorWidget> createState() => _AdvancedQuillEditorWidgetState();
// }

// class _AdvancedQuillEditorWidgetState extends State<AdvancedQuillEditorWidget> {
//   late QuillController _controller;
//   late FocusNode _focusNode;
//   late ScrollController _scrollController;

//   // متغيرات الحالة المبسطة
//   bool _isSaving = false;
//   Timer? _debounceTimer; // استخدام debounce بدلاً من Timer متعدد
  
//   // خدمة API للمستندات
//   final UnifiedDocumentApiService _documentService = UnifiedDocumentApiService();
  
//   // خط عربي محمل مسبقاً لتحسين الأداء
//   pw.Font? _cachedArabicFont;
//   pw.Font? _cachedArabicBoldFont;

//   @override
//   void initState() {
//     super.initState();
//     _initializeEditor();
//     _preloadArabicFonts(); // تحميل الخطوط مسبقاً
//   }

//   @override
//   void didUpdateWidget(AdvancedQuillEditorWidget oldWidget) {
//     super.didUpdateWidget(oldWidget);
    
//     // إعادة تهيئة المحرر فقط عند تغيير المحتوى الأولي بشكل جوهري
//     if (oldWidget.initialContent != widget.initialContent) {
//       _reinitializeEditor();
//     }
//   }

//   @override
//   void dispose() {
//     // تنظيف شامل للموارد
//     _debounceTimer?.cancel();
//     _controller.dispose();
//     _focusNode.dispose();
//     _scrollController.dispose();
//     super.dispose();
//   }

//   /// تحميل الخطوط العربية مسبقاً لتحسين الأداء
//   Future<void> _preloadArabicFonts() async {
//     try {
//       // محاولة تحميل خط Cairo أولاً
//       try {
//         final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
//         final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
//         _cachedArabicFont = pw.Font.ttf(fontData);
//         _cachedArabicBoldFont = pw.Font.ttf(boldFontData);
//         debugPrint('✅ تم تحميل خط Cairo مسبقاً');
//       } catch (e) {
//         // استخدام خط احتياطي
//         _cachedArabicFont = pw.Font.times();
//         _cachedArabicBoldFont = pw.Font.timesBold();
//         debugPrint('⚠️ استخدام خط احتياطي: $e');
//       }
//     } catch (e) {
//       debugPrint('خطأ في تحميل الخطوط: $e');
//     }
//   }

//   /// تهيئة المحرر بطريقة مبسطة
//   void _initializeEditor() {
//     // إنشاء المستند بطريقة مبسطة
//     Document document;
    
//     if (widget.initialContent.isNotEmpty) {
//       try {
//         // محاولة تحليل JSON أولاً
//         final jsonData = jsonDecode(widget.initialContent);
//         document = Document.fromJson(jsonData);
//       } catch (e) {
//         // إنشاء مستند جديد مع النص العادي
//         document = Document()..insert(0, widget.initialContent);
//       }
//     } else {
//       document = Document();
//     }

//     // إنشاء المتحكمات
//     _controller = QuillController(
//       document: document,
//       selection: const TextSelection.collapsed(offset: 0),
//     );
//     _focusNode = FocusNode();
//     _scrollController = ScrollController();

//     // إضافة مستمع واحد مع debounce
//     _controller.addListener(_onDocumentChangedWithDebounce);
//   }

//   /// إعادة تهيئة المحرر
//   void _reinitializeEditor() {
//     if (!mounted) return;

//     try {
//       _controller.removeListener(_onDocumentChangedWithDebounce);
      
//       Document document;
//       if (widget.initialContent.isNotEmpty) {
//         try {
//           final jsonData = jsonDecode(widget.initialContent);
//           document = Document.fromJson(jsonData);
//         } catch (e) {
//           document = Document()..insert(0, widget.initialContent);
//         }
//       } else {
//         document = Document();
//       }

//       _controller.document = document;
//       _controller.addListener(_onDocumentChangedWithDebounce);
      
//     } catch (e) {
//       debugPrint('خطأ في إعادة تهيئة المحرر: $e');
//     }
//   }

//   /// معالج تغيير المستند مع debounce لتحسين الأداء
//   void _onDocumentChangedWithDebounce() {
//     if (!mounted) return;

//     // إلغاء المؤقت السابق
//     _debounceTimer?.cancel();
    
//     // بدء مؤقت جديد مع تأخير 500ms
//     _debounceTimer = Timer(const Duration(milliseconds: 500), () {
//       if (mounted) {
//         _processDocumentChange();
//       }
//     });
//   }

//   /// معالجة تغيير المستند (يتم استدعاؤها بعد debounce)
//   void _processDocumentChange() {
//     final content = _getDocumentContent();
    
//     // استدعاء callback التغيير
//     if (widget.onContentChanged != null) {
//       widget.onContentChanged!(content);
//     }

//     // بدء الحفظ التلقائي إذا كان مفعلاً
//     if (widget.enableAutoSave) {
//       _autoSaveDocument();
//     }
//   }

//   /// الحفظ التلقائي المبسط
//   Future<void> _autoSaveDocument() async {
//     if (!mounted || _isSaving) return;

//     try {
//       setState(() => _isSaving = true);

//       final content = _getDocumentContent();

//       if (widget.documentId != null) {
//         // استخدام خدمة API
//         await _documentService.autoSaveDocument(
//           documentId: widget.documentId!,
//           deltaJson: content,
//           plainText: _getPlainText(),
//         );
//       } else if (widget.onAutoSave != null) {
//         // استخدام callback مخصص
//         widget.onAutoSave!(content);
//       }

//       debugPrint('✅ تم الحفظ التلقائي');
//     } catch (e) {
//       debugPrint('خطأ في الحفظ التلقائي: $e');
//     } finally {
//       if (mounted) {
//         setState(() => _isSaving = false);
//       }
//     }
//   }

//   /// الحصول على محتوى المستند (مبسط)
//   String _getDocumentContent() {
//     return jsonEncode(_controller.document.toDelta().toJson());
//   }

//   /// الحصول على النص العادي (مبسط)
//   String _getPlainText() {
//     return _controller.document.toPlainText();
//   }

//   /// حساب إحصائيات النص (مبسط باستخدام ميزات Dart المدمجة)
//   Map<String, int> _getTextStats() {
//     final text = _getPlainText();
//     return {
//       'words': text.isEmpty ? 0 : text.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).length,
//       'characters': text.length,
//       'charactersNoSpaces': text.replaceAll(RegExp(r'\s+'), '').length,
//       'paragraphs': text.isEmpty ? 0 : text.split('\n\n').where((p) => p.trim().isNotEmpty).length,
//       'lines': text.isEmpty ? 0 : text.split('\n').length,
//     };
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (!mounted) return const SizedBox.shrink();

//     return Column(
//       children: [
//         // شريط الأدوات المبسط
//         if (widget.showToolbar && !widget.isReadOnly) _buildSimpleToolbar(),

//         // المحرر مع إعدادات محسنة
//         Expanded(
//           child: Container(
//             constraints: BoxConstraints(
//               minHeight: widget.minHeight ?? 300,
//               maxHeight: widget.maxHeight ?? double.infinity,
//             ),
//             decoration: BoxDecoration(
//               border: Border.all(color: Colors.grey.shade300),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: QuillEditor.basic(
//               controller: _controller,
//               focusNode: _focusNode,
//               scrollController: _scrollController,
//               configurations: QuillEditorConfigurations(
//                 scrollable: true,
//                 autoFocus: false,
//                 expands: true,
//                 padding: const EdgeInsets.all(16),
//                 showCursor: true,
//                 enableInteractiveSelection: true,
//                 placeholder: widget.placeholder,
//                 textCapitalization: TextCapitalization.none,
//                 // دعم اتجاه النص العربي مدمج
//                 // textDirection: TextDirection.rtl,
//               ),
//             ),
//           ),
//         ),

//         // شريط الحالة المبسط
//         _buildStatusBar(),
//       ],
//     );
//   }

//   /// بناء شريط الأدوات المبسط
//   Widget _buildSimpleToolbar() {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.grey.shade50,
//         border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
//       ),
//       child: Column(
//         children: [
//           // شريط الأدوات الأساسي من Quill
//           QuillSimpleToolbar(
//             controller: _controller,
//             configurations: QuillSimpleToolbarConfigurations(
//               multiRowsDisplay: false,
//               showFontFamily: true,
//               showFontSize: true,
//               showBoldButton: true,
//               showItalicButton: true,
//               showUnderLineButton: true,
//               showStrikeThrough: true,
//               showColorButton: true,
//               showBackgroundColorButton: true,
//               showClearFormat: true,
//               showAlignmentButtons: true,
//               showDirection: true, // مهم للعربية
//               showHeaderStyle: true,
//               showListNumbers: true,
//               showListBullets: true,
//               showCodeBlock: true,
//               showQuote: true,
//               showIndent: true,
//               showLink: true,
//               showUndo: true,
//               showRedo: true,
//               showSearchButton: true,
//               // خطوط عربية
//               fontFamilyValues: const {
//                 'Arial': 'Arial',
//                 'Times New Roman': 'Times New Roman',
//                 'Cairo': 'Cairo',
//                 'Amiri': 'Amiri',
//                 'Noto Sans Arabic': 'Noto Sans Arabic',
//               },
//             ),
//           ),

//           // أدوات إضافية مبسطة
//           _buildCustomToolbar(),
//         ],
//       ),
//     );
//   }

//   /// شريط أدوات مخصص مبسط
//   Widget _buildCustomToolbar() {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: Colors.grey.shade100,
//         border: Border(top: BorderSide(color: Colors.grey.shade300)),
//       ),
//       child: Row(
//         children: [
//           // أزرار التصدير
//           _buildToolbarButton(
//             icon: Icons.print,
//             tooltip: 'طباعة',
//             onPressed: _printDocument,
//           ),
//           _buildToolbarButton(
//             icon: Icons.picture_as_pdf,
//             tooltip: 'تصدير PDF',
//             onPressed: _exportToPdf,
//           ),
          
//           const VerticalDivider(),
          
//           // أزرار الوسائط
//           _buildToolbarButton(
//             icon: Icons.table_chart,
//             tooltip: 'إدراج جدول',
//             onPressed: _insertTable,
//           ),
//           _buildToolbarButton(
//             icon: Icons.image,
//             tooltip: 'إدراج صورة',
//             onPressed: _insertImage,
//           ),

//           // التعليقات إذا كانت مفعلة
//           if (widget.enableComments)
//             _buildToolbarButton(
//               icon: Icons.comment,
//               tooltip: 'إضافة تعليق',
//               onPressed: _addComment,
//             ),

//           const Spacer(),

//           // حالة الحفظ
//           _buildSaveStatus(),
//         ],
//       ),
//     );
//   }

//   /// بناء زر شريط الأدوات
//   Widget _buildToolbarButton({
//     required IconData icon,
//     required String tooltip,
//     required VoidCallback onPressed,
//   }) {
//     return Tooltip(
//       message: tooltip,
//       child: IconButton(
//         icon: Icon(icon, size: 18),
//         onPressed: onPressed,
//         padding: const EdgeInsets.all(4),
//         constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
//       ),
//     );
//   }

//   /// مؤشر حالة الحفظ
//   Widget _buildSaveStatus() {
//     if (_isSaving) {
//       return Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           SizedBox(
//             width: 12,
//             height: 12,
//             child: CircularProgressIndicator(
//               strokeWidth: 2,
//               valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
//             ),
//           ),
//           const SizedBox(width: 4),
//           Text(
//             'جاري الحفظ...',
//             style: TextStyle(fontSize: 11, color: Colors.blue),
//           ),
//         ],
//       );
//     }

//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Icon(Icons.check_circle, size: 12, color: Colors.green),
//         const SizedBox(width: 4),
//         Text(
//           'محفوظ',
//           style: TextStyle(fontSize: 11, color: Colors.green),
//         ),
//       ],
//     );
//   }

//   /// شريط الحالة المبسط
//   Widget _buildStatusBar() {
//     final stats = _getTextStats();
    
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       decoration: BoxDecoration(
//         color: Colors.grey.shade100,
//         border: Border(top: BorderSide(color: Colors.grey.shade300)),
//       ),
//       child: Row(
//         children: [
//           Text(
//             'الكلمات: ${stats['words']}',
//             style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
//           ),
//           const SizedBox(width: 16),
//           Text(
//             'الأحرف: ${stats['characters']}',
//             style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
//           ),
//           const SizedBox(width: 16),
//           Text(
//             'الفقرات: ${stats['paragraphs']}',
//             style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
//           ),

//           const Spacer(),

//           // زر الحفظ
//           if (widget.onSave != null)
//             TextButton.icon(
//               onPressed: widget.onSave,
//               icon: const Icon(Icons.save, size: 16),
//               label: const Text('حفظ'),
//               style: TextButton.styleFrom(
//                 foregroundColor: Colors.blue,
//                 textStyle: const TextStyle(fontSize: 12),
//               ),
//             ),
//         ],
//       ),
//     );
//   }

//   // ==================== الميزات المتقدمة المبسطة ====================

//   /// طباعة المستند (مبسط باستخدام Printing مباشرة)
//   Future<void> _printDocument() async {
//     try {
//       _showLoadingDialog('جاري تحضير المستند للطباعة...');
      
//       final pdfBytes = await _generateSimplePdf();
      
//       if (mounted) Navigator.of(context).pop();

//       await Printing.layoutPdf(
//         onLayout: (format) => pdfBytes,
//         name: 'مستند_${DateTime.now().millisecondsSinceEpoch}',
//         format: PdfPageFormat.a4,
//       );

//       debugPrint('✅ تم عرض معاينة الطباعة');
//     } catch (e) {
//       if (mounted) Navigator.of(context).pop();
//       _showErrorSnackbar('خطأ في الطباعة: $e');
//     }
//   }

//   /// تصدير PDF مبسط
//   Future<void> _exportToPdf() async {
//     try {
//       _showLoadingDialog('جاري تصدير PDF...');
      
//       final pdfBytes = await _generateSimplePdf();
      
//       if (mounted) Navigator.of(context).pop();

//       final fileName = 'مستند_${DateTime.now().millisecondsSinceEpoch}.pdf';
//       await Printing.sharePdf(bytes: pdfBytes, filename: fileName);

//       if (widget.onExport != null) {
//         widget.onExport!('pdf', _getPlainText());
//       }

//       _showSuccessSnackbar('تم تصدير PDF بنجاح');
//     } catch (e) {
//       if (mounted) Navigator.of(context).pop();
//       _showErrorSnackbar('خطأ في تصدير PDF: $e');
//     }
//   }

//   /// توليد PDF مبسط باستخدام MultiPage
//   Future<Uint8List> _generateSimplePdf() async {
//     final text = _getPlainText();
//     final safeText = text.isNotEmpty ? text : 'لا يوجد محتوى';
    
//     // استخدام الخطوط المحملة مسبقاً
//     final arabicFont = _cachedArabicFont ?? pw.Font.times();
//     final arabicBoldFont = _cachedArabicBoldFont ?? pw.Font.timesBold();

//     final pdf = pw.Document(
//       theme: pw.ThemeData.withFont(
//         base: arabicFont,
//         bold: arabicBoldFont,
//       ),
//     );

//     // استخدام MultiPage المدمج - لا حاجة لتقسيم يدوي
//     pdf.addPage(
//       pw.MultiPage(
//         pageFormat: PdfPageFormat.a4,
//         textDirection: pw.TextDirection.rtl,
//         margin: const pw.EdgeInsets.all(40),
//         build: (pw.Context context) {
//           return [
//             // عنوان المستند
//             pw.Container(
//               margin: const pw.EdgeInsets.only(bottom: 20),
//               child: pw.Column(
//                 crossAxisAlignment: pw.CrossAxisAlignment.start,
//                 children: [
//                   pw.Text(
//                     widget.documentTitle ?? 'مستند PDF',
//                     style: pw.TextStyle(
//                       fontSize: 20,
//                       font: arabicBoldFont,
//                       color: PdfColors.blue800,
//                     ),
//                     textDirection: pw.TextDirection.rtl,
//                   ),
//                   pw.SizedBox(height: 10),
//                   pw.Divider(color: PdfColors.grey400),
//                 ],
//               ),
//             ),
            
//             // المحتوى الرئيسي - MultiPage سيتولى التقسيم التلقائي
//             pw.Text(
//               safeText,
//               style: pw.TextStyle(
//                 fontSize: 14,
//                 font: arabicFont,
//                 lineSpacing: 1.5,
//               ),
//               textDirection: pw.TextDirection.rtl,
//               textAlign: pw.TextAlign.right,
//             ),
//           ];
//         },
        
//         // رأس الصفحة
//         header: (pw.Context context) {
//           return pw.Container(
//             margin: const pw.EdgeInsets.only(bottom: 10),
//             child: pw.Text(
//               'صفحة ${context.pageNumber}',
//               style: pw.TextStyle(fontSize: 10, font: arabicFont),
//               textDirection: pw.TextDirection.rtl,
//             ),
//           );
//         },
        
//         // تذييل الصفحة
//         footer: (pw.Context context) {
//           return pw.Container(
//             margin: const pw.EdgeInsets.only(top: 10),
//             child: pw.Text(
//               DateTime.now().toString().split('.')[0],
//               style: pw.TextStyle(fontSize: 8, font: arabicFont),
//               textDirection: pw.TextDirection.rtl,
//             ),
//           );
//         },
//       ),
//     );

//     return await pdf.save();
//   }

//   /// إدراج جدول مبسط
//   Future<void> _insertTable() async {
//     try {
//       final tableConfig = await _showTableConfigDialog();
//       if (tableConfig == null) return;

//       final index = _controller.selection.baseOffset;
//       final tableText = _generateSimpleTable(
//         tableConfig['rows'] as int,
//         tableConfig['columns'] as int,
//         tableConfig['hasHeader'] as bool,
//       );

//       _controller.document.insert(index, '\n$tableText\n');
//       _showSuccessSnackbar('تم إدراج الجدول بنجاح');
//     } catch (e) {
//       _showErrorSnackbar('خطأ في إدراج الجدول: $e');
//     }
//   }

//   /// توليد جدول بسيط
//   String _generateSimpleTable(int rows, int columns, bool hasHeader) {
//     final buffer = StringBuffer();
    
//     // عنوان الجدول
//     buffer.writeln('┌─ جدول ${rows}x$columns ─┐');
    
//     // صف العنوان
//     if (hasHeader) {
//       buffer.write('│ ');
//       for (int col = 1; col <= columns; col++) {
//         buffer.write('عنوان $col'.padRight(10));
//         if (col < columns) buffer.write('│ ');
//       }
//       buffer.writeln(' │');
      
//       // خط فاصل
//       buffer.write('├');
//       for (int col = 1; col <= columns; col++) {
//         buffer.write('─' * 10);
//         if (col < columns) buffer.write('┼');
//       }
//       buffer.writeln('┤');
//       rows--; // تقليل عدد الصفوف
//     }
    
//     // صفوف البيانات
//     for (int row = 1; row <= rows; row++) {
//       buffer.write('│ ');
//       for (int col = 1; col <= columns; col++) {
//         buffer.write('خلية $row-$col'.padRight(10));
//         if (col < columns) buffer.write('│ ');
//       }
//       buffer.writeln(' │');
//     }
    
//     // إغلاق الجدول
//     buffer.write('└');
//     for (int col = 1; col <= columns; col++) {
//       buffer.write('─' * 10);
//       if (col < columns) buffer.write('┴');
//     }
//     buffer.writeln('┘');
    
//     return buffer.toString();
//   }

//   /// حوار تكوين الجدول
//   Future<Map<String, dynamic>?> _showTableConfigDialog() async {
//     int rows = 3;
//     int columns = 3;
//     bool hasHeader = true;

//     return await showDialog<Map<String, dynamic>>(
//       context: context,
//       builder: (context) => StatefulBuilder(
//         builder: (context, setState) => AlertDialog(
//           title: const Text('إعدادات الجدول'),
//           content: Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               // عدد الصفوف
//               Row(
//                 children: [
//                   const Text('عدد الصفوف: '),
//                   const Spacer(),
//                   IconButton(
//                     onPressed: rows > 1 ? () => setState(() => rows--) : null,
//                     icon: const Icon(Icons.remove),
//                   ),
//                   Text('$rows'),
//                   IconButton(
//                     onPressed: rows < 10 ? () => setState(() => rows++) : null,
//                     icon: const Icon(Icons.add),
//                   ),
//                 ],
//               ),
              
//               // عدد الأعمدة
//               Row(
//                 children: [
//                   const Text('عدد الأعمدة: '),
//                   const Spacer(),
//                   IconButton(
//                     onPressed: columns > 1 ? () => setState(() => columns--) : null,
//                     icon: const Icon(Icons.remove),
//                   ),
//                   Text('$columns'),
//                   IconButton(
//                     onPressed: columns < 10 ? () => setState(() => columns++) : null,
//                     icon: const Icon(Icons.add),
//                   ),
//                 ],
//               ),
              
//               // صف العنوان
//               CheckboxListTile(
//                 title: const Text('إضافة صف عنوان'),
//                 value: hasHeader,
//                 onChanged: (value) => setState(() => hasHeader = value ?? true),
//               ),
//             ],
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.of(context).pop(),
//               child: const Text('إلغاء'),
//             ),
//             ElevatedButton(
//               onPressed: () => Navigator.of(context).pop({
//                 'rows': rows,
//                 'columns': columns,
//                 'hasHeader': hasHeader,
//               }),
//               child: const Text('إدراج'),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   /// إدراج صورة مبسط
//   Future<void> _insertImage() async {
//     try {
//       final source = await _showImageSourceDialog();
//       if (source == null) return;

//       File? imageFile;
      
//       if (source == ImageSource.camera) {
//         final picker = ImagePicker();
//         final pickedFile = await picker.pickImage(source: ImageSource.camera);
//         if (pickedFile != null) {
//           imageFile = File(pickedFile.path);
//         }
//       } else {
//         final result = await FilePicker.platform.pickFiles(
//           type: FileType.image,
//           allowMultiple: false,
//         );
//         if (result != null && result.files.isNotEmpty && result.files.first.path != null) {
//           imageFile = File(result.files.first.path!);
//         }
//       }

//       if (imageFile != null) {
//         await _processAndInsertImage(imageFile);
//       }
//     } catch (e) {
//       _showErrorSnackbar('خطأ في إدراج الصورة: $e');
//     }
//   }

//   /// حوار اختيار مصدر الصورة
//   Future<ImageSource?> _showImageSourceDialog() async {
//     return await showDialog<ImageSource>(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('اختيار مصدر الصورة'),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             ListTile(
//               leading: const Icon(Icons.camera_alt),
//               title: const Text('التقاط صورة'),
//               onTap: () => Navigator.of(context).pop(ImageSource.camera),
//             ),
//             ListTile(
//               leading: const Icon(Icons.photo_library),
//               title: const Text('اختيار من المعرض'),
//               onTap: () => Navigator.of(context).pop(ImageSource.gallery),
//             ),
//           ],
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إلغاء'),
//           ),
//         ],
//       ),
//     );
//   }

//   /// معالجة وإدراج الصورة
//   Future<void> _processAndInsertImage(File imageFile) async {
//     try {
//       _showLoadingDialog('جاري رفع الصورة...');

//       final uploadService = UploadService();
//       final uploadedPath = await uploadService.uploadFile(
//         imageFile,
//         'image_${DateTime.now().millisecondsSinceEpoch}.${imageFile.path.split('.').last}',
//       );

//       if (mounted) Navigator.of(context).pop();

//       if (uploadedPath != null) {
//         final index = _controller.selection.baseOffset;
//         final imageEmbed = BlockEmbed.image(uploadedPath);
//         _controller.document.insert(index, imageEmbed);
//         _showSuccessSnackbar('تم إدراج الصورة بنجاح');
//       } else {
//         _showErrorSnackbar('فشل في رفع الصورة');
//       }
//     } catch (e) {
//       if (mounted && Navigator.of(context).canPop()) {
//         Navigator.of(context).pop();
//       }
//       _showErrorSnackbar('خطأ في معالجة الصورة: $e');
//     }
//   }

//   /// إضافة تعليق مبسط
//   Future<void> _addComment() async {
//     try {
//       final selection = _controller.selection;
//       if (selection.isCollapsed) {
//         _showErrorSnackbar('يرجى تحديد النص المراد التعليق عليه');
//         return;
//       }

//       final selectedText = _getPlainText().substring(selection.start, selection.end);
//       final comment = await _showCommentDialog(selectedText);

//       if (comment != null && comment.isNotEmpty && widget.onAddComment != null) {
//         widget.onAddComment!(comment, selection.start, selection.end);
//         _showSuccessSnackbar('تم إضافة التعليق بنجاح');
//       }
//     } catch (e) {
//       _showErrorSnackbar('خطأ في إضافة التعليق: $e');
//     }
//   }

//   /// حوار إضافة تعليق
//   Future<String?> _showCommentDialog(String selectedText) async {
//     final commentController = TextEditingController();

//     return await showDialog<String>(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('إضافة تعليق'),
//         content: SizedBox(
//           width: 400,
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               const Text('النص المحدد:'),
//               const SizedBox(height: 8),
//               Container(
//                 padding: const EdgeInsets.all(12),
//                 decoration: BoxDecoration(
//                   color: Colors.grey.shade100,
//                   borderRadius: BorderRadius.circular(8),
//                   border: Border.all(color: Colors.grey.shade300),
//                 ),
//                 child: Text(
//                   selectedText,
//                   style: const TextStyle(fontStyle: FontStyle.italic),
//                 ),
//               ),
//               const SizedBox(height: 16),
//               const Text('التعليق:'),
//               const SizedBox(height: 8),
//               TextField(
//                 controller: commentController,
//                 decoration: const InputDecoration(
//                   hintText: 'اكتب تعليقك هنا...',
//                   border: OutlineInputBorder(),
//                 ),
//                 maxLines: 3,
//                 autofocus: true,
//               ),
//             ],
//           ),
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: const Text('إلغاء'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               final comment = commentController.text.trim();
//               if (comment.isNotEmpty) {
//                 Navigator.of(context).pop(comment);
//               }
//             },
//             child: const Text('إضافة'),
//           ),
//         ],
//       ),
//     );
//   }

//   // ==================== دوال المساعدة ====================

//   /// عرض حوار التحميل
//   void _showLoadingDialog(String message) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (context) => AlertDialog(
//         content: Row(
//           children: [
//             const CircularProgressIndicator(),
//             const SizedBox(width: 16),
//             Expanded(child: Text(message)),
//           ],
//         ),
//       ),
//     );
//   }

//   /// عرض رسالة نجاح
//   void _showSuccessSnackbar(String message) {
//     if (!mounted) return;
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Colors.green,
//         duration: const Duration(seconds: 3),
//       ),
//     );
//   }

//   /// عرض رسالة خطأ
//   void _showErrorSnackbar(String message) {
//     if (!mounted) return;
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: Colors.red,
//         duration: const Duration(seconds: 3),
//       ),
//     );
//   }
// }