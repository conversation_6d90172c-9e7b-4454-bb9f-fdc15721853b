import 'package:flutter/material.dart';
// import '../../screens/admin/role_management_tab.dart';
import '../../models/custom_role_model.dart';

/// ودجت موحد لعرض الدور (مختصر أو تفصيلي)
class RoleCardWidget extends StatelessWidget {
  final CustomRole role;
  final bool isDetailed; // true = عرض تفصيلي، false = مختصر
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onTap;
  final bool isSelected;

  const RoleCardWidget({
    super.key,
    required this.role,
    this.isDetailed = false,
    this.onEdit,
    this.onDelete,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final color = Theme.of(context).primaryColor;
    final icon = Icons.group;

    if (isDetailed) {
      // عرض تفصيلي
      return Card(
        margin: const EdgeInsets.all(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: color,
                    child: Icon(icon, color: Colors.white, size: 30),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          role.name,
                          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                        ),
                        if (role.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            role.description!,
                            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (onEdit != null || onDelete != null)
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit' && onEdit != null) onEdit!();
                        if (value == 'delete' && onDelete != null) onDelete!();
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          const PopupMenuItem(value: 'edit', child: ListTile(leading: Icon(Icons.edit), title: Text('تعديل'))),
                        if (onDelete != null)
                          const PopupMenuItem(value: 'delete', child: ListTile(leading: Icon(Icons.delete), title: Text('حذف'))),
                      ],
                    ),
                ],
              ),
              // يمكن إضافة تفاصيل إضافية هنا
            ],
          ),
        ),
      );
    } else {
      // عرض مختصر (عنصر قائمة)
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withAlpha(25) : null,
          borderRadius: BorderRadius.circular(8),
          border: isSelected ? Border.all(color: Theme.of(context).primaryColor, width: 2) : null,
        ),
        child: ListTile(
          onTap: onTap,
          leading: CircleAvatar(
            backgroundColor: color,
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          title: Text(
            role.name,
            style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
          ),
          subtitle: role.description != null
              ? Text(role.description!, maxLines: 2, overflow: TextOverflow.ellipsis)
              : null,
          trailing: PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit' && onEdit != null) onEdit!();
              if (value == 'delete' && onDelete != null) onDelete!();
            },
            itemBuilder: (context) => [
              if (onEdit != null)
                const PopupMenuItem(value: 'edit', child: ListTile(leading: Icon(Icons.edit), title: Text('تعديل'))),
              if (onDelete != null)
                const PopupMenuItem(value: 'delete', child: ListTile(leading: Icon(Icons.delete), title: Text('حذف'))),
            ],
          ),
        ),
      );
    }
  }
}
