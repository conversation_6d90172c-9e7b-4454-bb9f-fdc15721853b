import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/task_models.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../utils/date_formatter.dart';
import '../../screens/home/<USER>';
import 'create_task_screen.dart';
// تم تعليق استيرادات لوحات المهام المحسنة لاستخدام قائمة المهام الرئيسية فقط
// import 'simple_board_screen.dart';
// import 'enhanced_task_board_screen.dart';

class TaskBoardScreen extends StatefulWidget {
  const TaskBoardScreen({super.key});

  @override
  State<TaskBoardScreen> createState() => _TaskBoardScreenState();
}

class _TaskBoardScreenState extends State<TaskBoardScreen> {
  bool _isExpanded = true;
  bool _isCompletedExpanded = true;

  @override
  void initState() {
    super.initState();
    // استخدام ميكانيكية addPostFrameCallback لتأخير تحميل المهام حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTasks();
    });
  }

  Future<void> _loadTasks() async {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    if (authController.isSuperAdmin) {
      // مدير النظام العام يرى جميع المهام
      await taskController.loadAllTasks();
    } else if (authController.isAdmin &&
        authController.currentUser.value?.departmentId != null) {
      // مدير الإدارة يرى مهام إدارته فقط
      await taskController.loadTasksByDepartment(
          authController.currentUser.value!.departmentId!);
    } else if (authController.isDepartmentManager &&
        authController.currentUser.value?.departmentId != null) {
      // مدير القسم يرى مهام قسمه فقط
      await taskController.loadTasksByDepartment(
          authController.currentUser.value!.departmentId!);
    } else {
      // الموظف العادي يرى مهامه المخصصة له فقط
      await taskController
          .loadTasksByAssignee(authController.currentUser.value!.id);
    }
  }

  /// Builds the task board screen UI
  @override
  Widget build(BuildContext context) {
    // Get controllers
    final taskController = Get.find<TaskController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('عرض الجدول'),
        actions: [
          // تم تعليق أزرار التنقل إلى لوحات المهام المحسنة لاستخدام قائمة المهام الرئيسية فقط
          // Enhanced board view button
          /*IconButton(
            icon: const Icon(Icons.dashboard),
            tooltip: 'عرض المهام المحسن',
            onPressed: () {
              Get.off(() => const EnhancedTaskBoardScreen());
            },
          ),*/
          // View toggle button - return to main tasks view
          IconButton(
            icon: const Icon(Icons.view_list),
            tooltip: 'عرض القائمة',
            onPressed: () {
              Get.off(() => const TasksTab());
            },
          ),
          // Drag & Drop Board button
          /*IconButton(
            icon: const Icon(Icons.drag_indicator),
            tooltip: 'لوحة السحب والإفلات',
            onPressed: () {
              Get.off(() => const SimpleBoardScreen());
            },
          ),*/
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'تصفية المهام',
            onPressed: () {
              // Show filter options
              Get.bottomSheet(
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('Filter Tasks', style: AppStyles.headingMedium),
                      const SizedBox(height: 16),
                      // Filter options would go here
                      ElevatedButton(
                        onPressed: () => Get.back(),
                        child: const Text('Apply Filters'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          // Sort button
          IconButton(
            icon: const Icon(Icons.sort),
            tooltip: 'ترتيب المهام',
            onPressed: () {
              // Show sort options
              Get.bottomSheet(
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('Sort Tasks', style: AppStyles.headingMedium),
                      const SizedBox(height: 16),
                      // Sort options would go here
                      ElevatedButton(
                        onPressed: () => Get.back(),
                        child: const Text('Apply Sorting'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Obx(() {
        if (taskController.isLoading) {
          return const Center(child: CircularProgressIndicator());
        } else {
          return RefreshIndicator(
            onRefresh: _loadTasks,
            child: _buildTaskBoard(taskController.filteredTasks),
          );
        }
      }),
      floatingActionButton: FloatingActionButton(
        heroTag: 'task_board_fab',
        onPressed: () {
          // Navigate to create task screen
          Get.to(() => const CreateTaskScreen());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Builds the task board with grouped tasks
  Widget _buildTaskBoard(List<Task> allTasks) {
    // Group tasks by status using string values
    final pendingTasks = allTasks
        .where((task) =>
            task.status == 'pending' ||
            task.status == 'in_progress' ||
            task.status == 'waiting_for_info')
        .toList();

    final completedTasks =
        allTasks.where((task) => task.status == 'completed').toList();

    return SingleChildScrollView(
      child: Column(
        children: [
          // Header row
          _buildHeaderRow(),

          // To-Do section (pending, in progress, waiting)
          _buildTaskGroup(
            title: 'To-Do',
            tasks: pendingTasks,
            isExpanded: _isExpanded,
            onToggleExpanded: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),

          // Completed section
          _buildTaskGroup(
            title: 'Completed',
            tasks: completedTasks,
            isExpanded: _isCompletedExpanded,
            onToggleExpanded: () {
              setState(() {
                _isCompletedExpanded = !_isCompletedExpanded;
              });
            },
          ),

          // Add new group button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: OutlinedButton.icon(
              onPressed: () {
                // Add new group functionality
                Get.dialog(
                  AlertDialog(
                    title: const Text('Add New Group'),
                    content: TextField(
                      decoration: const InputDecoration(
                        labelText: 'Group Name',
                        hintText: 'Enter a name for the new group',
                      ),
                      autofocus: true,
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('Cancel'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          // Create new group
                          Get.back();
                          Get.snackbar(
                            'Success',
                            'New group created',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        },
                        child: const Text('Create'),
                      ),
                    ],
                  ),
                );
              },
              icon: const Icon(Icons.add),
              label: const Text('Add new group'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: BorderSide(color: AppColors.getBorderColor()),
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Row(
        children: [
          // Checkbox column
          const SizedBox(width: 24),

          // Task column
          Expanded(
            flex: 3,
            child: Text(
              'Task',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Budget column
          Expanded(
            flex: 2,
            child: Text(
              'Budget',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Files column
          Expanded(
            flex: 1,
            child: Text(
              'Files',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Timeline column
          Expanded(
            flex: 2,
            child: Text(
              'Timeline',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Last updated column
          Expanded(
            flex: 2,
            child: Text(
              'Last updated',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Priority column
          Expanded(
            flex: 2,
            child: Text(
              'Priority',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Label column
          Expanded(
            flex: 2,
            child: Text(
              'Label',
              style: AppStyles.titleSmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),

          // Add column button
          IconButton(
            icon: Icon(Icons.add, color: AppColors.textSecondary),
            onPressed: () {
              // Add column functionality
              Get.dialog(
                AlertDialog(
                  title: const Text('Add Column'),
                  content: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Column Name',
                      hintText: 'Enter a name for the new column',
                    ),
                    autofocus: true,
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        // Add new column
                        Get.back();
                        Get.snackbar(
                          'Success',
                          'New column added',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      child: const Text('Add'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTaskGroup({
    required String title,
    required List<Task> tasks,
    required bool isExpanded,
    required VoidCallback onToggleExpanded,
  }) {
    return Column(
      children: [
        // Group header
        InkWell(
          onTap: onToggleExpanded,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: title == 'To-Do'
                  ? Get.isDarkMode
                      ? const Color(0xFF1A3A57) // Dark blue with low opacity
                      : const Color(0xFFE3F2FD) // Light blue with low opacity
                  : Get.isDarkMode
                      ? const Color(0xFF1B5E20) // Dark green with low opacity
                      : const Color(0xFFE8F5E9), // Light green with low opacity
              border: Border(
                bottom: BorderSide(color: AppColors.getBorderColor()),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_down
                      : Icons.keyboard_arrow_right,
                  color: title == 'To-Do' ? AppColors.info : AppColors.success,
                ),
                const SizedBox(width: 8),
                Text(
                  '$title (${tasks.length})',
                  style: AppStyles.titleMedium.copyWith(
                    color:
                        title == 'To-Do' ? AppColors.info : AppColors.success,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Task rows
        if (isExpanded) ...tasks.map((task) => _buildTaskRow(task)),

        // Add task row
        if (isExpanded)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.card,
              border: Border(
                bottom: BorderSide(color: AppColors.getBorderColor()),
              ),
            ),
            child: Row(
              children: [
                const SizedBox(width: 24),
                Text(
                  '+ Add task',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildTaskRow(Task task) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.getBorderColor()),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          Checkbox(
            value: task.status == 'completed',
            onChanged: (value) {
              // Update task status
              if (value != null) {
                final taskController = Get.find<TaskController>();
                final newStatus = value
                    ? 'completed'
                    : 'pending';

                // Create updated task
                final updatedTask = task.copyWith(
                  status: newStatus,
                  completionPercentage: value ? 100 : 0,
                );

                // Update task
                taskController.updateTask(
                  id: updatedTask.id,
                  status: newStatus,
                  completionPercentage: value ? 100 : 0,
                );
              }
            },
          ),

          // Task column
          Expanded(
            flex: 3,
            child: Row(
              children: [
                // Priority indicator
                Container(
                  width: 4,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.getTaskPriorityColor(task.priority),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.title,
                        style: AppStyles.titleSmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (task.description != null && task.description!.isNotEmpty)
                        Text(
                          task.description!,
                          style: AppStyles.bodySmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Budget column
          Expanded(
            flex: 2,
            child: Text(
              '\$0', // Placeholder for budget
              style: AppStyles.bodyMedium,
            ),
          ),

          // Files column
          Expanded(
            flex: 1,
            child: task.accessUserIds != null && task.accessUserIds!.isNotEmpty
                ? Icon(
                    Icons.attach_file,
                    color: AppColors.primary,
                    size: 20,
                  )
                : const SizedBox(),
          ),

          // Timeline column
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getTimelineColor(task),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                task.dueDate != null
                    ? DateFormatter.formatShortDate(task.dueDateDateTime!)
                    : '-',
                style: AppStyles.labelSmall.copyWith(
                  color: Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // Last updated column
          Expanded(
            flex: 2,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: AppColors.primary,
                  child: const Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '2 days ago', // Placeholder for last updated
                  style: AppStyles.bodySmall,
                ),
              ],
            ),
          ),

          // Priority column
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.getTaskPriorityColor(task.priority).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getPriorityDisplayName(task.priority),
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.getTaskPriorityColor(task.priority),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // Label column
          Expanded(
            flex: 2,
            child: Container(
              height: 24,
              decoration: BoxDecoration(
                color: Get.isDarkMode
                    ? const Color(0xFF424242)
                    : const Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTimelineColor(Task task) {
    if (task.status == 'completed') {
      return AppColors.success;
    }

    if (task.dueDate == null) {
      return AppColors.statusPending;
    }

    if (task.isOverdue()) {
      return AppColors.error;
    }

    // Due soon (within 3 days)
    final daysRemaining = task.daysRemaining;
    if (daysRemaining != null && daysRemaining <= 3) {
      return AppColors.warning;
    }

    return AppColors.info;
  }

  /// الحصول على اسم الأولوية للعرض
  String _getPriorityDisplayName(String priority) {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      case 'urgent':
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }


}
