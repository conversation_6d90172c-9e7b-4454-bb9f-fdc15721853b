import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/models/attachment_model.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';

// import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart'; // تم تعليقه مؤقتًا بسبب مشكلة التوافق
import 'package:flutter_application_2/controllers/task_controller.dart';
import 'package:flutter_application_2/controllers/attachments_controller.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';

import 'package:flutter_application_2/models/user_model.dart';
import 'package:flutter_application_2/utils/file_processor.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/screens/widgets/drop_zone_widget.dart';
import 'package:flutter_application_2/screens/widgets/file_viewer_widget.dart';
import 'package:flutter_application_2/screens/widgets/attachment_preview_widget.dart';
import 'package:flutter_application_2/services/download_service.dart';
import 'package:flutter_application_2/services/unified_signalr_service.dart';

/// علامة تبويب المرفقات
/// تعرض المرفقات المرتبطة بالمهمة وتتيح إضافة مرفقات جديدة
class TaskAttachmentsTab extends StatefulWidget {
  final List<Attachment>? attachments;
  const TaskAttachmentsTab({super.key, this.attachments});

  @override
  State<TaskAttachmentsTab> createState() => _TaskAttachmentsTabState();
}

class _TaskAttachmentsTabState extends State<TaskAttachmentsTab> {
  final TaskController _taskController = Get.find<TaskController>();
  final AttachmentsController _attachmentsController = Get.find<AttachmentsController>();
  final AuthController _authController = Get.find<AuthController>();

  // فلتر المرفقات
  String _filterType = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();

    final signalRService = Get.find<UnifiedSignalRService>();

    // الاشتراك في إشعار إضافة مرفق - تحديث سريع للمرفقات فقط
    signalRService.hubConnection?.on("AttachmentAdded", (arguments) async {
      if (arguments == null || arguments.isEmpty) return;
      final int? taskId = arguments[0] is int ? arguments[0] : int.tryParse(arguments[0].toString());
      if (taskId != null && _taskController.currentTask?.id == taskId) {
        // تحديث سريع للمرفقات فقط بدلاً من تحديث المهمة كاملة
        await _taskController.refreshTaskAttachments(taskId);
        debugPrint('🔔 تم تحديث المرفقات بعد إشعار SignalR - إضافة مرفق');
      }
    });

    // الاشتراك في إشعار حذف مرفق - تحديث سريع للمرفقات فقط
    signalRService.hubConnection?.on("AttachmentDeleted", (arguments) async {
      if (arguments == null || arguments.isEmpty) return;
      final int? taskId = arguments[0] is int ? arguments[0] : int.tryParse(arguments[0].toString());
      if (taskId != null && _taskController.currentTask?.id == taskId) {
        // تحديث سريع للمرفقات فقط بدلاً من تحديث المهمة كاملة
        await _taskController.refreshTaskAttachments(taskId);
        debugPrint('🔔 تم تحديث المرفقات بعد إشعار SignalR - حذف مرفق');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // استخدام GetBuilder لتحديث الواجهة تلقائياً عند تغيير البيانات
    return GetBuilder<TaskController>(
      id: 'task_details', // نفس المعرف المستخدم في TaskDetailScreen
      builder: (controller) {
        // يجب الاعتماد فقط على controller.currentTask وعدم استخدام أي نسخة محلية
        final task = controller.currentTask;

        if (task == null) {
          return const Center(child: Text('لم يتم تحميل المهمة'));
        }

        // استخدم قائمة المرفقات من الكائن الحالي للمهمة دائماً
        // لا تحفظ أي نسخة محلية أو متغير خارجي
        final filteredAttachments = _getFilteredAttachments(List<Attachment>.from(task.attachments));
        debugPrint('📦 [task_attachments_tab] attachmentsCount: ' + task.attachments.length.toString());

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان القسم
              Text(
                'مرفقات المهمة',
                style: AppStyles.titleLarge,
              ),
              const SizedBox(height: 16),

              // شريط البحث والفلترة
              _buildSearchAndFilterBar(),
              const SizedBox(height: 16),

              // زر إضافة مرفق
              _buildAddAttachmentButton(),
              const SizedBox(height: 16),

              // منطقة سحب وإسقاط الملفات
              Expanded(
                child: DropZoneWidget(
                  onDroppedFiles: _handleDroppedFiles,
                  child: _buildAttachmentsList(filteredAttachments),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// معالجة الملفات التي تم إسقاطها
  Future<void> _handleDroppedFiles(List<XFile> files) async {
    final task = _taskController.currentTask;
    if (task == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // معالجة كل ملف تم إسقاطه
      for (final file in files) {
        final filePath = file.path;
        final fileName = file.name;
        final fileSize = await file.length();
        final fileType = lookupMimeType(filePath) ?? 'application/octet-stream';

        // رفع الملف
        await _uploadAttachment(filePath, fileType, fileName, fileSize);
      }

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'نجاح'.tr,
        'تم رفع ${files.length} ملف بنجاح'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
      );

      // تحديث فوري للواجهة
      _taskController.update(['task_details']);
      setState(() {}); // تحديث إضافي للواجهة المحلية
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء رفع الملفات: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// بناء شريط البحث والفلترة
  Widget _buildSearchAndFilterBar() {
    return Row(
      children: [
        // حقل البحث
        Expanded(
          child: TextField(
            decoration: InputDecoration(
              hintText: 'بحث في المرفقات'.tr,
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        const SizedBox(width: 8),

        // قائمة الفلترة
        DropdownButton<String>(
          value: _filterType,
          icon: const Icon(Icons.filter_list),
          underline: const SizedBox(),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _filterType = newValue;
              });
            }
          },
          items: [
            DropdownMenuItem<String>(
              value: 'all',
              child: Text('الكل'.tr),
            ),
            DropdownMenuItem<String>(
              value: 'image',
              child: Text('صور'.tr),
            ),
            DropdownMenuItem<String>(
              value: 'document',
              child: Text('مستندات'.tr),
            ),
            DropdownMenuItem<String>(
              value: 'video',
              child: Text('فيديو'.tr),
            ),
            DropdownMenuItem<String>(
              value: 'audio',
              child: Text('صوت'.tr),
            ),
            DropdownMenuItem<String>(
              value: 'other',
              child: Text('أخرى'.tr),
            ),
          ],
        ),
      ],
    );
  }

  /// فلترة المرفقات (تأخذ القائمة كمُدخل)
  List<Attachment> _getFilteredAttachments(List<Attachment> attachments) {
    List<Attachment> filtered = List.from(attachments);
    debugPrint('📦 جميع المرفقات قبل الفلترة: ${attachments.map((a) => a.fileName).join(', ')}');

    // فلترة حسب البحث (تجاهل إذا كان البحث فارغ أو قصير جداً)
    if (_searchQuery.isNotEmpty && _searchQuery.length > 2) {
      filtered = filtered.where((attachment) {
        return attachment.fileName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
      debugPrint('🔍 بعد فلترة البحث "$_searchQuery": ${filtered.map((a) => a.fileName).join(', ')}');
    }

    // فلترة حسب النوع
    if (_filterType != 'all') {
      filtered = filtered.where((attachment) {
        final attachmentMimeCategory = _getMimeCategory(attachment.fileType, attachment.fileName);
        debugPrint('🔍 فلترة المرفق: ${attachment.fileName} - نوع MIME: ${attachment.fileType} - تصنيف: $attachmentMimeCategory - فلتر: $_filterType');
        return attachmentMimeCategory == _filterType;
      }).toList();
    }

    debugPrint('🟠 بعد الفلترة النهائية: ${filtered.map((a) => a.fileName).join(', ')}');
    return filtered;
  }

  /// بناء زر إضافة مرفق
  Widget _buildAddAttachmentButton() {
    return ElevatedButton.icon(
      onPressed: _showAttachmentDialog,
      icon: const Icon(Icons.add, color: Colors.white),
      label: Text('إضافة مرفق'.tr),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// بناء قائمة المرفقات
  Widget _buildAttachmentsList(List<Attachment> attachments) {
    
    if (attachments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.attach_file,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مرفقات'.tr,
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اسحب الملفات هنا أو اضغط على زر إضافة مرفق'.tr,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    final int crossAxisCount = ResponsiveHelper.isMobile(context)
        ? 2
        : ResponsiveHelper.isTablet(context)
            ? 3
            : 4;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: attachments.length,
      itemBuilder: (context, index) {
        final attachment = attachments[index];

        return AttachmentPreview(
          attachment: attachment,
          onView: _viewAttachment,
          onOpen: !kIsWeb ? _openAttachment : null, // فقط للتطبيقات المحلية وليس للويب
          onDownload: _downloadAttachment,
          onDelete: _canDeleteAttachment(attachment) ? _deleteAttachment : null,
          canDelete: _canDeleteAttachment(attachment),
        );
      },
    );
  }

  /// عرض مربع حوار إضافة مرفق
  void _showAttachmentDialog() {
    final task = _taskController.currentTask;
    if (task == null) return;

    Get.dialog(
      AlertDialog(
        title: Text('إضافة مرفق'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text('اختيار صورة من المعرض'.tr),
              onTap: () {
                Get.back();
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text('التقاط صورة'.tr),
              onTap: () {
                Get.back();
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.upload_file),
              title: Text('اختيار ملف'.tr),
              onTap: () {
                Get.back();
                _pickFile();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // التأكد من أن اسم الملف غير فارغ
        final fileSize = await image.length();
        final imageName = image.name.isNotEmpty ? image.name : 'image_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
        _uploadAttachment(
          image.path, 
          image.mimeType ?? 'image/${imageName.split('.').last}', 
          imageName,
          fileSize,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        final fileSize = await image.length();
        final imageName = image.name.isNotEmpty ? image.name : 'image_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
        _uploadAttachment(
          image.path, 
          image.mimeType ?? 'image/${imageName.split('.').last}', 
          imageName,
          fileSize,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء التقاط الصورة: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// اختيار ملف
  Future<void> _pickFile() async {
    final task = _taskController.currentTask;
    if (task == null) return;

    try {
      final result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path != null) {
          // استخدام lookupMimeType للحصول على نوع MIME بشكل أدق إذا كان المسار متاحًا
          // file.mimeType متاح في بعض المنصات لـ PlatformFile
          final mimeType = lookupMimeType(file.name, headerBytes: file.bytes?.take(1024).toList()) ?? file.extension ?? 'application/octet-stream';
          _uploadAttachment(file.path!, mimeType, file.name, file.size);
        } else {
          // هذا يحدث غالبًا على الويب حيث لا يكون للملف مسار مباشر
          Get.snackbar(
            'تنبيه'.tr,
            'لا يمكن الوصول إلى مسار الملف. قد يكون هذا بسبب قيود المتصفح.'.tr,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.amber.shade100,
            colorText: Colors.amber.shade800,
          );
        }
      } else {
        // لم يتم اختيار ملف
        Get.snackbar(
          'معلومة'.tr,
          'لم يتم اختيار أي ملف.'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء اختيار الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// رفع مرفق
  /// تم تحسين عملية رفع الملفات بإضافة:
  /// - شريط تقدم للرفع مع عرض نسبة التقدم والوقت المتبقي
  /// - تنفيذ الرفع في الخلفية مع إمكانية متابعة العمل في التطبيق
  /// - إضافة إشعارات لإعلام المستخدم بانتهاء عملية الرفع
  Future<void> _uploadAttachment(String filePath, String fileType, String fileName, int fileSize) async {
    final task = _taskController.currentTask;
    if (task == null) return;
    
    final authUser = _authController.currentUser.value;
    if (authUser == null) {
      Get.snackbar('خطأ', 'يجب تسجيل الدخول لرفع المرفقات.');
      return;
    }

    // عرض مربع حوار تقدم الرفع
    final RxDouble uploadProgress = 0.0.obs;
    final RxString remainingTime = ''.obs;
    final RxBool isUploading = true.obs;

    // حساب الوقت المتبقي
    final startTime = DateTime.now();
    File fileToUpload;

    if (!kIsWeb) {
      fileToUpload = File(filePath);
    } 
    else {
      fileToUpload = File(filePath); // هذا قد يحتاج إلى معالجة خاصة للويب في TaskController
    }

    // عرض مربع حوار التقدم المحسن
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Text('جاري رفع الملف'.tr),
          ],
        ),
        content: Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الملف
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(FileProcessor.getFileIcon(filePath), size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(fileName,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(FileProcessor.formatFileSize(fileSize),
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: uploadProgress.value,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 8),

            // نسبة التقدم والوقت المتبقي
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${(uploadProgress.value * 100).toStringAsFixed(1)}%',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(remainingTime.value,
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ],
        )),
        actions: [
          TextButton(
            onPressed: () {
              isUploading.value = false;
              Get.back();
            },
            child: Text('إلغاء'.tr),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    try {
      // استدعاء دالة الرفع في TaskController
      final newAttachment = await _taskController.addAttachment(
        task.id,
        authUser.id,
        fileToUpload, // استخدام File object
        description: "مرفق جديد: $fileName",
        onUploadProgress: (progress) {
          if (!isUploading.value) return; // تحقق من الإلغاء
          uploadProgress.value = progress;
          _updateRemainingTime(startTime, progress, remainingTime);
        },
      );

      // إغلاق مربع الحوار بعد اكتمال الرفع أو الفشل
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (newAttachment != null) {
        // تحديث قائمة المرفقات من السيرفر مباشرة بعد الرفع
        await _taskController.refreshTaskAttachments(task.id);
        debugPrint('✅ تم رفع المرفق وتحديث القائمة من السيرفر');
        // تحديث إضافي للتأكد من تحديث الواجهة
        Future.delayed(const Duration(milliseconds: 300), () {
          _taskController.update(['task_details', 'task_attachments', 'task_overview']);
          if (mounted) setState(() {});
        });

        Get.snackbar(
          'نجاح'.tr,
          'تم رفع الملف "${newAttachment.fileName}" بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        debugPrint('Attachment added============================================================: [32m${newAttachment.fileName}[0m'); // Debugging line
      } 
      else {
        Get.snackbar(
          'خطأ'.tr,
          _taskController.error.isNotEmpty ? 'فشل رفع الملف: ${_taskController.error}' : 'فشل رفع الملف.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مربع الحوار في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء رفع الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تحديث الوقت المتبقي للرفع
  void _updateRemainingTime(DateTime startTime, double progress, RxString remainingTime) {
    if (progress <= 0) {
      remainingTime.value = 'جاري الحساب...'.tr;
      return;
    }

    final elapsedSeconds = DateTime.now().difference(startTime).inSeconds;
    if (elapsedSeconds <= 0 || progress >= 1.0) {
      remainingTime.value = '';
      return;
    }

    // حساب الوقت المتبقي بناءً على التقدم الحالي والوقت المنقضي
    final totalSeconds = (elapsedSeconds / progress).round();
    final remainingSeconds = totalSeconds - elapsedSeconds;

    if (remainingSeconds < 60) {
      remainingTime.value = '$remainingSeconds ثانية'.tr;
    } else if (remainingSeconds < 3600) {
      final minutes = (remainingSeconds / 60).round();
      remainingTime.value = '$minutes دقيقة'.tr;
    } else {
      final hours = (remainingSeconds / 3600).round();
      remainingTime.value = '$hours ساعة'.tr;
    }
  }

  /// عرض المرفق
  /// تم تحسين عرض المرفقات باستخدام عارض الملفات الموحد
  /// الذي يوفر:
  /// - دعم معاينة ملفات PDF والمستندات
  /// - تحسين واجهة عرض الصور مع إمكانية التكبير والتصغير
  /// - إضافة خيارات تحرير للصور
  /// - واجهة موحدة لجميع أنواع الملفات
  void _viewAttachment(Attachment attachment) {
    // على الويب، نفترض أن الملف موجود دائمًا
    // على المنصات الأخرى، نتحقق من وجود الملف
    bool fileExists = true;
    if (!kIsWeb) {
      final file = File(attachment.filePath);
      fileExists = file.existsSync();
    }

    // التحقق من وجود الملف
    if (!fileExists) {
      Get.snackbar(
        'خطأ'.tr,
        'الملف غير موجود'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // استخدام عارض الملفات الموحد
    Get.to(() => FileViewerWidget(
      attachment: attachment,
      showToolbar: true,
      onDownload: _downloadAttachment,
      onShare: (attachment) {
        // يمكن تنفيذ وظيفة المشاركة هنا
        Get.snackbar(
          'مشاركة'.tr,
          'جاري مشاركة الملف: ${attachment.fileName}'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    ));
  }

  /// فتح الملف باستخدام التطبيق الافتراضي
  Future<void> _openAttachment(Attachment attachment) async {
    try {
      // على الويب، نفترض أن الملف موجود دائمًا
      // على المنصات الأخرى، نتحقق من وجود الملف
      bool fileExists = true;
      if (!kIsWeb) {
        final file = File(attachment.filePath);
        fileExists = file.existsSync();
      }

      // التحقق من وجود الملف
      if (!fileExists) {
        Get.snackbar(
          'خطأ'.tr,
          'الملف غير موجود'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
        return;
      }

      // استخدام خدمة التنزيل المحسنة
      final downloadService = DownloadService();

      // فتح الملف باستخدام التطبيق الافتراضي
      final result = await downloadService.openFile(attachment.filePath);

      if (!result['success']) {
        Get.snackbar(
          'خطأ'.tr,
          result['message'] ?? 'لا يمكن فتح الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء فتح الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }














  /// تنزيل المرفق
  /// تم تحسين وظيفة التنزيل لتوفير:
  /// - عرض تقدم التنزيل
  /// - إمكانية التنزيل في الخلفية
  /// - دعم استئناف التنزيل في حالة انقطاع الاتصال
  /// - إمكانية فتح الملف بعد التنزيل
  Future<void> _downloadAttachment(Attachment attachment, {bool openAfterDownload = false}) async {
    // متغيرات لتتبع حالة التنزيل
    final RxDouble progress = 0.0.obs;

    // عرض مربع حوار تقدم التنزيل
    Get.dialog(
      AlertDialog(
        title: Text('جاري التنزيل...'.tr),
        content: Obx(() => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // معلومات الملف
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(FileProcessor.getFileIcon(attachment.filePath)),
              title: Text(attachment.fileName, maxLines: 1, overflow: TextOverflow.ellipsis),
              subtitle: Text(FileProcessor.formatFileSize(attachment.fileSize)),
            ),
            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: progress.value > 0 ? progress.value : null,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
            const SizedBox(height: 8),

            // نسبة التقدم
            Text('${(progress.value * 100).toStringAsFixed(1)}%'),
          ],
        )),
        actions: [
          // زر إلغاء التنزيل
          TextButton(
            onPressed: () {
              // يمكن تنفيذ إلغاء التنزيل هنا
              Get.back();
            },
            child: Text('إلغاء'.tr),
          ),
          // زر تنفيذ التنزيل في الخلفية
          TextButton(
            onPressed: () {
              Get.back();
              // استمرار التنزيل في الخلفية
              Get.snackbar(
                'تنزيل في الخلفية'.tr,
                'يتم تنزيل الملف في الخلفية'.tr,
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: Text('تنفيذ في الخلفية'.tr),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    try {
      // تنزيل حقيقي للملف
      final downloadService = DownloadService();
      final success = await downloadService.downloadAttachment(
        attachment,
        onProgress: (received, total) {
          if (total > 0) {
            progress.value = received / total;
          }
        },
        openAfterDownload: openAfterDownload,
      );

      // إغلاق مربع الحوار
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (success) {
        // عرض رسالة نجاح
        Get.snackbar(
          'نجاح'.tr,
          'تم تنزيل الملف "${attachment.fileName}" بنجاح'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
          duration: const Duration(seconds: 3),
        );
      } else {
        // عرض رسالة فشل
        Get.snackbar(
          'خطأ'.tr,
          'فشل في تنزيل الملف'.tr,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      // إغلاق مربع الحوار في حالة الخطأ
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ'.tr,
        'حدث خطأ أثناء تنزيل الملف: ${e.toString()}'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// حذف المرفق
  void _deleteAttachment(Attachment attachment) {
    Get.dialog(
      AlertDialog(
        title: Text('تأكيد الحذف'.tr),
        content: Text('هل أنت متأكد من حذف هذا المرفق؟'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إلغاء'.tr),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();

              // عرض مؤشر التحميل
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );

              try {
                // حذف المرفق
                final success = await _attachmentsController.deleteAttachment(attachment.id);

                // إغلاق مؤشر التحميل
                Get.back();

                if (success) {
                  // جلب بيانات المهمة من السيرفر بعد الحذف
                  await _taskController.refreshTaskDetails(_taskController.currentTask!.id);
                  // تحديث فوري للواجهة
                  _taskController.update(['task_details']);

                  Get.snackbar(
                    'نجاح'.tr,
                    'تم حذف المرفق بنجاح'.tr,
                  );
                } else {
                  Get.snackbar(
                    'خطأ'.tr,
                    'فشل حذف المرفق'.tr,
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red.shade100,
                    colorText: Colors.red.shade800,
                  );
                }
              } catch (e) {
                // إغلاق مؤشر التحميل
                Get.back();

                Get.snackbar(
                  'خطأ'.tr,
                  'حدث خطأ أثناء حذف المرفق: e.toString()}'.tr,
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'.tr),
          ),
        ],
      ),
    );
  }

  /// التحقق من إمكانية حذف المرفق
  bool _canDeleteAttachment(Attachment attachment) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return false;

    // المستخدم الذي رفع المرفق يمكنه حذفه
    if (attachment.uploadedBy == currentUser.id) return true;

    // المدير يمكنه حذف أي مرفق
    if (currentUser.role == UserRole.admin) return true;

    // مدير القسم يمكنه حذف مرفقات قسمه
    final task = _taskController.currentTask;
    if (task != null && currentUser.role == UserRole.manager && task.departmentId == currentUser.departmentId) {
      return true;
    }

    return false;
  }

  String _getMimeCategory(String? mimeType, [String? fileName]) {
    if (mimeType == null && fileName == null) return 'other';

    // إذا كان نوع MIME صحيح، استخدمه
    if (mimeType != null && mimeType != 'application/octet-stream') {
      if (mimeType.startsWith('image/')) return 'image';
      if (mimeType.startsWith('video/')) return 'video';
      if (mimeType.startsWith('audio/')) return 'audio';
      if (mimeType == 'application/pdf' ||
          mimeType.contains('document') || // Covers .doc, .docx, .odt etc.
          mimeType.contains('msword') || mimeType.contains('officedocument.wordprocessingml') ||
          mimeType.contains('excel') || mimeType.contains('spreadsheetml') ||
          mimeType.contains('powerpoint') || mimeType.contains('presentationml')) {
        return 'document';
      }
    }

    // إذا كان نوع MIME غير صحيح أو application/octet-stream، استخدم امتداد الملف
    if (fileName != null) {
      final extension = fileName.split('.').last.toLowerCase();
      switch (extension) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'webp':
        case 'svg':
          return 'image';
        case 'mp4':
        case 'avi':
        case 'mov':
        case 'wmv':
        case 'flv':
        case 'webm':
          return 'video';
        case 'mp3':
        case 'wav':
        case 'ogg':
        case 'aac':
        case 'flac':
          return 'audio';
        case 'pdf':
        case 'doc':
        case 'docx':
        case 'xls':
        case 'xlsx':
        case 'ppt':
        case 'pptx':
        case 'txt':
        case 'rtf':
          return 'document';
        default:
          return 'other';
      }
    }

    return 'other';
  }
}
