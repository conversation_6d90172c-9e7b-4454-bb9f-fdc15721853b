import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../models/user_model.dart';
import '../../../models/role_model.dart';
import '../../../models/department_model.dart';
import '../shared/admin_form_widget.dart';
import '../shared/admin_dialog_widget.dart';

/// حوار نموذج المستخدم (إضافة/تعديل)
class UserFormDialog extends StatefulWidget {
  final User? user; // null للإضافة، User للتعديل

  const UserFormDialog({super.key, this.user});

  @override
  State<UserFormDialog> createState() => _UserFormDialogState();
}

class _UserFormDialogState extends State<UserFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final AdminController _adminController = Get.find<AdminController>();
  
  // متحكمات النص
  late final TextEditingController _nameController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;
  late final TextEditingController _confirmPasswordController;
  
  // القيم المحددة
  Role? _selectedRole;
  Department? _selectedDepartment;
  bool _isActive = true;
  
  final RxBool _isLoading = false.obs;
  final RxBool _obscurePassword = true.obs;
  final RxBool _obscureConfirmPassword = true.obs;

  bool get _isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadFormData();
  }

  /// تهيئة متحكمات النص
  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.user?.name ?? '');
    _firstNameController = TextEditingController(text: widget.user?.firstName ?? '');
    _lastNameController = TextEditingController(text: widget.user?.lastName ?? '');
    _emailController = TextEditingController(text: widget.user?.email ?? '');
    _usernameController = TextEditingController(text: widget.user?.username ?? '');
    _passwordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
    
    if (widget.user != null) {
      _isActive = widget.user!.isActive;
    }
  }

  /// تحميل بيانات النموذج
  Future<void> _loadFormData() async {
    try {
      await _adminController.loadRoles();
      await _adminController.loadDepartments();
      
      if (widget.user != null) {
        // البحث عن الدور والقسم المحددين
        _selectedRole = _adminController.roles.firstWhereOrNull(
          (role) => role.id == widget.user!.roleId,
        );
        _selectedDepartment = _adminController.departments.firstWhereOrNull(
          (dept) => dept.id == widget.user!.departmentId,
        );
      }
      
      setState(() {});
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: 'فشل في تحميل بيانات النموذج: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الحوار
            _buildDialogHeader(),
            
            // محتوى النموذج
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: _buildForm(),
              ),
            ),
            
            // أزرار الحوار
            _buildDialogActions(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildDialogHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isEditing ? Icons.edit : Icons.person_add,
            color: Colors.white,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// بناء النموذج
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // الاسم الكامل
          AdminTextField(
            label: 'الاسم الكامل',
            controller: _nameController,
            isRequired: true,
            prefixIcon: Icons.person,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'الاسم الكامل مطلوب';
              }
              if (value.trim().length < 2) {
                return 'الاسم يجب أن يكون أكثر من حرفين';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // الاسم الأول والأخير
          Row(
            children: [
              Expanded(
                child: AdminTextField(
                  label: 'الاسم الأول',
                  controller: _firstNameController,
                  prefixIcon: Icons.person_outline,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdminTextField(
                  label: 'الاسم الأخير',
                  controller: _lastNameController,
                  prefixIcon: Icons.person_outline,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // البريد الإلكتروني
          AdminTextField(
            label: 'البريد الإلكتروني',
            controller: _emailController,
            isRequired: true,
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'البريد الإلكتروني مطلوب';
              }
              if (!GetUtils.isEmail(value.trim())) {
                return 'البريد الإلكتروني غير صحيح';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // اسم المستخدم
          AdminTextField(
            label: 'اسم المستخدم',
            controller: _usernameController,
            prefixIcon: Icons.account_circle,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                if (value.trim().length < 3) {
                  return 'اسم المستخدم يجب أن يكون أكثر من 3 أحرف';
                }
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // كلمة المرور (للإضافة أو التعديل الاختياري)
          if (!_isEditing) ...[
            Obx(() => AdminTextField(
              label: 'كلمة المرور',
              controller: _passwordController,
              isRequired: true,
              obscureText: _obscurePassword.value,
              prefixIcon: Icons.lock,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword.value ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () => _obscurePassword.toggle(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'كلمة المرور مطلوبة';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب أن تكون أكثر من 6 أحرف';
                }
                return null;
              },
            )),
            
            const SizedBox(height: 16),
            
            Obx(() => AdminTextField(
              label: 'تأكيد كلمة المرور',
              controller: _confirmPasswordController,
              isRequired: true,
              obscureText: _obscureConfirmPassword.value,
              prefixIcon: Icons.lock_outline,
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword.value ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () => _obscureConfirmPassword.toggle(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'تأكيد كلمة المرور مطلوب';
                }
                if (value != _passwordController.text) {
                  return 'كلمة المرور غير متطابقة';
                }
                return null;
              },
            )),
            
            const SizedBox(height: 16),
          ],
          
          // الدور
          AdminDropdownField<Role>(
            label: 'الدور',
            value: _selectedRole,
            isRequired: true,
            prefixIcon: Icons.security,
            items: _adminController.roles.map((role) {
              return DropdownMenuItem<Role>(
                value: role,
                child: Text(role.displayName),
              );
            }).toList(),
            onChanged: (role) => setState(() => _selectedRole = role),
            validator: (value) {
              if (value == null) {
                return 'يجب اختيار دور للمستخدم';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // القسم
          AdminDropdownField<Department>(
            label: 'القسم',
            value: _selectedDepartment,
            prefixIcon: Icons.business,
            items: _adminController.departments.map((dept) {
              return DropdownMenuItem<Department>(
                value: dept,
                child: Text(dept.name),
              );
            }).toList(),
            onChanged: (dept) => setState(() => _selectedDepartment = dept),
          ),
          
          const SizedBox(height: 16),
          
          // حالة التفعيل
          AdminSwitchField(
            label: 'المستخدم نشط',
            subtitle: 'تحديد ما إذا كان المستخدم يمكنه تسجيل الدخول',
            value: _isActive,
            onChanged: (value) => setState(() => _isActive = value),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الحوار
  Widget _buildDialogActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          const SizedBox(width: 16),
          Obx(() => ElevatedButton(
            onPressed: _isLoading.value ? null : _saveUser,
            child: _isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_isEditing ? 'تحديث' : 'إضافة'),
          )),
        ],
      ),
    );
  }

  /// حفظ المستخدم
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _isLoading.value = true;
    
    try {
      // TODO: استدعاء API لحفظ المستخدم
      await Future.delayed(const Duration(seconds: 2)); // محاكاة
      
      AdminMessageDialog.showSuccess(
        title: 'نجح العملية',
        message: _isEditing 
          ? 'تم تحديث المستخدم بنجاح'
          : 'تم إضافة المستخدم بنجاح',
      );
      
      Get.back(result: true);
    } catch (e) {
      AdminMessageDialog.showError(
        title: 'خطأ',
        message: _isEditing 
          ? 'فشل في تحديث المستخدم: $e'
          : 'فشل في إضافة المستخدم: $e',
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
}
