import 'package:flutter/material.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../utils/image_helper.dart';

/// حوار اختيار المستخدمين المحسن
/// يدعم الاختيار المتعدد والبحث والتصفية حسب القسم
class UserSelectionDialog extends StatefulWidget {
  final List<User> users;
  final List<Department> departments;
  final bool multiSelect;
  final List<String>? selectedUserIds;
  final List<String>? recentUserIds;
  final List<String>? favoriteUserIds;
  final String title;
  final String? subtitle;
  final Function(List<String>) onUsersSelected;

  const UserSelectionDialog({
    super.key,
    required this.users,
    required this.departments,
    this.multiSelect = false,
    this.selectedUserIds,
    this.recentUserIds,
    this.favoriteUserIds,
    this.title = 'اختيار المستخدمين',
    this.subtitle,
    required this.onUsersSelected,
  });

  @override
  State<UserSelectionDialog> createState() => _UserSelectionDialogState();
}

class _UserSelectionDialogState extends State<UserSelectionDialog> {
  late TextEditingController _searchController;
  late Set<String> _selectedUserIds;
  String? _selectedDepartmentId;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _selectedUserIds = Set<String>.from(widget.selectedUserIds ?? []);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<User> _getFilteredUsers() {
    return widget.users.where((user) {
      // تصفية حسب الإدارة
      if (_selectedDepartmentId != null && user.departmentId.toString() != _selectedDepartmentId) {
        return false;
      }

      // تصفية حسب النص
      if (_searchController.text.isNotEmpty) {
        final searchText = _searchController.text.toLowerCase();
        return user.name.toLowerCase().contains(searchText) ||
            user.email.toLowerCase().contains(searchText);
      }

      return true;
    }).toList();
  }

  List<User> _getRecentUsers() {
    if (widget.recentUserIds == null) return [];
    
    final recentUsers = <User>[];
    for (final userId in widget.recentUserIds!) {
      final user = widget.users.firstWhere(
        (u) => u.id.toString() == userId,
        orElse: () => User(
          id: 0,
          name: '',
          email: '',
          roleId: UserRole.user.value,
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ),
      );

      if (user.name.isNotEmpty) {
        recentUsers.add(user);
      }
    }
    return recentUsers;
  }

  List<User> _getFavoriteUsers() {
    if (widget.favoriteUserIds == null) return [];
    
    final favoriteUsers = <User>[];
    for (final userId in widget.favoriteUserIds!) {
      final user = widget.users.firstWhere(
        (u) => u.id.toString() == userId,
        orElse: () => User(
          id: 0,
          name: '',
          email: '',
          roleId: UserRole.user.value,
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ),
      );

      if (user.name.isNotEmpty) {
        favoriteUsers.add(user);
      }
    }
    return favoriteUsers;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: AppStyles.headingMedium,
                      ),
                      if (widget.subtitle != null)
                        Text(
                          widget.subtitle!,
                          style: AppStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط البحث والتصفية
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: AppStyles.inputDecoration(
                      labelText: 'البحث عن مستخدم',
                      hintText: 'اكتب اسم المستخدم أو البريد الإلكتروني',
                      prefixIcon: const Icon(Icons.search),
                    ),
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: _selectedDepartmentId,
                    decoration: AppStyles.inputDecoration(
                      labelText: 'القسم',
                    ),
                    onChanged: (value) {
                      setState(() {
                        _selectedDepartmentId = value;
                      });
                    },
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('جميع الأقسام'),
                      ),
                      ...widget.departments.map((department) => DropdownMenuItem<String?>(
                        value: department.id.toString(),
                        child: Text(department.name),
                      )),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // التبويبات
            if (widget.recentUserIds != null || widget.favoriteUserIds != null)
              DefaultTabController(
                length: 3,
                child: Column(
                  children: [
                    TabBar(
                      onTap: (index) => setState(() => _currentTabIndex = index),
                      tabs: const [
                        Tab(text: 'جميع المستخدمين'),
                        Tab(text: 'المستخدمون الحديثون'),
                        Tab(text: 'المفضلون'),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),

            // قائمة المستخدمين
            Expanded(
              child: _buildUsersList(),
            ),

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _selectedUserIds.isNotEmpty
                      ? () {
                          widget.onUsersSelected(_selectedUserIds.toList());
                          Navigator.of(context).pop();
                        }
                      : null,
                  style: AppStyles.primaryButtonStyle,
                  child: Text(
                    widget.multiSelect
                        ? 'اختيار (${_selectedUserIds.length})'
                        : 'اختيار',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    List<User> users;
    
    switch (_currentTabIndex) {
      case 1:
        users = _getRecentUsers();
        break;
      case 2:
        users = _getFavoriteUsers();
        break;
      default:
        users = _getFilteredUsers();
    }

    if (users.isEmpty) {
      return const Center(
        child: Text('لا توجد مستخدمون'),
      );
    }

    return ListView.builder(
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        final isSelected = _selectedUserIds.contains(user.id.toString());

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: ImageHelper.buildProfileImage(
              imagePath: user.profileImage,
              radius: 20,
              fallbackText: user.name,
              backgroundColor: AppColors.primary.withAlpha(51),
              textColor: AppColors.primary,
            ),
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                if (user.departmentId != null)
                  FutureBuilder<String>(
                    future: _getDepartmentName(user.departmentId.toString()),
                    builder: (context, snapshot) {
                      if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                        return Text(
                          snapshot.data!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
              ],
            ),
            trailing: widget.multiSelect
                ? Checkbox(
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _selectedUserIds.add(user.id.toString());
                        } else {
                          _selectedUserIds.remove(user.id.toString());
                        }
                      });
                    },
                  )
                : null,
            onTap: () {
              if (widget.multiSelect) {
                setState(() {
                  if (isSelected) {
                    _selectedUserIds.remove(user.id.toString());
                  } else {
                    _selectedUserIds.add(user.id.toString());
                  }
                });
              } else {
                widget.onUsersSelected([user.id.toString()]);
                Navigator.of(context).pop();
              }
            },
          ),
        );
      },
    );
  }

  // الحصول على اسم القسم من معرفه
  Future<String> _getDepartmentName(String departmentId) async {
    final department = widget.departments.firstWhere(
      (d) => d.id.toString() == departmentId,
      orElse: () => Department(id: 0, name: '', description: '', createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000),
    );

    return department.name;
  }
}

/// دالة مساعدة لعرض حوار اختيار مستخدم واحد
Future<String?> showSingleUserSelectionDialog({
  required BuildContext context,
  required List<User> users,
  required List<Department> departments,
  String title = 'اختيار مستخدم',
  String? subtitle,
  List<String>? recentUserIds,
  List<String>? favoriteUserIds,
}) async {
  String? selectedUserId;

  await showDialog<String>(
    context: context,
    builder: (BuildContext context) {
      return UserSelectionDialog(
        users: users,
        departments: departments,
        multiSelect: false,
        title: title,
        subtitle: subtitle,
        recentUserIds: recentUserIds,
        favoriteUserIds: favoriteUserIds,
        onUsersSelected: (List<String> userIds) {
          if (userIds.isNotEmpty) {
            selectedUserId = userIds.first;
          }
        },
      );
    },
  );

  return selectedUserId;
}

/// دالة مساعدة لعرض حوار اختيار متعدد المستخدمين
Future<List<String>?> showMultiUserSelectionDialog({
  required BuildContext context,
  required List<User> users,
  required List<Department> departments,
  String title = 'اختيار المستخدمين',
  String? subtitle,
  List<String>? selectedUserIds,
  List<String>? recentUserIds,
  List<String>? favoriteUserIds,
}) async {
  List<String>? selectedUsers;

  await showDialog<List<String>>(
    context: context,
    builder: (BuildContext context) {
      return UserSelectionDialog(
        users: users,
        departments: departments,
        multiSelect: true,
        title: title,
        subtitle: subtitle,
        selectedUserIds: selectedUserIds,
        recentUserIds: recentUserIds,
        favoriteUserIds: favoriteUserIds,
        onUsersSelected: (List<String> userIds) {
          selectedUsers = userIds;
        },
      );
    },
  );

  return selectedUsers;
}
