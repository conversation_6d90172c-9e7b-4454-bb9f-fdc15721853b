
import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/permission_models.dart';
import 'package:flutter_application_2/services/api/new_permissions_api_service.dart';

import 'package:get/get.dart';


/// متحكم الصلاحيات الجديد - يدعم الصلاحيات الـ 71
class NewPermissionsController extends GetxController {
  final NewPermissionsApiService _apiService = NewPermissionsApiService();

  // ===== البيانات الأساسية =====
  
  // قوائم الصلاحيات
  final RxList<Permission> _allPermissions = <Permission>[].obs;
  final RxList<Permission> _filteredPermissions = <Permission>[].obs;
  final RxList<Permission> _userPermissions = <Permission>[].obs;
  final RxList<Permission> _customRolePermissions = <Permission>[].obs;
  
  // الصلاحية الحالية
  final Rx<Permission?> _currentPermission = Rx<Permission?>(null);
  
  // مجموعات الصلاحيات
  final RxList<String> _permissionGroups = <String>[].obs;
  final RxMap<String, List<Permission>> _permissionsByGroup = <String, List<Permission>>{}.obs;
  
  // ===== حالة التطبيق =====
  
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingPermissions = false.obs;
  final RxBool _isLoadingGroups = false.obs;
  final RxString _error = ''.obs;
  final RxString _successMessage = ''.obs;
  
  // ===== المرشحات والبحث =====
  
  final RxString _searchQuery = ''.obs;
  final Rx<String?> _selectedGroup = Rx<String?>(null);
  final Rx<int?> _selectedLevel = Rx<int?>(null);
  final RxBool _showDefaultOnly = false.obs;
  final RxBool _showActiveOnly = true.obs;
  
  // ===== الإحصائيات =====
  
  final RxMap<String, dynamic> _permissionsStats = <String, dynamic>{}.obs;
  
  // ===== Getters =====
  
  List<Permission> get allPermissions => _allPermissions;
  List<Permission> get filteredPermissions => _filteredPermissions;
  List<Permission> get userPermissions => _userPermissions;
  List<Permission> get customRolePermissions => _customRolePermissions;
  Permission? get currentPermission => _currentPermission.value;
  
  List<String> get permissionGroups => _permissionGroups;
  Map<String, List<Permission>> get permissionsByGroup => _permissionsByGroup;
  
  bool get isLoading => _isLoading.value;
  bool get isLoadingPermissions => _isLoadingPermissions.value;
  bool get isLoadingGroups => _isLoadingGroups.value;
  String get error => _error.value;
  String get successMessage => _successMessage.value;
  
  String get searchQuery => _searchQuery.value;
  String? get selectedGroup => _selectedGroup.value;
  int? get selectedLevel => _selectedLevel.value;
  bool get showDefaultOnly => _showDefaultOnly.value;
  bool get showActiveOnly => _showActiveOnly.value;
  
  Map<String, dynamic> get permissionsStats => _permissionsStats;
  
  // ===== دورة الحياة =====
  
  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }
  
  /// تهيئة المتحكم
  Future<void> _initializeController() async {
    await Future.wait([
      loadAllPermissions(),
      loadPermissionGroups(),
      loadPermissionsStats(),
    ]);
  }
  
  // ===== تحميل البيانات =====
  
  /// تحميل جميع الصلاحيات الـ 71
  Future<void> loadAllPermissions() async {
    _isLoadingPermissions.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 تحميل جميع الصلاحيات الـ 71...');
      final permissions = await _apiService.getAllPermissions();
      _allPermissions.assignAll(permissions);
      _groupPermissionsByCategory();
      _applyFilters();
      
      _successMessage.value = 'تم تحميل ${permissions.length} صلاحية بنجاح';
      debugPrint('✅ تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحيات: $e';
      debugPrint('❌ خطأ في تحميل الصلاحيات: $e');
    } finally {
      _isLoadingPermissions.value = false;
    }
  }
  
  /// تحميل مجموعات الصلاحيات
  Future<void> loadPermissionGroups() async {
    _isLoadingGroups.value = true;
    
    try {
      debugPrint('🔄 تحميل مجموعات الصلاحيات...');
      final groups = await _apiService.getPermissionGroups();
      _permissionGroups.assignAll(groups);
      debugPrint('✅ تم تحميل ${groups.length} مجموعة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مجموعات الصلاحيات: $e');
    } finally {
      _isLoadingGroups.value = false;
    }
  }
  
  /// تحميل إحصائيات الصلاحيات
  Future<void> loadPermissionsStats() async {
    try {
      debugPrint('📊 تحميل إحصائيات الصلاحيات...');
      final stats = await _apiService.getPermissionsStats();
      _permissionsStats.assignAll(stats);
      debugPrint('✅ تم تحميل الإحصائيات');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإحصائيات: $e');
    }
  }
  
  // ===== إدارة الصلاحيات =====
  
  /// الحصول على صلاحية بالمعرف
  Future<void> getPermissionById(int id, {bool includeRelations = true}) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 جلب الصلاحية $id...');
      final permission = await _apiService.getPermissionById(id, includeRelations: includeRelations);
      _currentPermission.value = permission;
      
      if (permission != null) {
        _successMessage.value = 'تم تحميل الصلاحية: ${permission.name}';
        debugPrint('✅ تم تحميل الصلاحية: ${permission.name}');
      } else {
        _error.value = 'لم يتم العثور على الصلاحية';
      }
    } catch (e) {
      _error.value = 'خطأ في تحميل الصلاحية: $e';
      debugPrint('❌ خطأ في تحميل الصلاحية: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// إنشاء صلاحية جديدة
  Future<bool> createPermission(Permission permission) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 إنشاء صلاحية جديدة: ${permission.name}');
      final newPermission = await _apiService.createPermission(permission);
      _allPermissions.add(newPermission);
      _groupPermissionsByCategory();
      _applyFilters();
      
      _successMessage.value = 'تم إنشاء الصلاحية: ${newPermission.name}';
      debugPrint('✅ تم إنشاء الصلاحية: ${newPermission.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في إنشاء الصلاحية: $e';
      debugPrint('❌ خطأ في إنشاء الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// تحديث صلاحية
  Future<bool> updatePermission(int id, Permission permission) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 تحديث الصلاحية $id: ${permission.name}');
      final updatedPermission = await _apiService.updatePermission(id, permission);
      
      final index = _allPermissions.indexWhere((p) => p.id == id);
      if (index != -1) {
        _allPermissions[index] = updatedPermission;
        _groupPermissionsByCategory();
        _applyFilters();
      }
      
      _successMessage.value = 'تم تحديث الصلاحية: ${updatedPermission.name}';
      debugPrint('✅ تم تحديث الصلاحية: ${updatedPermission.name}');
      return true;
    } catch (e) {
      _error.value = 'خطأ في تحديث الصلاحية: $e';
      debugPrint('❌ خطأ في تحديث الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// حذف صلاحية
  Future<bool> deletePermission(int id) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 حذف الصلاحية $id');
      final success = await _apiService.deletePermission(id);
      
      if (success) {
        _allPermissions.removeWhere((p) => p.id == id);
        _groupPermissionsByCategory();
        _applyFilters();
        _successMessage.value = 'تم حذف الصلاحية بنجاح';
        debugPrint('✅ تم حذف الصلاحية $id');
      } else {
        _error.value = 'فشل في حذف الصلاحية';
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في حذف الصلاحية: $e';
      debugPrint('❌ خطأ في حذف الصلاحية: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  // ===== التحقق من الصلاحيات =====
  
  /// التحقق من صلاحية مستخدم
  Future<bool> checkUserPermission(int userId, String permissionName) async {
    try {
      return await _apiService.checkUserPermission(userId, permissionName);
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }
  
  /// التحقق من صلاحية المستخدم الحالي
  Future<bool> checkCurrentUserPermission(String permissionName) async {
    try {
      return await _apiService.checkCurrentUserPermission(permissionName);
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية: $e');
      return false;
    }
  }
  
  /// التحقق من صلاحيات متعددة
  Future<Map<String, bool>> checkMultiplePermissions(int userId, List<String> permissionNames) async {
    try {
      return await _apiService.checkMultiplePermissions(userId, permissionNames);
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
      return {};
    }
  }
  
  // ===== إدارة صلاحيات المستخدمين =====
  
  /// تحميل صلاحيات مستخدم
  Future<void> loadUserPermissions(int userId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 تحميل صلاحيات المستخدم $userId');
      final permissions = await _apiService.getUserPermissions(userId);
      _userPermissions.assignAll(permissions);
      
      _successMessage.value = 'تم تحميل ${permissions.length} صلاحية للمستخدم';
      debugPrint('✅ تم تحميل ${permissions.length} صلاحية للمستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات المستخدم: $e';
      debugPrint('❌ خطأ في تحميل صلاحيات المستخدم: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// منح صلاحية لمستخدم
  Future<bool> grantPermissionToUser(int userId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      final success = await _apiService.grantPermissionToUser(userId, permissionId);
      
      if (success) {
        _successMessage.value = 'تم منح الصلاحية للمستخدم بنجاح';
        // إعادة تحميل صلاحيات المستخدم
        await loadUserPermissions(userId);
      } else {
        _error.value = 'فشل في منح الصلاحية للمستخدم';
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحية: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// إلغاء صلاحية من مستخدم
  Future<bool> revokePermissionFromUser(int userId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      final success = await _apiService.revokePermissionFromUser(userId, permissionId);
      
      if (success) {
        _successMessage.value = 'تم إلغاء الصلاحية من المستخدم بنجاح';
        // إعادة تحميل صلاحيات المستخدم
        await loadUserPermissions(userId);
      } else {
        _error.value = 'فشل في إلغاء الصلاحية من المستخدم';
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في إلغاء الصلاحية: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  // ===== إدارة صلاحيات الأدوار المخصصة =====
  
  /// تحميل صلاحيات دور مخصص
  Future<void> loadCustomRolePermissions(int customRoleId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      debugPrint('🔄 تحميل صلاحيات الدور المخصص $customRoleId');
      final permissions = await _apiService.getCustomRolePermissions(customRoleId);
      _customRolePermissions.assignAll(permissions);
      
      _successMessage.value = 'تم تحميل ${permissions.length} صلاحية للدور المخصص';
      debugPrint('✅ تم تحميل ${permissions.length} صلاحية للدور المخصص');
    } catch (e) {
      _error.value = 'خطأ في تحميل صلاحيات الدور المخصص: $e';
      debugPrint('❌ خطأ في تحميل صلاحيات الدور المخصص: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// منح صلاحية لدور مخصص
  Future<bool> grantPermissionToCustomRole(int customRoleId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      final success = await _apiService.grantPermissionToCustomRole(customRoleId, permissionId);
      
      if (success) {
        _successMessage.value = 'تم منح الصلاحية للدور المخصص بنجاح';
        // إعادة تحميل صلاحيات الدور المخصص
        await loadCustomRolePermissions(customRoleId);
      } else {
        _error.value = 'فشل في منح الصلاحية للدور المخصص';
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في منح الصلاحية للدور المخصص: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// إلغاء صلاحية من دور مخصص
  Future<bool> revokePermissionFromCustomRole(int customRoleId, int permissionId) async {
    _isLoading.value = true;
    _error.value = '';
    
    try {
      final success = await _apiService.revokePermissionFromCustomRole(customRoleId, permissionId);
      
      if (success) {
        _successMessage.value = 'تم إلغاء الصلاحية من الدور المخصص بنجاح';
        // إعادة تحميل صلاحيات الدور المخصص
        await loadCustomRolePermissions(customRoleId);
      } else {
        _error.value = 'فشل في إلغاء الصلاحية من الدور المخصص';
      }
      
      return success;
    } catch (e) {
      _error.value = 'خطأ في إلغاء الصلاحية من الدور المخصص: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
  
  // ===== البحث والتصفية =====
  
  /// البحث في الصلاحيات
  Future<void> searchPermissions(String query) async {
    _searchQuery.value = query;
    
    if (query.isEmpty) {
      _applyFilters();
      return;
    }
    
    _isLoading.value = true;
    
    try {
      debugPrint('🔍 البحث في الصلاحيات: $query');
      final results = await _apiService.searchPermissions(
        query,
        group: _selectedGroup.value,
        level: _selectedLevel.value,
        isDefault: _showDefaultOnly.value ? true : null,
      );
      
      _filteredPermissions.assignAll(results);
      debugPrint('✅ تم العثور على ${results.length} صلاحية');
    } catch (e) {
      _error.value = 'خطأ في البحث: $e';
      debugPrint('❌ خطأ في البحث: $e');
    } finally {
      _isLoading.value = false;
    }
  }
  
  /// تطبيق المرشحات
  void _applyFilters() {
    var filtered = _allPermissions.toList();
    
    // تطبيق مرشح البحث
    if (_searchQuery.value.isNotEmpty) {
      filtered = filtered.where((permission) =>
        permission.name.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
        (permission.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }
    
    // تطبيق مرشح المجموعة
    if (_selectedGroup.value != null) {
      filtered = filtered.where((permission) =>
        permission.permissionGroup == _selectedGroup.value
      ).toList();
    }
    
    // تطبيق مرشح المستوى
    if (_selectedLevel.value != null) {
      filtered = filtered.where((permission) =>
        permission.level == _selectedLevel.value
      ).toList();
    }
    
    // تطبيق مرشح الافتراضية
    if (_showDefaultOnly.value) {
      filtered = filtered.where((permission) => permission.isDefault).toList();
    }
    
    _filteredPermissions.assignAll(filtered);
  }
  
  /// تجميع الصلاحيات حسب المجموعة
  void _groupPermissionsByCategory() {
    final grouped = <String, List<Permission>>{};
    
    for (final permission in _allPermissions) {
      final group = permission.permissionGroup;
      if (!grouped.containsKey(group)) {
        grouped[group] = [];
      }
      grouped[group]!.add(permission);
    }
    
    _permissionsByGroup.assignAll(grouped);
  }
  
  // ===== إعدادات المرشحات =====
  
  /// تعيين مرشح المجموعة
  void setGroupFilter(String? group) {
    _selectedGroup.value = group;
    _applyFilters();
  }
  
  /// تعيين مرشح المستوى
  void setLevelFilter(int? level) {
    _selectedLevel.value = level;
    _applyFilters();
  }
  
  /// تبديل مرشح الافتراضية
  void toggleDefaultFilter() {
    _showDefaultOnly.value = !_showDefaultOnly.value;
    _applyFilters();
  }
  
  /// تبديل مرشح النشطة
  void toggleActiveFilter() {
    _showActiveOnly.value = !_showActiveOnly.value;
    _applyFilters();
  }
  
  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _selectedGroup.value = null;
    _selectedLevel.value = null;
    _showDefaultOnly.value = false;
    _showActiveOnly.value = true;
    _applyFilters();
  }
  
  // ===== الأدوات المساعدة =====
  
  /// مسح الرسائل
  void clearMessages() {
    _error.value = '';
    _successMessage.value = '';
  }
  
  /// تصدير الصلاحيات
  Future<Map<String, dynamic>> exportPermissions({String format = 'json'}) async {
    try {
      debugPrint('📤 تصدير الصلاحيات...');
      final data = await _apiService.exportPermissions(format: format);
      _successMessage.value = 'تم تصدير الصلاحيات بنجاح';
      return data;
    } catch (e) {
      _error.value = 'خطأ في تصدير الصلاحيات: $e';
      return {};
    }
  }
  
  /// إعادة تحميل البيانات
  @override
  Future<void> refresh() async {
    await _initializeController();
  }
  
  /// مسح التخزين المؤقت
  Future<bool> clearCache() async {
    try {
      final success = await _apiService.clearPermissionsCache();
      if (success) {
        _successMessage.value = 'تم مسح التخزين المؤقت بنجاح';
        await refresh();
      } else {
        _error.value = 'فشل في مسح التخزين المؤقت';
      }
      return success;
    } catch (e) {
      _error.value = 'خطأ في مسح التخزين المؤقت: $e';
      return false;
    }
  }
}