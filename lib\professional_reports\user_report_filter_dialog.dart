import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/department_model.dart';

/// فلتر مخصص لاختيار مستخدمين لتقرير المستخدمين الشامل
/// لا يؤثر على أي مكان آخر في النظام
class UserReportFilterDialog extends StatefulWidget {
  final List<User> users;
  final List<Department> departments;
  final List<String>? initialSelectedUserIds;
  final Function(List<String>) onUsersSelected;

  const UserReportFilterDialog({
    super.key,
    required this.users,
    required this.departments,
    this.initialSelectedUserIds,
    required this.onUsersSelected,
  });

  @override
  State<UserReportFilterDialog> createState() => _UserReportFilterDialogState();
}

class _UserReportFilterDialogState extends State<UserReportFilterDialog> {
  late TextEditingController _searchController;
  late Set<String> _selectedUserIds;
  String? _selectedDepartmentId;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _selectedUserIds = Set<String>.from(widget.initialSelectedUserIds ?? []);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<User> _getFilteredUsers() {
    return widget.users.where((user) {
      if (_selectedDepartmentId != null && user.departmentId.toString() != _selectedDepartmentId) {
        return false;
      }
      if (_searchController.text.isNotEmpty) {
        final searchText = _searchController.text.toLowerCase();
        return user.name.toLowerCase().contains(searchText) ||
            user.email.toLowerCase().contains(searchText);
      }
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final filteredUsers = _getFilteredUsers();
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('فلترة المستخدمين للتقرير', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'بحث بالاسم أو البريد',
                      prefixIcon: Icon(Icons.search),
                    ),
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: _selectedDepartmentId,
                    decoration: const InputDecoration(labelText: 'القسم'),
                    onChanged: (value) {
                      setState(() {
                        _selectedDepartmentId = value;
                      });
                    },
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('جميع الأقسام'),
                      ),
                      ...widget.departments.map((department) => DropdownMenuItem<String?>(
                        value: department.id.toString(),
                        child: Text(department.name),
                      )),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: filteredUsers.isEmpty
                  ? const Center(child: Text('لا يوجد مستخدمون'))
                  : ListView.builder(
                      itemCount: filteredUsers.length,
                      itemBuilder: (context, index) {
                        final user = filteredUsers[index];
                        final isSelected = _selectedUserIds.contains(user.id.toString());
                        return CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedUserIds.add(user.id.toString());
                              } else {
                                _selectedUserIds.remove(user.id.toString());
                              }
                            });
                          },
                          title: Text(user.name),
                          subtitle: Text(user.email),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    widget.onUsersSelected(_selectedUserIds.toList());
                    Navigator.of(context).pop();
                  },
                  child: const Text('تأكيد الاختيار'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// دالة مساعدة لعرض فلتر المستخدمين للتقرير
Future<List<String>?> showUserReportFilterDialog({
  required BuildContext context,
  required List<User> users,
  required List<Department> departments,
  List<String>? initialSelectedUserIds,
}) async {
  List<String>? selectedUserIds;
  await showDialog(
    context: context,
    builder: (context) => UserReportFilterDialog(
      users: users,
      departments: departments,
      initialSelectedUserIds: initialSelectedUserIds,
      onUsersSelected: (ids) => selectedUserIds = ids,
    ),
  );
  return selectedUserIds;
}
