import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

import '../reports/user_performance_comparison_report.dart';
import '../../services/api/task_api_service.dart';
import '../../services/api/user_api_service.dart';
import '../../services/api/departments_api_service.dart';
import '../../models/user_model.dart';
import '../../models/department_model.dart';

// فئة بيانات لحالة المهام
class TaskStatusData {
  final String status;
  final int count;
  final Color color;

  TaskStatusData({
    required this.status,
    required this.count,
    required this.color,
  });
}

class UserPerformanceComparisonReportScreen extends StatefulWidget {
  const UserPerformanceComparisonReportScreen({super.key});

  @override
  State<UserPerformanceComparisonReportScreen> createState() =>
      _UserPerformanceComparisonReportScreenState();
}

class _UserPerformanceComparisonReportScreenState
    extends State<UserPerformanceComparisonReportScreen> {
  final TaskApiService _taskApiService = TaskApiService();
  final UserApiService _userApiService = UserApiService();
  final DepartmentsApiService _departmentsApiService = DepartmentsApiService();

  late Future<UserPerformanceComparisonReport> _reportFuture;
  List<User> _allUsers = [];
  List<Department> _allDepartments = [];

  Set<int> _selectedUserIds = {};
  Set<int> _selectedDepartmentIds = {};
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() async {
    try {
      _allUsers = await _userApiService.getAllUsers();
      _allDepartments = await _departmentsApiService.getAllDepartments();
      _reportFuture = _getReport();
      setState(() {});
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الأولية: $e');
      Get.snackbar(
        'خطأ',
        'فشل في تحميل البيانات الأولية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<UserPerformanceComparisonReport> _getReport() async {
    try {
      final tasks = await _taskApiService.getAllTasks();
      
      // Apply department filter to users
      Set<int> userIdsToFilter = _selectedUserIds.toSet();
      if (_selectedDepartmentIds.isNotEmpty) {
        try {
          final usersInDepartments = await _userApiService.getUsersByDepartmentIds(_selectedDepartmentIds.toList());
          userIdsToFilter.addAll(usersInDepartments.map((u) => u.id));
        } catch (e) {
          debugPrint('تحذير: فشل في الحصول على مستخدمي الأقسام: $e');
          // في حالة فشل API، نطبق التصفية محلياً
          final usersInDepartments = _allUsers.where((user) => 
            user.departmentId != null && _selectedDepartmentIds.contains(user.departmentId!)).toList();
          userIdsToFilter.addAll(usersInDepartments.map((u) => u.id));
        }
      }

      return UserPerformanceComparisonReport.fromTasks(
        tasks,
        _allUsers,
        userIds: userIdsToFilter.isEmpty ? null : userIdsToFilter,
        startDate: _startDate,
        endDate: _endDate,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء التقرير: $e');
      rethrow;
    }
  }

  void _refreshReport() {
    setState(() {
      _reportFuture = _getReport();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير أداء المستخدمين المقارن'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshReport,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: FutureBuilder<UserPerformanceComparisonReport>(
              future: _reportFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                } else if (snapshot.hasData) {
                  final report = snapshot.data!;
                  if (report.userPerformances.isEmpty) {
                    return const Center(child: Text('لا توجد بيانات لعرضها.'));
                  }
                  return _buildReportContent(report);
                } else {
                  return const Center(child: Text('No data'));
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        alignment: WrapAlignment.center,
        children: [
          _buildMultiSelectChip(
            label: 'المستخدمين',
            allOptions: _allUsers.map((u) => DropdownMenuItem(value: u.id, child: Text(u.name))).toList(),
            selectedValues: _selectedUserIds,
            onChanged: (values) {
              setState(() {
                _selectedUserIds = values;
              });
            },
          ),
          _buildMultiSelectChip(
            label: 'الأقسام',
            allOptions: _allDepartments.map((d) => DropdownMenuItem(value: d.id, child: Text(d.name))).toList(),
            selectedValues: _selectedDepartmentIds,
            onChanged: (values) {
              setState(() {
                _selectedDepartmentIds = values;
              });
            },
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.date_range),
            label: Text(_startDate == null || _endDate == null
                ? 'تحديد التاريخ'
                : '${DateFormat.yMd().format(_startDate!)} - ${DateFormat.yMd().format(_endDate!)}'),
            onPressed: _pickDateRange,
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.clear),
            label: const Text('مسح الفلاتر'),
            onPressed: () {
              setState(() {
                _selectedUserIds.clear();
                _selectedDepartmentIds.clear();
                _startDate = null;
                _endDate = null;
                _refreshReport();
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.grey),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.check),
            label: const Text('تطبيق'),
            onPressed: _refreshReport,
            style: ElevatedButton.styleFrom(backgroundColor: Theme.of(context).primaryColor),
          ),
        ],
      ),
    );
  }

  Future<void> _pickDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2101),
      initialDateRange: _startDate != null && _endDate != null
          ? DateTimeRange(start: _startDate!, end: _endDate!)
          : null,
    );
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  Widget _buildMultiSelectChip<T>({
    required String label,
    required List<DropdownMenuItem<T>> allOptions,
    required Set<T> selectedValues,
    required ValueChanged<Set<T>> onChanged,
  }) {
    return ActionChip(
      avatar: const Icon(Icons.arrow_drop_down),
      label: Text('$label (${selectedValues.length})'),
      onPressed: () async {
        final selected = await showDialog<Set<T>>(
          context: context,
          builder: (context) {
            return MultiSelectDialog(
              items: allOptions,
              initialValue: selectedValues,
            );
          },
        );
        if (selected != null) {
          onChanged(selected);
        }
      },
    );
  }

  Widget _buildReportContent(UserPerformanceComparisonReport report) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: const TabBar(
              labelColor: Colors.black87,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(text: 'الرسم البياني', icon: Icon(Icons.bar_chart)),
                Tab(text: 'الجدول التفصيلي', icon: Icon(Icons.table_chart)),
                Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildChartView(report),
                _buildTableView(report),
                _buildStatisticsView(report),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartView(UserPerformanceComparisonReport report) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رسم بياني للمقارنة بين المستخدمين - معدل الإنجاز
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.trending_up, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      const Text(
                        'معدل الإنجاز للمستخدمين',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    height: 300,
                    child: SfCartesianChart(
                      primaryXAxis: CategoryAxis(
                        title: AxisTitle(text: 'المستخدمين'),
                        labelStyle: const TextStyle(fontSize: 10),
                        labelIntersectAction: AxisLabelIntersectAction.rotate45,
                      ),
                      primaryYAxis: NumericAxis(
                        title: AxisTitle(text: 'معدل الإنجاز (%)'),
                        minimum: 0,
                        maximum: 100,
                      ),
                      title: ChartTitle(text: 'مقارنة معدلات الإنجاز بين المستخدمين'),
                      legend: Legend(isVisible: false),
                      tooltipBehavior: TooltipBehavior(enable: true),
                      series: <CartesianSeries<UserPerformance, String>>[
                        ColumnSeries<UserPerformance, String>(
                          dataSource: report.userPerformances,
                          xValueMapper: (UserPerformance performance, _) => 
                            performance.user.name.length > 10 
                                ? '${performance.user.name.substring(0, 10)}...'
                                : performance.user.name,
                          yValueMapper: (UserPerformance performance, _) => performance.completionRate,
                          pointColorMapper: (UserPerformance performance, _) => 
                            _getColorForPerformance(performance.completionRate),
                          dataLabelSettings: const DataLabelSettings(
                            isVisible: true,
                            labelAlignment: ChartDataLabelAlignment.top,
                            textStyle: TextStyle(fontSize: 10),
                          ),
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            topRight: Radius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          
          // رسم بياني دائري لتوزيع المهام
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.pie_chart, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      const Text(
                        'توزيع حالات المهام',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    height: 300,
                    child: SfCircularChart(
                      title: ChartTitle(text: 'توزيع حالات المهام الإجمالي'),
                      legend: Legend(
                        isVisible: true,
                        position: LegendPosition.bottom,
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                      tooltipBehavior: TooltipBehavior(enable: true),
                      series: <CircularSeries>[
                        PieSeries<TaskStatusData, String>(
                          dataSource: _buildTaskStatusData(report),
                          xValueMapper: (TaskStatusData data, _) => data.status,
                          yValueMapper: (TaskStatusData data, _) => data.count,
                          pointColorMapper: (TaskStatusData data, _) => data.color,
                          dataLabelSettings: const DataLabelSettings(
                            isVisible: true,
                            labelPosition: ChartDataLabelPosition.outside,
                            useSeriesColor: true,
                          ),
                          explode: true,
                          explodeIndex: 0,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getColorForPerformance(double completionRate) {
    if (completionRate >= 80) return Colors.green;
    if (completionRate >= 60) return Colors.orange;
    if (completionRate >= 40) return Colors.yellow;
    return Colors.red;
  }

  List<TaskStatusData> _buildTaskStatusData(UserPerformanceComparisonReport report) {
    int totalCompleted = 0;
    int totalInProgress = 0;
    int totalOverdue = 0;
    int totalPending = 0;

    for (var performance in report.userPerformances) {
      totalCompleted += performance.completedTasks;
      totalInProgress += performance.inProgressTasks;
      totalOverdue += performance.overdueTasks;
      totalPending += performance.pendingTasks;
    }

    return [
      TaskStatusData(
        status: 'مكتملة',
        count: totalCompleted,
        color: Colors.green,
      ),
      TaskStatusData(
        status: 'قيد التنفيذ',
        count: totalInProgress,
        color: Colors.blue,
      ),
      TaskStatusData(
        status: 'متأخرة',
        count: totalOverdue,
        color: Colors.orange,
      ),
      TaskStatusData(
        status: 'معلقة',
        count: totalPending,
        color: Colors.grey,
      ),
    ].where((data) => data.count > 0).toList();
  }

  Widget _buildTableView(UserPerformanceComparisonReport report) {
    final columns = [
      PlutoColumn(
        title: 'المستخدم',
        field: 'user',
        type: PlutoColumnType.text(),
        width: 120,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'إجمالي المهام',
        field: 'totalTasks',
        type: PlutoColumnType.number(),
        width: 100,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'المكتملة',
        field: 'completedTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'قيد التنفيذ',
        field: 'inProgressTasks',
        type: PlutoColumnType.number(),
        width: 100,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'متأخرة',
        field: 'overdueTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'معلقة',
        field: 'pendingTasks',
        type: PlutoColumnType.number(),
        width: 80,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'معدل الإنجاز',
        field: 'completionRate',
        type: PlutoColumnType.text(),
        width: 100,
        enableEditingMode: false,
      ),
      PlutoColumn(
        title: 'متوسط وقت الإنجاز (ساعة)',
        field: 'averageCompletionTime',
        type: PlutoColumnType.text(),
        width: 150,
        enableEditingMode: false,
      ),
    ];

    final rows = report.userPerformances.map((performance) {
      return PlutoRow(
        cells: {
          'user': PlutoCell(value: performance.user.name),
          'totalTasks': PlutoCell(value: performance.totalTasks),
          'completedTasks': PlutoCell(value: performance.completedTasks),
          'inProgressTasks': PlutoCell(value: performance.inProgressTasks),
          'overdueTasks': PlutoCell(value: performance.overdueTasks),
          'pendingTasks': PlutoCell(value: performance.pendingTasks),
          'completionRate': PlutoCell(value: '${performance.completionRate.toStringAsFixed(1)}%'),
          'averageCompletionTime': PlutoCell(value: performance.averageCompletionTime.toStringAsFixed(1)),
        },
      );
    }).toList();

    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.table_chart, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'جدول مقارنة أداء المستخدمين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير Excel'),
                  onPressed: () => _exportToExcel(report),
                ),
              ],
            ),
          ),
          Expanded(
            child: PlutoGrid(
              columns: columns,
              rows: rows,
              onLoaded: (PlutoGridOnLoadedEvent event) {
                // يمكن إضافة إعدادات إضافية هنا
              },
              configuration: PlutoGridConfiguration(
                style: PlutoGridStyleConfig(
                  gridBorderColor: Colors.grey.shade300,
                  activatedBorderColor: Theme.of(context).primaryColor,
                  rowColor: Colors.white,
                  checkedColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                ),
                localeText: const PlutoGridLocaleText.arabic(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsView(UserPerformanceComparisonReport report) {
    // حساب الإحصائيات الإجمالية
    final totalUsers = report.userPerformances.length;
    final totalTasks = report.userPerformances.fold(0, (sum, p) => sum + p.totalTasks);
    final totalCompleted = report.userPerformances.fold(0, (sum, p) => sum + p.completedTasks);
    final totalOverdue = report.userPerformances.fold(0, (sum, p) => sum + p.overdueTasks);
    final averageCompletionRate = totalUsers > 0 
        ? report.userPerformances.fold(0.0, (sum, p) => sum + p.completionRate) / totalUsers
        : 0.0;

    // أفضل المؤدين
    final topPerformers = report.userPerformances.take(3).toList();
    
    // المستخدمون الذين يحتاجون لمتابعة
    final needsFollowUp = report.userPerformances
        .where((p) => p.completionRate < 50 || p.overdueTasks > 0)
        .toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة
          const Text(
            'الإحصائيات العامة',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المستخدمين',
                  totalUsers.toString(),
                  Icons.people,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'إجمالي المهام',
                  totalTasks.toString(),
                  Icons.task,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'المهام المكتملة',
                  totalCompleted.toString(),
                  Icons.check_circle,
                  Colors.teal,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'المهام المتأخرة',
                  totalOverdue.toString(),
                  Icons.warning,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            'متوسط معدل الإنجاز',
            '${averageCompletionRate.toStringAsFixed(1)}%',
            Icons.trending_up,
            Colors.purple,
            isFullWidth: true,
          ),
          
          const SizedBox(height: 32),
          
          // أفضل المؤدين
          const Text(
            'أفضل المؤدين',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...topPerformers.asMap().entries.map((entry) {
            final index = entry.key;
            final performance = entry.value;
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getRankColor(index),
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(performance.user.name),
                subtitle: Text(
                  'معدل الإنجاز: ${performance.completionRate.toStringAsFixed(1)}% | '
                  'المهام المكتملة: ${performance.completedTasks}/${performance.totalTasks}',
                ),
                trailing: Icon(
                  Icons.star,
                  color: _getRankColor(index),
                ),
              ),
            );
          }),
          
          const SizedBox(height: 32),
          
          // المستخدمون الذين يحتاجون لمتابعة
          if (needsFollowUp.isNotEmpty) ...[
            const Text(
              'يحتاجون لمتابعة',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...needsFollowUp.map((performance) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                color: Colors.red.shade50,
                child: ListTile(
                  leading: const CircleAvatar(
                    backgroundColor: Colors.red,
                    child: Icon(Icons.warning, color: Colors.white),
                  ),
                  title: Text(performance.user.name),
                  subtitle: Text(
                    'معدل الإنجاز: ${performance.completionRate.toStringAsFixed(1)}% | '
                    'متأخرة: ${performance.overdueTasks}',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    bool isFullWidth = false,
  }) {
    return Card(
      elevation: 4,
      child: Container(
        width: isFullWidth ? double.infinity : null,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getRankColor(int index) {
    switch (index) {
      case 0: return Colors.amber; // ذهبي
      case 1: return Colors.grey; // فضي
      case 2: return Colors.brown; // برونزي
      default: return Colors.blue;
    }
  }

  void _exportToExcel(UserPerformanceComparisonReport report) {
    // يمكن إضافة وظيفة تصدير Excel هنا
    Get.snackbar(
      'تصدير',
      'سيتم إضافة وظيفة تصدير Excel قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// Helper class for multi-select dialog
class MultiSelectDialog<T> extends StatefulWidget {
  final List<DropdownMenuItem<T>> items;
  final Set<T> initialValue;

  const MultiSelectDialog({super.key, required this.items, required this.initialValue});

  @override
  State<MultiSelectDialog<T>> createState() => _MultiSelectDialogState<T>();
}

class _MultiSelectDialogState<T> extends State<MultiSelectDialog<T>> {
  late Set<T> _selectedValues;

  @override
  void initState() {
    super.initState();
    _selectedValues = widget.initialValue.toSet();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('اختر العناصر'),
      content: SingleChildScrollView(
        child: ListBody(
          children: widget.items.map((item) {
            return CheckboxListTile(
              value: _selectedValues.contains(item.value),
              title: item.child,
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (isChecked) {
                setState(() {
                  if (isChecked!) {
                    _selectedValues.add(item.value as T);
                  } else {
                    _selectedValues.remove(item.value);
                  }
                });
              },
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          child: const Text('إلغاء'),
          onPressed: () => Navigator.of(context).pop(),
        ),
        ElevatedButton(
          child: const Text('موافق'),
          onPressed: () => Navigator.of(context).pop(_selectedValues),
        ),
      ],
    );
  }
}
