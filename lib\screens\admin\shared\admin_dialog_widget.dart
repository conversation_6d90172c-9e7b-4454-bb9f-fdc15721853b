import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// حوار تأكيد موحد للعمليات الإدارية
class AdminConfirmDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmColor;
  final IconData? icon;

  const AdminConfirmDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'تأكيد',
    this.cancelText = 'إلغاء',
    this.onConfirm,
    this.onCancel,
    this.confirmColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: confirmColor ?? Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      actions: [
        TextButton(
          onPressed: onCancel ?? () => Get.back(result: false),
          child: Text(cancelText),
        ),
        ElevatedButton(
          onPressed: onConfirm ?? () => Get.back(result: true),
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmColor ?? Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
          ),
          child: Text(confirmText),
        ),
      ],
    );
  }

  /// عرض حوار التأكيد
  static Future<bool> show({
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    Color? confirmColor,
    IconData? icon,
  }) async {
    final result = await Get.dialog<bool>(
      AdminConfirmDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        confirmColor: confirmColor,
        icon: icon,
      ),
    );
    return result ?? false;
  }
}

/// حوار تحميل موحد
class AdminLoadingDialog extends StatelessWidget {
  final String message;

  const AdminLoadingDialog({
    super.key,
    this.message = 'جاري التحميل...',
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: Row(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار التحميل
  static void show({String message = 'جاري التحميل...'}) {
    Get.dialog(
      AdminLoadingDialog(message: message),
      barrierDismissible: false,
    );
  }

  /// إخفاء حوار التحميل
  static void hide() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }
}

/// حوار رسالة موحد
class AdminMessageDialog extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final Color? iconColor;
  final String buttonText;

  const AdminMessageDialog({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.iconColor,
    this.buttonText = 'موافق',
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: iconColor ?? Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
      actions: [
        ElevatedButton(
          onPressed: () => Get.back(),
          child: Text(buttonText),
        ),
      ],
    );
  }

  /// عرض رسالة نجاح
  static void showSuccess({
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    Get.dialog(
      AdminMessageDialog(
        title: title,
        message: message,
        icon: Icons.check_circle,
        iconColor: Colors.green,
        buttonText: buttonText,
      ),
    );
  }

  /// عرض رسالة خطأ
  static void showError({
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    Get.dialog(
      AdminMessageDialog(
        title: title,
        message: message,
        icon: Icons.error,
        iconColor: Colors.red,
        buttonText: buttonText,
      ),
    );
  }

  /// عرض رسالة تحذير
  static void showWarning({
    required String title,
    required String message,
    String buttonText = 'موافق',
  }) {
    Get.dialog(
      AdminMessageDialog(
        title: title,
        message: message,
        icon: Icons.warning,
        iconColor: Colors.orange,
        buttonText: buttonText,
      ),
    );
  }
}
