/// خدمة التقارير المحسنة للمهام
/// 
/// تقوم هذه الخدمة بإنشاء تقارير شاملة ومفصلة للمهام
/// مع استخدام نموذج TaskReportModel المحسن لاستخراج جميع البيانات

import 'package:flutter/services.dart';
import 'package:flutter_application_2/professional_reports/models/report_data_models.dart';
import 'package:flutter_application_2/models/task_models.dart';
import 'package:flutter_application_2/models/task_report_models.dart';
import 'package:flutter_application_2/professional_reports/reporttask_backup.dart' show EnhancedTaskApiService;
import 'package:flutter_application_2/professional_reports/tasks_report_service.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة إنشاء التقارير المحسنة للمهام
class EnhancedTaskReportService {
  final TaskApiServiceReports _taskApiService = TaskApiServiceReports();
  final EnhancedTaskApiService _enhancedApiService = EnhancedTaskApiService();

  /// إنشاء تقرير شامل لجميع المهام
  /// 
  /// [onProgress] - دالة لتتبع تقدم العملية
  /// [filters] - معايير التصفية (اختياري)
  /// [includeStatistics] - تضمين الإحصائيات (افتراضي: true)
  /// [includeCharts] - تضمين الرسوم البيانية (افتراضي: false)
  Future<pw.Document> generateComprehensiveTasksReport({
    Function(String)? onProgress,
    ReportFilters? filters,
    bool includeStatistics = true,
    bool includeCharts = false,
  }) async {
    final pdf = pw.Document();
    
    // تحميل الخطوط العربية
    onProgress?.call('تحميل الخطوط العربية...');
    final fonts = await _loadArabicFonts();
    
    try {
      // جلب المهام من API
      onProgress?.call('جلب بيانات المهام من الخادم...');
      final tasks = await _taskApiService.getAllTasks();
      
      if (tasks.isEmpty) {
        return _createEmptyReport(pdf, fonts, 'لا توجد مهام متاحة لعرضها في التقرير.');
      }
      
      // تطبيق الفلاتر إذا كانت موجودة
      final filteredTasks = _applyFilters(tasks, filters);
      onProgress?.call('تم تصفية ${filteredTasks.length} مهمة من أصل ${tasks.length}');
      
      // تحويل المهام إلى نماذج تقارير
      onProgress?.call('معالجة بيانات المهام...');
      final taskReports = filteredTasks.map((task) => TaskReportData.fromJson(task.toJson())).toList();
      
      // حساب الإحصائيات
      ReportStatistics? statistics;
      if (includeStatistics) {
        onProgress?.call('حساب الإحصائيات...');
        statistics = _calculateStatistics(taskReports);
      }
      
      // إنشاء صفحة الغلاف
      onProgress?.call('إنشاء صفحة الغلاف...');
      _addCoverPage(pdf, fonts, taskReports.length, statistics);
      
      // إضافة الإحصائيات إذا كانت مطلوبة
      if (statistics != null) {
        onProgress?.call('إضافة صفحة الإحصائيات...');
        _addStatisticsPage(pdf, fonts, statistics);
      }
      
      // إضافة جدول المهام الرئيسي
      onProgress?.call('إنشاء جدول المهام...');
      _addTasksTable(pdf, fonts, taskReports);
      
      // إضافة تفاصيل المهام المهمة
      onProgress?.call('إضافة تفاصيل المهام المهمة...');
      _addImportantTasksDetails(pdf, fonts, taskReports);
      
      onProgress?.call('تم إنشاء التقرير بنجاح!');
      return pdf;
      
    } catch (e) {
      return _createErrorReport(pdf, fonts, 'فشل في إنشاء التقرير: $e');
    }
  }

  /// إنشاء تقرير مفصل لمهمة واحدة
  /// 
  /// [task] - المهمة المراد إنشاء تقرير لها
  /// [onProgress] - دالة لتتبع تقدم العملية
  /// [username] - اسم المستخدم الذي طلب التقرير
  /// [includeFullHistory] - تضمين التاريخ الكامل (افتراضي: true)
  Future<pw.Document> generateDetailedTaskReport(
    Task task, {
    Function(String)? onProgress,
    String? username,
    bool includeFullHistory = true,
  }) async {
    final pdf = pw.Document();
    
    // تحميل الخطوط العربية
    onProgress?.call('تحميل الخطوط العربية...');
    final fonts = await _loadArabicFonts();
    
    try {
      // جلب البيانات الشاملة للمهمة من API المحسن
      onProgress?.call('جلب البيانات الشاملة للمهمة من الخادم...');
      final reportData = await _enhancedApiService.getTaskComprehensiveReportData(task.id);
      
      // إنشاء نموذج التقرير الشامل من البيانات المجلبة
      onProgress?.call('إنشاء نموذج التقرير الشامل المحسن...');
      final reportModel = _enhancedApiService.createEnhancedTaskReportModel(reportData);
      
      onProgress?.call('تم استخراج ${reportModel.contributors.length} مساهم و ${reportModel.transfers.length} تحويل');
      
      // إضافة صفحة الغلاف
      onProgress?.call('إنشاء صفحة الغلاف...');
      _addTaskCoverPage(pdf, fonts, task, username);
      
      // إضافة معلومات المهمة الأساسية
      onProgress?.call('إضافة المعلومات الأساسية...');
      _addTaskBasicInfo(pdf, fonts, task, reportModel.statistics);
      
      // إضافة تفاصيل المساهمين
      if (reportModel.contributors.isNotEmpty) {
        onProgress?.call('إضافة تفاصيل المساهمين...');
        _addContributorsSection(pdf, fonts, reportModel.contributors);
      }
      
      // إضافة تفاصيل التحويلات
      if (reportModel.transfers.isNotEmpty) {
        onProgress?.call('إضافة تفاصيل التحويلات...');
        _addTransfersSection(pdf, fonts, reportModel.transfers);
      }
      
      // إضافة التعليقات والمرفقات
      onProgress?.call('إضافة التعليقات والمرفقات...');
      _addCommentsAndAttachments(pdf, fonts, task);
      
      // إضافة المهام الفرعية
      if (task.subtasks.isNotEmpty) {
        onProgress?.call('إضافة المهام الفرعية...');
        _addSubtasksSection(pdf, fonts, task.subtasks);
      }
      
      onProgress?.call('تم إنشاء التقرير المفصل بنجاح!');
      return pdf;
      
    } catch (e) {
      return _createErrorReport(pdf, fonts, 'فشل في إنشاء التقرير المفصل: $e');
    }
  }

  /// تحميل الخطوط العربية
  Future<_ArabicFonts> _loadArabicFonts() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      final boldFontData = await rootBundle.load('assets/fonts/NotoSansArabic-Bold.ttf');
      
      return _ArabicFonts(
        regular: pw.Font.ttf(fontData),
        bold: pw.Font.ttf(boldFontData),
        fallbacks: [pw.Font.ttf(fontData), pw.Font.ttf(boldFontData)],
      );
    } catch (e) {
      // استخدام خطوط افتراضية في حالة الفشل
      return _ArabicFonts(
        regular: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
        fallbacks: [pw.Font.helvetica(), pw.Font.helveticaBold()],
      );
    }
  }

  /// تطبيق فلاتر التصفية على المهام
  List<Task> _applyFilters(List<Task> tasks, ReportFilters? filters) {
    if (filters == null) return tasks;
    
    return tasks.where((task) {
      // فلتر التاريخ
      if (filters.startDate != null) {
        final taskDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        if (taskDate.isBefore(filters.startDate!)) return false;
      }
      
      if (filters.endDate != null) {
        final taskDate = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        if (taskDate.isAfter(filters.endDate!)) return false;
      }
      
      // فلتر المستخدمين
      if (filters.userIds != null && filters.userIds!.isNotEmpty) {
        if (!filters.userIds!.contains(task.assigneeId) && 
            !filters.userIds!.contains(task.creatorId)) {
          return false;
        }
      }
      
      // فلتر الأقسام
      if (filters.departmentIds != null && filters.departmentIds!.isNotEmpty) {
        if (!filters.departmentIds!.contains(task.departmentId)) return false;
      }
      
      // فلتر الحالات
      if (filters.statuses != null && filters.statuses!.isNotEmpty) {
        if (!filters.statuses!.contains(task.status)) return false;
      }
      
      // فلتر الأولويات
      if (filters.priorities != null && filters.priorities!.isNotEmpty) {
        if (!filters.priorities!.contains(task.priority)) return false;
      }
      
      // فلتر المهام المحذوفة
      if (!filters.includeDeleted && task.isDeleted) return false;
      
      // فلتر المهام المكتملة فقط
      if (filters.completedOnly && task.status != 'مكتملة') return false;
      
      // فلتر المهام المتأخرة فقط
      if (filters.overdueOnly) {
        if (task.dueDate == null || task.status == 'مكتملة') return false;
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        if (now <= task.dueDate!) return false;
      }
      
      return true;
    }).toList();
  }

  /// حساب إحصائيات التقرير
  ReportStatistics _calculateStatistics(List<TaskReportData> tasks) {
    final totalTasks = tasks.length;
    final completedTasks = tasks.where((t) => t.status == 'مكتملة').length;
    final inProgressTasks = tasks.where((t) => t.status == 'قيد التنفيذ').length;
    final overdueTasks = tasks.where((t) => t.isOverdue).length;
    final cancelledTasks = tasks.where((t) => t.status == 'ملغاة').length;
    
    final averageCompletion = totalTasks > 0 
        ? tasks.map((t) => t.completionPercentage).reduce((a, b) => a + b) / totalTasks
        : 0.0;
    
    final totalEstimatedTime = tasks
        .where((t) => t.estimatedTime != null)
        .map((t) => t.estimatedTime!)
        .fold(0, (a, b) => a + b);
    
    final totalActualTime = tasks
        .where((t) => t.actualTime != null)
        .map((t) => t.actualTime!)
        .fold(0, (a, b) => a + b);
    
    final activeUsers = tasks
        .map((t) => t.assignee?.id)
        .where((id) => id != null)
        .toSet()
        .length;
    
    final activeDepartments = tasks
        .map((t) => t.department?.id)
        .where((id) => id != null)
        .toSet()
        .length;
    
    return ReportStatistics(
      totalTasks: totalTasks,
      completedTasks: completedTasks,
      inProgressTasks: inProgressTasks,
      overdueTasks: overdueTasks,
      cancelledTasks: cancelledTasks,
      averageCompletion: averageCompletion,
      totalEstimatedTime: totalEstimatedTime,
      totalActualTime: totalActualTime,
      activeUsers: activeUsers,
      activeDepartments: activeDepartments,
    );
  }

  /// إنشاء تقرير فارغ
  pw.Document _createEmptyReport(pw.Document pdf, _ArabicFonts fonts, String message) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'تقرير المهام',
                  style: pw.TextStyle(
                    font: fonts.bold,
                    fontFallback: fonts.fallbacks,
                    fontSize: 24,
                    color: PdfColors.teal800,
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  message,
                  style: pw.TextStyle(
                    font: fonts.regular,
                    fontFallback: fonts.fallbacks,
                    fontSize: 16,
                    color: PdfColors.grey800,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
    return pdf;
  }

  /// إنشاء تقرير خطأ
  pw.Document _createErrorReport(pw.Document pdf, _ArabicFonts fonts, String error) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'خطأ في إنشاء التقرير',
                  style: pw.TextStyle(
                    font: fonts.bold,
                    fontFallback: fonts.fallbacks,
                    fontSize: 20,
                    color: PdfColors.red,
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Text(
                  error,
                  style: pw.TextStyle(
                    font: fonts.regular,
                    fontFallback: fonts.fallbacks,
                    fontSize: 14,
                    color: PdfColors.grey800,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
    return pdf;
  }

  /// إضافة صفحة الغلاف للتقرير الشامل
  void _addCoverPage(pw.Document pdf, _ArabicFonts fonts, int tasksCount, ReportStatistics? statistics) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.SizedBox(height: 50),
              
              // العنوان الرئيسي
              pw.Text(
                'بسم الله الرحمن الرحيم',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 18,
                  color: PdfColors.teal800,
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              pw.Text(
                'تقرير شامل للمهام',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 28,
                  color: PdfColors.teal800,
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // معلومات أساسية
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    _buildInfoRow(fonts, 'إجمالي المهام', tasksCount.toString()),
                    if (statistics != null) ...[
                      pw.SizedBox(height: 10),
                      _buildInfoRow(fonts, 'المهام المكتملة', statistics.completedTasks.toString()),
                      pw.SizedBox(height: 10),
                      _buildInfoRow(fonts, 'المهام قيد التنفيذ', statistics.inProgressTasks.toString()),
                      pw.SizedBox(height: 10),
                      _buildInfoRow(fonts, 'المهام المتأخرة', statistics.overdueTasks.toString()),
                    ],
                  ],
                ),
              ),
              
              pw.Spacer(),
              
              // معلومات التاريخ
              pw.Text(
                'تاريخ الإنشاء: ${_formatDateTime(DateTime.now())}',
                style: pw.TextStyle(
                  font: fonts.regular,
                  fontFallback: fonts.fallbacks,
                  fontSize: 12,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// إضافة صفحة الإحصائيات
  void _addStatisticsPage(pw.Document pdf, _ArabicFonts fonts, ReportStatistics statistics) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // عنوان الصفحة
              pw.Text(
                'الإحصائيات العامة',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 22,
                  color: PdfColors.teal800,
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // إحصائيات المهام
              _buildStatisticsSection(fonts, 'إحصائيات المهام', [
                ['إجمالي المهام', statistics.totalTasks.toString()],
                ['المهام المكتملة', '${statistics.completedTasks} (${statistics.completionRate.toStringAsFixed(1)}%)'],
                ['المهام قيد التنفيذ', statistics.inProgressTasks.toString()],
                ['المهام المتأخرة', '${statistics.overdueTasks} (${statistics.overdueRate.toStringAsFixed(1)}%)'],
                ['المهام الملغاة', statistics.cancelledTasks.toString()],
                ['متوسط نسبة الإنجاز', '${statistics.averageCompletion.toStringAsFixed(1)}%'],
              ]),
              
              pw.SizedBox(height: 20),
              
              // إحصائيات الوقت
              _buildStatisticsSection(fonts, 'إحصائيات الوقت', [
                ['إجمالي الوقت المقدر', '${statistics.totalEstimatedTime} ساعة'],
                ['إجمالي الوقت الفعلي', '${statistics.totalActualTime} ساعة'],
                ['كفاءة الوقت', '${statistics.timeEfficiency.toStringAsFixed(1)}%'],
              ]),
              
              pw.SizedBox(height: 20),
              
              // إحصائيات الموارد
              _buildStatisticsSection(fonts, 'إحصائيات الموارد', [
                ['المستخدمون النشطون', statistics.activeUsers.toString()],
                ['الأقسام النشطة', statistics.activeDepartments.toString()],
              ]),
            ],
          );
        },
      ),
    );
  }

  /// إضافة جدول المهام الرئيسي
  void _addTasksTable(pw.Document pdf, _ArabicFonts fonts, List<TaskReportData> tasks) {
    // تقسيم المهام إلى مجموعات للصفحات
    const tasksPerPage = 15;
    final totalPages = (tasks.length / tasksPerPage).ceil();
    
    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final startIndex = pageIndex * tasksPerPage;
      final endIndex = (startIndex + tasksPerPage).clamp(0, tasks.length);
      final pageTasks = tasks.sublist(startIndex, endIndex);
      
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          header: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'جدول المهام - الصفحة ${pageIndex + 1} من $totalPages',
                  style: pw.TextStyle(
                    font: fonts.bold,
                    fontFallback: fonts.fallbacks,
                    fontSize: 18,
                    color: PdfColors.teal800,
                  ),
                ),
                pw.SizedBox(height: 10),
              ],
            );
          },
          footer: _buildFooter(fonts),
          build: (pw.Context context) {
            return [
              _buildTasksTable(fonts, pageTasks, startIndex),
            ];
          },
        ),
      );
    }
  }

  /// بناء جدول المهام
  pw.Widget _buildTasksTable(_ArabicFonts fonts, List<TaskReportData> tasks, int startIndex) {
    final headers = [
      '#',
      'العنوان',
      'الحالة',
      'الأولوية',
      'النسبة %',
      'تاريخ الإنشاء',
      'المكلف',
      'القسم',
    ];
    
    final data = tasks.asMap().entries.map((entry) {
      final index = startIndex + entry.key + 1;
      final task = entry.value;
      return [
        index.toString(),
        task.title.length > 30 ? '${task.title.substring(0, 30)}...' : task.title,
        task.status,
        task.priority,
        '${task.completionPercentage}%',
        _formatDate(task.createdAt),
        task.assignee?.name ?? '-',
        task.department?.name ?? '-',
      ];
    }).toList();
    
    return pw.TableHelper.fromTextArray(
      headers: headers,
      data: data,
      cellStyle: pw.TextStyle(
        font: fonts.regular,
        fontFallback: fonts.fallbacks,
        fontSize: 10,
      ),
      headerStyle: pw.TextStyle(
        font: fonts.bold,
        fontFallback: fonts.fallbacks,
        fontSize: 12,
        fontWeight: pw.FontWeight.bold,
      ),
      cellAlignment: pw.Alignment.centerRight,
      headerDecoration: const pw.BoxDecoration(color: PdfColors.teal100),
      rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
      oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey50),
      cellAlignments: {
        0: pw.Alignment.center,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.center,
        3: pw.Alignment.center,
        4: pw.Alignment.center,
        5: pw.Alignment.center,
        6: pw.Alignment.centerRight,
        7: pw.Alignment.centerRight,
      },
      columnWidths: {
        0: pw.FixedColumnWidth(25),
        1: pw.FlexColumnWidth(3),
        2: pw.FixedColumnWidth(60),
        3: pw.FixedColumnWidth(60),
        4: pw.FixedColumnWidth(50),
        5: pw.FixedColumnWidth(80),
        6: pw.FlexColumnWidth(2),
        7: pw.FlexColumnWidth(2),
      },
    );
  }

  /// إضافة تفاصيل المهام المهمة
  void _addImportantTasksDetails(pw.Document pdf, _ArabicFonts fonts, List<TaskReportData> tasks) {
    // المهام المتأخرة
    final overdueTasks = tasks.where((t) => t.isOverdue).toList();
    if (overdueTasks.isNotEmpty) {
      _addTasksDetailSection(pdf, fonts, 'المهام المتأخرة', overdueTasks);
    }
    
    // المهام عالية الأولوية
    final highPriorityTasks = tasks.where((t) => t.priority == 'عالية').toList();
    if (highPriorityTasks.isNotEmpty) {
      _addTasksDetailSection(pdf, fonts, 'المهام عالية الأولوية', highPriorityTasks);
    }
    
    // المهام المكتملة حديثاً
    final recentlyCompleted = tasks
        .where((t) => t.status == 'مكتملة' && t.completedAt != null)
        .toList()
      ..sort((a, b) => b.completedAt!.compareTo(a.completedAt!));
    
    if (recentlyCompleted.isNotEmpty) {
      final recent = recentlyCompleted.take(10).toList();
      _addTasksDetailSection(pdf, fonts, 'المهام المكتملة حديثاً', recent);
    }
  }

  /// إضافة قسم تفاصيل مجموعة مهام
  void _addTasksDetailSection(pw.Document pdf, _ArabicFonts fonts, String title, List<TaskReportData> tasks) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            title,
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return tasks.map((task) => _buildTaskDetailCard(fonts, task)).toList();
        },
      ),
    );
  }

  /// بناء بطاقة تفاصيل مهمة
  pw.Widget _buildTaskDetailCard(_ArabicFonts fonts, TaskReportData task) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 15),
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان المهمة
          pw.Text(
            task.title,
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 14,
              color: PdfColors.teal800,
            ),
          ),
          
          pw.SizedBox(height: 8),
          
          // معلومات أساسية
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoRow(fonts, 'الحالة', task.status),
              ),
              pw.Expanded(
                child: _buildInfoRow(fonts, 'الأولوية', task.priority),
              ),
              pw.Expanded(
                child: _buildInfoRow(fonts, 'النسبة', '${task.completionPercentage}%'),
              ),
            ],
          ),
          
          pw.SizedBox(height: 5),
          
          // معلومات إضافية
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoRow(fonts, 'المكلف', task.assignee?.name ?? '-'),
              ),
              pw.Expanded(
                child: _buildInfoRow(fonts, 'القسم', task.department?.name ?? '-'),
              ),
              pw.Expanded(
                child: _buildInfoRow(fonts, 'تاريخ الإنشاء', _formatDate(task.createdAt)),
              ),
            ],
          ),
          
          // الوصف إذا كان موجوداً
          if (task.description != null && task.description!.isNotEmpty) ...[
            pw.SizedBox(height: 8),
            pw.Text(
              'الوصف: ${task.description}',
              style: pw.TextStyle(
                font: fonts.regular,
                fontFallback: fonts.fallbacks,
                fontSize: 10,
                color: PdfColors.grey700,
              ),
            ),
          ],
          
          // معلومات التأخير إذا كانت المهمة متأخرة
          if (task.isOverdue) ...[
            pw.SizedBox(height: 5),
            pw.Text(
              'متأخرة بـ ${task.daysOverdue} يوم',
              style: pw.TextStyle(
                font: fonts.bold,
                fontFallback: fonts.fallbacks,
                fontSize: 10,
                color: PdfColors.red,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// إضافة صفحة الغلاف للمهمة الواحدة
  void _addTaskCoverPage(pw.Document pdf, _ArabicFonts fonts, Task task, String? username) {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.SizedBox(height: 50),
              
              pw.Text(
                'بسم الله الرحمن الرحيم',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 18,
                  color: PdfColors.teal800,
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              pw.Text(
                'تقرير مفصل للمهمة',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 24,
                  color: PdfColors.teal800,
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // عنوان المهمة
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.teal50,
                  borderRadius: pw.BorderRadius.circular(10),
                  border: pw.Border.all(color: PdfColors.teal200),
                ),
                child: pw.Text(
                  task.title,
                  style: pw.TextStyle(
                    font: fonts.bold,
                    fontFallback: fonts.fallbacks,
                    fontSize: 18,
                    color: PdfColors.teal800,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // معلومات أساسية
              pw.Container(
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  children: [
                    _buildInfoRow(fonts, 'رقم المهمة', task.id.toString()),
                    pw.SizedBox(height: 8),
                    _buildInfoRow(fonts, 'الحالة', task.status),
                    pw.SizedBox(height: 8),
                    _buildInfoRow(fonts, 'الأولوية', task.priority),
                    pw.SizedBox(height: 8),
                    _buildInfoRow(fonts, 'نسبة الإنجاز', '${task.completionPercentage}%'),
                  ],
                ),
              ),
              
              pw.Spacer(),
              
              // معلومات المستخدم والتاريخ
              pw.Column(
                children: [
                  if (username != null)
                    pw.Text(
                      'طلب التقرير: $username',
                      style: pw.TextStyle(
                        font: fonts.regular,
                        fontFallback: fonts.fallbacks,
                        fontSize: 12,
                        color: PdfColors.grey600,
                      ),
                    ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    'تاريخ الإنشاء: ${_formatDateTime(DateTime.now())}',
                    style: pw.TextStyle(
                      font: fonts.regular,
                      fontFallback: fonts.fallbacks,
                      fontSize: 12,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  /// إضافة المعلومات الأساسية للمهمة
  void _addTaskBasicInfo(pw.Document pdf, _ArabicFonts fonts, Task task, TaskReportStatistics statistics) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'المعلومات الأساسية',
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return [
            // معلومات المهمة
            _buildInfoSection(fonts, 'تفاصيل المهمة', [
              ['رقم المهمة', task.id.toString()],
              ['العنوان', task.title],
              ['الوصف', task.description ?? '-'],
              ['الحالة', task.status],
              ['الأولوية', task.priority],
              ['نسبة الإنجاز', '${task.completionPercentage}%'],
              ['تاريخ الإنشاء', _formatDate(DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000))],
              ['تاريخ البداية', task.startDate != null ? _formatDate(DateTime.fromMillisecondsSinceEpoch(task.startDate! * 1000)) : '-'],
              ['تاريخ الاستحقاق', task.dueDate != null ? _formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000)) : '-'],
              ['تاريخ الإنجاز', task.completedAt != null ? _formatDate(DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000)) : '-'],
              ['الوقت المقدر', task.estimatedTime != null ? '${task.estimatedTime} دقيقة' : '-'],
              ['الوقت الفعلي', task.actualTime != null ? '${task.actualTime} دقيقة' : '-'],
              ['الوارد', task.incoming ?? '-'],
              ['ملاحظات', task.note ?? '-'],
            ]),
            
            pw.SizedBox(height: 20),
            
            // معلومات الأشخاص
            _buildInfoSection(fonts, 'الأشخاص المرتبطون', [
              ['المنشئ', task.creator?.name ?? '-'],
              ['المكلف', task.assignee?.name ?? '-'],
              ['القسم', task.department?.name ?? '-'],
              ['نوع المهمة', task.taskType?.name ?? '-'],
            ]),
            
            pw.SizedBox(height: 20),
            
            // الإحصائيات
            _buildInfoSection(fonts, 'إحصائيات المهمة', [
              ['عدد التعليقات', statistics.totalComments.toString()],
              ['عدد المرفقات', statistics.totalAttachments.toString()],
              ['عدد المساهمين', statistics.totalContributors.toString()],
              ['عدد التحويلات', statistics.totalTransfers.toString()],
              ['الأيام النشطة', statistics.daysActive.toString()],
              ['أكثر المساهمين نشاطاً', statistics.mostActiveContributor],
              ['آخر تحويل', statistics.latestTransfer],
            ]),
          ];
        },
      ),
    );
  }

  /// إضافة قسم المساهمين
  void _addContributorsSection(pw.Document pdf, _ArabicFonts fonts, List<TaskContributor> contributors) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'المساهمون في المهمة',
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return [
            // جدول المساهمين
            pw.TableHelper.fromTextArray(
              headers: ['#', 'الاسم', 'الدور', 'التعليقات', 'المرفقات', 'إجمالي المساهمات', 'النسبة %', 'المستوى'],
              data: contributors.asMap().entries.map((entry) {
                final index = entry.key + 1;
                final contributor = entry.value;
                return [
                  index.toString(),
                  contributor.userName,
                  contributor.role,
                  contributor.commentsCount.toString(),
                  contributor.attachmentsCount.toString(),
                  contributor.totalContributions.toString(),
                  '${contributor.percentage.toStringAsFixed(1)}%',
                  contributor.level.displayName,
                ];
              }).toList(),
              cellStyle: pw.TextStyle(
                font: fonts.regular,
                fontFallback: fonts.fallbacks,
                fontSize: 10,
              ),
              headerStyle: pw.TextStyle(
                font: fonts.bold,
                fontFallback: fonts.fallbacks,
                fontSize: 11,
                fontWeight: pw.FontWeight.bold,
              ),
              cellAlignment: pw.Alignment.centerRight,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.teal100),
              rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
              oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey50),
              cellAlignments: {
                0: pw.Alignment.center,
                1: pw.Alignment.centerRight,
                2: pw.Alignment.center,
                3: pw.Alignment.center,
                4: pw.Alignment.center,
                5: pw.Alignment.center,
                6: pw.Alignment.center,
                7: pw.Alignment.center,
              },
            ),
          ];
        },
      ),
    );
  }

  /// إضافة قسم التحويلات
  void _addTransfersSection(pw.Document pdf, _ArabicFonts fonts, List<TaskTransfer> transfers) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'تحويلات المهمة',
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return [
            // جدول التحويلات
            pw.TableHelper.fromTextArray(
              headers: ['#', 'من', 'إلى', 'التاريخ', 'السبب', 'المنفذ', 'النوع'],
              data: transfers.asMap().entries.map((entry) {
                final index = entry.key + 1;
                final transfer = entry.value;
                return [
                  index.toString(),
                  transfer.fromUser,
                  transfer.toUser,
                  _formatDate(DateTime.fromMillisecondsSinceEpoch(transfer.timestamp * 1000)),
                  transfer.reason,
                  transfer.executor,
                  transfer.type.displayName,
                ];
              }).toList(),
              cellStyle: pw.TextStyle(
                font: fonts.regular,
                fontFallback: fonts.fallbacks,
                fontSize: 9,
              ),
              headerStyle: pw.TextStyle(
                font: fonts.bold,
                fontFallback: fonts.fallbacks,
                fontSize: 10,
                fontWeight: pw.FontWeight.bold,
              ),
              cellAlignment: pw.Alignment.centerRight,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.teal100),
              rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
              oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey50),
              cellAlignments: {
                0: pw.Alignment.center,
                1: pw.Alignment.centerRight,
                2: pw.Alignment.centerRight,
                3: pw.Alignment.center,
                4: pw.Alignment.centerRight,
                5: pw.Alignment.centerRight,
                6: pw.Alignment.center,
              },
            ),
          ];
        },
      ),
    );
  }

  /// إضافة التعليقات والمرفقات
  void _addCommentsAndAttachments(pw.Document pdf, _ArabicFonts fonts, Task task) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'التعليقات والمرفقات',
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return [
            // التعليقات
            if (task.comments.isNotEmpty) ...[
              pw.Text(
                'التعليقات (${task.comments.length})',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 16,
                  color: PdfColors.teal700,
                ),
              ),
              pw.SizedBox(height: 10),
              ...task.comments.map((comment) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 10),
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey50,
                  borderRadius: pw.BorderRadius.circular(5),
                  border: pw.Border.all(color: PdfColors.grey200),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Row(
                      children: [
                        pw.Text(
                          comment.user?.name ?? 'مستخدم غير معروف',
                          style: pw.TextStyle(
                            font: fonts.bold,
                            fontFallback: fonts.fallbacks,
                            fontSize: 11,
                            color: PdfColors.teal800,
                          ),
                        ),
                        pw.Spacer(),
                        pw.Text(
                          _formatDate(DateTime.fromMillisecondsSinceEpoch(comment.createdAt * 1000)),
                          style: pw.TextStyle(
                            font: fonts.regular,
                            fontFallback: fonts.fallbacks,
                            fontSize: 9,
                            color: PdfColors.grey600,
                          ),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      comment.content,
                      style: pw.TextStyle(
                        font: fonts.regular,
                        fontFallback: fonts.fallbacks,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              )),
              pw.SizedBox(height: 20),
            ],
            
            // المرفقات
            if (task.attachments.isNotEmpty) ...[
              pw.Text(
                'المرفقات (${task.attachments.length})',
                style: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 16,
                  color: PdfColors.teal700,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.TableHelper.fromTextArray(
                headers: ['#', 'اسم الملف', 'النوع', 'الحجم', 'رفعه', 'التاريخ'],
                data: task.attachments.asMap().entries.map((entry) {
                  final index = entry.key + 1;
                  final attachment = entry.value;
                  return [
                    index.toString(),
                    attachment.fileName,
                    attachment.fileType.toUpperCase(),
                    attachment.fileSizeFormatted,
                    attachment.uploadedByUser?.name ?? 'غير معروف',
                    _formatDate(DateTime.fromMillisecondsSinceEpoch(attachment.uploadedAt * 1000)),
                  ];
                }).toList(),
                cellStyle: pw.TextStyle(
                  font: fonts.regular,
                  fontFallback: fonts.fallbacks,
                  fontSize: 9,
                ),
                headerStyle: pw.TextStyle(
                  font: fonts.bold,
                  fontFallback: fonts.fallbacks,
                  fontSize: 10,
                  fontWeight: pw.FontWeight.bold,
                ),
                cellAlignment: pw.Alignment.centerRight,
                headerDecoration: const pw.BoxDecoration(color: PdfColors.teal100),
                rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
                oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey50),
              ),
            ],
            
            // رسالة في حالة عدم وجود تعليقات أو مرفقات
            if (task.comments.isEmpty && task.attachments.isEmpty)
              pw.Center(
                child: pw.Text(
                  'لا توجد تعليقات أو مرفقات لهذه المهمة',
                  style: pw.TextStyle(
                    font: fonts.regular,
                    fontFallback: fonts.fallbacks,
                    fontSize: 14,
                    color: PdfColors.grey600,
                  ),
                ),
              ),
          ];
        },
      ),
    );
  }

  /// إضافة قسم المهام الفرعية
  void _addSubtasksSection(pw.Document pdf, _ArabicFonts fonts, List<dynamic> subtasks) {
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        header: (pw.Context context) {
          return pw.Text(
            'المهام الفرعية (${subtasks.length})',
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 18,
              color: PdfColors.teal800,
            ),
          );
        },
        footer: _buildFooter(fonts),
        build: (pw.Context context) {
          return [
            pw.TableHelper.fromTextArray(
              headers: ['#', 'العنوان', 'الحالة', 'تاريخ الإنشاء', 'تاريخ الإنجاز'],
              data: subtasks.asMap().entries.map((entry) {
                final index = entry.key + 1;
                final subtask = entry.value;
                return [
                  index.toString(),
                  subtask.title ?? 'غير محدد',
                  subtask.isCompleted ? 'مكتملة' : 'قيد التنفيذ',
                  _formatDate(DateTime.fromMillisecondsSinceEpoch(subtask.createdAt * 1000)),
                  subtask.completedAt != null 
                      ? _formatDate(DateTime.fromMillisecondsSinceEpoch(subtask.completedAt * 1000))
                      : '-',
                ];
              }).toList(),
              cellStyle: pw.TextStyle(
                font: fonts.regular,
                fontFallback: fonts.fallbacks,
                fontSize: 10,
              ),
              headerStyle: pw.TextStyle(
                font: fonts.bold,
                fontFallback: fonts.fallbacks,
                fontSize: 11,
                fontWeight: pw.FontWeight.bold,
              ),
              cellAlignment: pw.Alignment.centerRight,
              headerDecoration: const pw.BoxDecoration(color: PdfColors.teal100),
              rowDecoration: const pw.BoxDecoration(color: PdfColors.white),
              oddRowDecoration: pw.BoxDecoration(color: PdfColors.grey50),
            ),
          ];
        },
      ),
    );
  }

  /// بناء قسم معلومات
  pw.Widget _buildInfoSection(_ArabicFonts fonts, String title, List<List<String>> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            font: fonts.bold,
            fontFallback: fonts.fallbacks,
            fontSize: 16,
            color: PdfColors.teal700,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            borderRadius: pw.BorderRadius.circular(5),
            border: pw.Border.all(color: PdfColors.grey200),
          ),
          child: pw.Column(
            children: data.map((row) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 5),
              child: _buildInfoRow(fonts, row[0], row[1]),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء قسم إحصائيات
  pw.Widget _buildStatisticsSection(_ArabicFonts fonts, String title, List<List<String>> data) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            font: fonts.bold,
            fontFallback: fonts.fallbacks,
            fontSize: 16,
            color: PdfColors.teal700,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Container(
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            color: PdfColors.teal50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.teal200),
          ),
          child: pw.Column(
            children: data.map((row) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 8),
              child: pw.Row(
                children: [
                  pw.Expanded(
                    flex: 2,
                    child: pw.Text(
                      row[0],
                      style: pw.TextStyle(
                        font: fonts.regular,
                        fontFallback: fonts.fallbacks,
                        fontSize: 12,
                        color: PdfColors.teal800,
                      ),
                    ),
                  ),
                  pw.Expanded(
                    flex: 1,
                    child: pw.Text(
                      row[1],
                      style: pw.TextStyle(
                        font: fonts.bold,
                        fontFallback: fonts.fallbacks,
                        fontSize: 12,
                        color: PdfColors.teal900,
                      ),
                      textAlign: pw.TextAlign.left,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء صف معلومات
  pw.Widget _buildInfoRow(_ArabicFonts fonts, String label, String value) {
    return pw.Row(
      children: [
        pw.Expanded(
          flex: 1,
          child: pw.Text(
            '$label:',
            style: pw.TextStyle(
              font: fonts.regular,
              fontFallback: fonts.fallbacks,
              fontSize: 11,
              color: PdfColors.grey700,
            ),
          ),
        ),
        pw.Expanded(
          flex: 2,
          child: pw.Text(
            value,
            style: pw.TextStyle(
              font: fonts.bold,
              fontFallback: fonts.fallbacks,
              fontSize: 11,
              color: PdfColors.black,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء تذييل الصفحة
  pw.Widget Function(pw.Context) _buildFooter(_ArabicFonts fonts) {
    return (pw.Context context) {
      return pw.Container(
        width: double.infinity,
        padding: const pw.EdgeInsets.symmetric(horizontal: 15, vertical: 8),
        child: pw.Column(
          mainAxisSize: pw.MainAxisSize.min,
          children: [
            // ترقيم الصفحات
            pw.Center(
              child: pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: pw.BoxDecoration(
                  color: PdfColors.teal800,
                  borderRadius: pw.BorderRadius.circular(15),
                  boxShadow: [
                    pw.BoxShadow(
                      color: PdfColors.grey400,
                      offset: const PdfPoint(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                child: pw.Text(
                  'صفحة ${context.pageNumber} من ${context.pagesCount}',
                  style: pw.TextStyle(
                    font: fonts.regular,
                    fontFallback: fonts.fallbacks,
                    fontSize: 10,
                    color: PdfColors.white,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ),
            pw.SizedBox(height: 4),
            // تاريخ الإنشاء
            pw.Center(
              child: pw.Text(
                'تاريخ الإنشاء: ${_formatDateTime(DateTime.now())}',
                style: pw.TextStyle(
                  font: fonts.regular,
                  fontFallback: fonts.fallbacks,
                  fontSize: 8,
                  color: PdfColors.grey600,
                ),
              ),
            ),
          ],
        ),
      );
    };
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// فئة مساعدة للخطوط العربية
class _ArabicFonts {
  final pw.Font regular;
  final pw.Font bold;
  final List<pw.Font> fallbacks;

  const _ArabicFonts({
    required this.regular,
    required this.bold,
    required this.fallbacks,
  });
}