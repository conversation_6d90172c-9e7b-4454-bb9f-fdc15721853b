import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

import '../../controllers/theme_controller.dart';

/// شاشة إعدادات السمة
///
/// تتيح للمستخدم تغيير وضع السمة (فاتح/داكن/نظام)
class ThemeSettingsScreen extends StatelessWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeController = Get.find<ThemeController>();

    return Scaffold(
      appBar: AppBar(
        title: Text('إعدادات السمة'.tr),
      ),
      body: Obx(() {
        // استخدام الخصائص التفاعلية للتحديث التلقائي
        themeController.isDarkModeRx.value;
        themeController.isSystemThemeRx.value;

        return ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // عنوان القسم
          Text(
            'وضع السمة'.tr,
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 16),

          // وضع السمة الفاتح
          _buildThemeOption(
            title: 'وضع فاتح'.tr,
            subtitle: 'استخدام الوضع الفاتح دائماً'.tr,
            icon: Icons.light_mode,
            isSelected: !themeController.isDarkMode && !themeController.isSystemTheme,
            onTap: () async {
              if (themeController.isDarkMode || themeController.isSystemTheme) {
                // تطبيق التغييرات
                await themeController.setDarkMode(false);

                // إظهار رسالة تأكيد
                Get.snackbar(
                  'تم تغيير السمة',
                  'تم تفعيل الوضع الفاتح بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.card,
                  colorText: AppColors.textPrimary,
                  duration: const Duration(seconds: 2),
                );
              }
            },
          ),

          const Divider(),

          // وضع السمة الداكن
          _buildThemeOption(
            title: 'وضع داكن'.tr,
            subtitle: 'استخدام الوضع الداكن دائماً'.tr,
            icon: Icons.dark_mode,
            isSelected: themeController.isDarkMode && !themeController.isSystemTheme,
            onTap: () async {
              if (!themeController.isDarkMode || themeController.isSystemTheme) {
                // تطبيق التغييرات
                await themeController.setDarkMode(true);

                // إظهار رسالة تأكيد
                Get.snackbar(
                  'تم تغيير السمة',
                  'تم تفعيل الوضع الداكن بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.card,
                  colorText: AppColors.textPrimary,
                  duration: const Duration(seconds: 2),
                );
              }
            },
          ),

          const Divider(),

          // وضع سمة النظام
          _buildThemeOption(
            title: 'وضع النظام'.tr,
            subtitle: 'اتباع إعدادات النظام'.tr,
            icon: Icons.settings_suggest,
            isSelected: themeController.isSystemTheme,
            onTap: () async {
              if (!themeController.isSystemTheme) {
                await themeController.setSystemTheme(true);

                // إظهار رسالة تأكيد
                Get.snackbar(
                  'تم تغيير السمة',
                  'تم تفعيل وضع النظام بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.card,
                  colorText: AppColors.textPrimary,
                  duration: const Duration(seconds: 2),
                );
              }
            },
          ),

          const SizedBox(height: 32),

          // معلومات حول السمة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.getContainerColor(lighter: true),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.getBorderColor()),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.textSecondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'معلومات'.tr,
                      style: AppStyles.titleSmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'يمكنك تغيير وضع السمة في أي وقت من خلال هذه الإعدادات. سيتم حفظ تفضيلاتك واستعادتها عند إعادة تشغيل التطبيق.'.tr,
                  style: AppStyles.bodyMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'الوضع الحالي: ${_getCurrentThemeText(themeController)}'.tr,
                  style: AppStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
        );
      }),
    );
  }

  /// بناء خيار وضع السمة
  Widget _buildThemeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
      ),
      trailing: isSelected
          ? const Icon(
              Icons.check_circle,
              color: AppColors.primary,
            )
          : null,
      onTap: onTap,
      selected: isSelected,
      selectedTileColor: AppColors.primary.withAlpha(25),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  /// الحصول على نص وضع السمة الحالي
  String _getCurrentThemeText(ThemeController controller) {
    if (controller.isSystemTheme) {
      return controller.isDarkMode
          ? 'وضع النظام (داكن)'
          : 'وضع النظام (فاتح)';
    } else {
      return controller.isDarkMode ? 'وضع داكن' : 'وضع فاتح';
    }
  }
}
