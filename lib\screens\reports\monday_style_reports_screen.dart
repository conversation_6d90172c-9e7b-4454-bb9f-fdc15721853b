import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../models/report_models.dart';
import '../../models/enhanced_report_model.dart';
import '../../routes/app_routes.dart';
import '../../controllers/report_controller.dart';
import '../../controllers/auth_controller.dart';
import '../widgets/reporting/monday_style_report_card.dart';
import '../widgets/common/empty_state_widget.dart';
import '../widgets/common/loading_indicator.dart';

/// شاشة التقارير بتصميم Monday.com
///
/// تعرض قائمة التقارير المتاحة بتصميم مشابه لـ Monday.com
class MondayStyleReportsScreen extends StatefulWidget {
  const MondayStyleReportsScreen({super.key});

  @override
  State<MondayStyleReportsScreen> createState() => _MondayStyleReportsScreenState();
}

class _MondayStyleReportsScreenState extends State<MondayStyleReportsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ReportController _reportController = Get.find<ReportController>();
  // Removed unused AuthController
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadReports();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل التقارير
  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _reportController.loadReports();
    } catch (e) {
      debugPrint('خطأ في تحميل التقارير: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل التقارير',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تصفية التقارير حسب البحث
  List<EnhancedReport> _filterReports(List<EnhancedReport> reports) {
    if (_searchQuery.isEmpty) {
      return reports;
    }

    return reports.where((report) {
      return report.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (report.description?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              // عرض مساعدة حول التقارير
              Get.toNamed(AppRoutes.help, arguments: 'reports');
            },
            tooltip: 'مساعدة',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'جميع التقارير'),
            Tab(text: 'المفضلة'),
            Tab(text: 'التقارير المشتركة'),
            Tab(text: 'تقاريري'),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'البحث في التقارير...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
          ),

          // محتوى التبويبات
          Expanded(
            child: Obx(() => TabBarView(
              controller: _tabController,
              children: [
                // جميع التقارير
                _buildReportsList(_reportController.reports),

                // المفضلة
                _buildReportsList(_reportController.favoriteReports),

                // التقارير المشتركة
                _buildReportsList(_reportController.sharedReports),

                // تقاريري
                _buildReportsList(_reportController.reports.where((report) =>
                  report.createdBy == Get.find<AuthController>().currentUser.value?.id).toList()),
              ],
            )),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // الانتقال إلى شاشة إنشاء تقرير جديد مع تمرير المعلومات الأساسية
          Get.toNamed(
            AppRoutes.createReport,
            arguments: {
              'title': 'تقرير جديد',
              'description': 'وصف التقرير الجديد',
              'type': ReportType.custom,
            },
          );
        },
        tooltip: 'إنشاء تقرير جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// بناء قائمة التقارير
  Widget _buildReportsList(List<EnhancedReport> reports) {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    final filteredReports = _filterReports(reports);

    if (filteredReports.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.bar_chart,
        message: 'لا توجد تقارير',
        description: 'لم يتم العثور على أي تقارير. يمكنك إنشاء تقرير جديد بالضغط على زر الإضافة.',
        buttonText: 'إنشاء تقرير جديد',
        onButtonPressed: () {
          Get.toNamed(
            AppRoutes.createReport,
            arguments: {
              'title': 'تقرير جديد',
              'description': 'وصف التقرير الجديد',
              'type': ReportType.custom,
            },
          );
        },
      );
    }

    // تجميع التقارير حسب النوع
    final Map<ReportType, List<EnhancedReport>> groupedReports = {};

    for (final report in filteredReports) {
      if (!groupedReports.containsKey(report.type)) {
        groupedReports[report.type] = [];
      }

      groupedReports[report.type]!.add(report);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض التقارير مجمعة حسب النوع
          for (final type in groupedReports.keys)
            _buildReportGroup(type, groupedReports[type]!),
        ],
      ),
    );
  }

  /// بناء مجموعة تقارير
  Widget _buildReportGroup(ReportType type, List<EnhancedReport> reports) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المجموعة
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            _getReportTypeTitle(type),
            style: AppStyles.titleMedium,
          ),
        ),

        // بطاقات التقارير
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1.5,
            crossAxisSpacing: 16.0,
            mainAxisSpacing: 16.0,
          ),
          itemCount: reports.length,
          itemBuilder: (context, index) {
            final report = reports[index];
            return MondayStyleReportCard(
              report: report,
              onTap: () {
                // الانتقال إلى شاشة عرض التقرير
                Get.toNamed(
                  AppRoutes.mondayStyleReportViewer,
                  arguments: {'reportId': report.id},
                );
              },
              onEdit: () async {
                // الانتقال إلى شاشة تعديل التقرير مع تمرير المعلومات المطلوبة
                try {
                  Get.toNamed(
                    AppRoutes.createReport,
                    arguments: {
                      'title': report.title,
                      'description': report.description,
                      'type': report.type,
                      'reportId': report.id, // إضافة معرف التقرير للتعديل
                      'isEditing': true,
                    },
                  );
                } catch (e) {
                  debugPrint('خطأ في الانتقال لتعديل التقرير: $e');
                  Get.snackbar(
                    'خطأ',
                    'حدث خطأ أثناء فتح شاشة التعديل',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              onDelete: () {
                _showDeleteConfirmation(report);
              },
              onToggleFavorite: () {
                _reportController.toggleFavorite(report.id);
              },
            );
          },
        ),

        const SizedBox(height: 24.0),
      ],
    );
  }

  /// الحصول على عنوان نوع التقرير
  String _getReportTypeTitle(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'تقارير حالة المهام';
      case ReportType.userPerformance:
        return 'تقارير أداء المستخدمين';
      case ReportType.departmentPerformance:
        return 'تقارير أداء الأقسام';
      case ReportType.timeTracking:
        return 'تقارير تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقارير تقدم المهام';
      case ReportType.taskDetails:
        return 'تقارير تفاصيل المهام';
      case ReportType.taskCompletion:
        return 'تقارير إكمال المهام';
      case ReportType.userActivity:
        return 'تقارير نشاط المستخدمين';
      case ReportType.departmentWorkload:
        return 'تقارير عبء العمل للأقسام';
      case ReportType.projectStatus:
        return 'تقارير حالة المشاريع';
      case ReportType.custom:
        return 'تقارير مخصصة';
      default:
        return 'تقارير أخرى';
    }
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(EnhancedReport report) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف التقرير'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _reportController.deleteReport(report.id);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
