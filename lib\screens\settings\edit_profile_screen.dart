import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../models/user_model.dart';
import '../../utils/file_processor.dart';
import '../../services/api/image_upload_api_service.dart';
import '../../utils/image_helper.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();

  final _authController = Get.find<AuthController>();
  final _imagePicker = ImagePicker();
  final _imageUploadService = ImageUploadApiService();

  File? _selectedImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initUserData();
  }

  void _initUserData() {
    final user = _authController.currentUser.value;
    if (user != null) {
      _nameController.text = user.name;
      _emailController.text = user.email;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  /// عرض خيارات اختيار الصورة
  Future<void> _showImageSourceOptions() async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));

      setState(() {
        _selectedImage = processedImage;
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));

      setState(() {
        _selectedImage = processedImage;
      });
    }
  }

  /// معالجة الصورة وضغطها
  Future<File> _processImage(File imageFile) async {
    try {
      // استخدام FileProcessor لإنشاء صورة مصغرة
      final thumbnailPath = await FileProcessor.createThumbnail(
        imagePath: imageFile.path,
        thumbnailSize: 500, // حجم مناسب لصورة الملف الشخصي
      );

      if (thumbnailPath != null) {
        return File(thumbnailPath);
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
    }

    // إذا فشلت المعالجة، إرجاع الصورة الأصلية
    return imageFile;
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      // تحضير بيانات المستخدم المحدثة
      String? profileImagePath = currentUser.profileImage;

      // إذا تم اختيار صورة جديدة، قم برفعها إلى الخادم
      if (_selectedImage != null) {
        try {
          profileImagePath = await _imageUploadService.uploadProfileImage(
            _selectedImage!,
            currentUser.id,
          );

          if (profileImagePath == null) {
            throw Exception('فشل في رفع صورة الملف الشخصي');
          }

          debugPrint('تم رفع صورة الملف الشخصي: $profileImagePath');
        } catch (e) {
          throw Exception('فشل في رفع صورة الملف الشخصي: $e');
        }
      }

      // تحديث بيانات المستخدم
      final User updatedUser;
      if (currentUser is User) {
        updatedUser = (currentUser as User).copyWith(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          profileImage: profileImagePath,
        );
      } else {
        updatedUser = currentUser.toUser().copyWith(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          profileImage: profileImagePath,
        );
      }

      // استدعاء وظيفة تحديث الملف الشخصي
      final result = await _authController.updateUserProfile(updatedUser.toJson());

      if (result) {
        // عرض رسالة نجاح
        Get.back();
        Get.snackbar(
          'تم بنجاح',
          'تم تحديث الملف الشخصي بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        throw Exception('فشل تحديث الملف الشخصي');
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل تحديث الملف الشخصي: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = _authController.currentUser.value;
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Profile image
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  _selectedImage != null
                      ? CircleAvatar(
                          radius: 60,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          backgroundImage: FileImage(_selectedImage!),
                        )
                      : ImageHelper.buildProfileImage(
                          imagePath: user.profileImage,
                          radius: 60,
                          fallbackText: user.name,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          textColor: AppColors.primary,
                          fallbackIcon: Icons.person,
                        ),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                      ),
                      onPressed: _showImageSourceOptions,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // حقل الاسم
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الكامل',
                  prefixIcon: Icon(Icons.person_outline),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال الاسم';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // حقل البريد الإلكتروني
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!GetUtils.isEmail(value)) {
                    return 'الرجاء إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // معلومات القسم (للقراءة فقط)
              TextFormField(
                initialValue: user.departmentId != null
                    ? 'القسم: ${user.departmentId}'
                    : 'غير مرتبط بأي قسم',
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  prefixIcon: Icon(Icons.business_outlined),
                ),
                readOnly: true,
                enabled: false,
              ),
              const SizedBox(height: 16),

              // معلومات الدور (للقراءة فقط)
              TextFormField(
                initialValue: _getRoleText(user.role),
                decoration: const InputDecoration(
                  labelText: 'الدور',
                  prefixIcon: Icon(Icons.badge_outlined),
                ),
                readOnly: true,
                enabled: false,
              ),
              const SizedBox(height: 32),

              // زر الحفظ
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _updateProfile,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('حفظ التغييرات'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'مدير النظام العام';
      case UserRole.admin:
        return 'مدير إدارة';
      case UserRole.manager:
        return 'مدير قسم';
      case UserRole.supervisor:
        return 'مشرف';
      case UserRole.user:
        return 'موظف';
    }
  }
}
