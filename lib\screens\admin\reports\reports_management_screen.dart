import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/admin_controller.dart';
import '../../../services/unified_permission_service.dart';
import '../shared/admin_dialog_widget.dart';
import '../shared/admin_card_widget.dart';

/// شاشة إدارة التقارير والإحصائيات
class ReportsManagementScreen extends StatefulWidget {
  const ReportsManagementScreen({super.key});

  @override
  State<ReportsManagementScreen> createState() => _ReportsManagementScreenState();
}

class _ReportsManagementScreenState extends State<ReportsManagementScreen> {
  final AdminController _adminController = Get.find<AdminController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();

  @override
  void initState() {
    super.initState();
    _loadReportsData();
  }

  /// تحميل بيانات التقارير
  Future<void> _loadReportsData() async {
    await _adminController.refreshAllData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير والإحصائيات'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportsData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadReportsData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // إحصائيات سريعة
              _buildQuickStatsSection(),
              
              const SizedBox(height: 24),
              
              // تقارير المستخدمين
              _buildUsersReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير الأدوار والصلاحيات
              _buildRolesPermissionsReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير النظام
              _buildSystemReportsSection(),
              
              const SizedBox(height: 16),
              
              // تقارير مخصصة
              _buildCustomReportsSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() {
          final stats = _adminController.statistics;
          return GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'المستخدمين',
                '${stats['totalUsers'] ?? 0}',
                '${stats['activeUsers'] ?? 0} نشط',
                Icons.people,
                Colors.blue,
              ),
              _buildStatCard(
                'الأدوار',
                '${stats['totalRoles'] ?? 0}',
                '${stats['activeRoles'] ?? 0} نشط',
                Icons.security,
                Colors.green,
              ),
              _buildStatCard(
                'الصلاحيات',
                '${stats['totalPermissions'] ?? 0}',
                'إجمالي الصلاحيات',
                Icons.admin_panel_settings,
                Colors.orange,
              ),
              _buildStatCard(
                'آخر تحديث',
                _formatLastUpdate(stats['lastUpdated']),
                'تحديث البيانات',
                Icons.update,
                Colors.purple,
              ),
            ],
          );
        }),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم تقارير المستخدمين
  Widget _buildUsersReportsSection() {
    return _buildReportSection(
      title: 'تقارير المستخدمين',
      icon: Icons.people,
      reports: [
        ReportItem(
          title: 'تقرير نشاط المستخدمين',
          subtitle: 'إحصائيات نشاط المستخدمين خلال فترة محددة',
          icon: Icons.analytics,
          color: Colors.blue,
          onTap: () => _showUserActivityReport(),
        ),
        ReportItem(
          title: 'توزيع المستخدمين حسب الأقسام',
          subtitle: 'عدد المستخدمين في كل قسم',
          icon: Icons.pie_chart,
          color: Colors.green,
          onTap: () => _showUserDistributionReport(),
        ),
        ReportItem(
          title: 'المستخدمين الجدد',
          subtitle: 'المستخدمين المضافين حديثاً',
          icon: Icons.person_add,
          color: Colors.orange,
          onTap: () => _showNewUsersReport(),
        ),
      ],
    );
  }

  /// بناء قسم تقارير الأدوار والصلاحيات
  Widget _buildRolesPermissionsReportsSection() {
    return _buildReportSection(
      title: 'تقارير الأدوار والصلاحيات',
      icon: Icons.security,
      reports: [
        ReportItem(
          title: 'توزيع الأدوار',
          subtitle: 'عدد المستخدمين لكل دور',
          icon: Icons.donut_small,
          color: Colors.purple,
          onTap: () => _showRoleDistributionReport(),
        ),
        ReportItem(
          title: 'استخدام الصلاحيات',
          subtitle: 'الصلاحيات الأكثر استخداماً',
          icon: Icons.bar_chart,
          color: Colors.teal,
          onTap: () => _showPermissionUsageReport(),
        ),
        ReportItem(
          title: 'الأدوار المخصصة',
          subtitle: 'تقرير الأدوار المخصصة وصلاحياتها',
          icon: Icons.settings,
          color: Colors.indigo,
          onTap: () => _showCustomRolesReport(),
        ),
      ],
    );
  }

  /// بناء قسم تقارير النظام
  Widget _buildSystemReportsSection() {
    return _buildReportSection(
      title: 'تقارير النظام',
      icon: Icons.computer,
      reports: [
        ReportItem(
          title: 'صحة النظام',
          subtitle: 'حالة النظام والأداء',
          icon: Icons.health_and_safety,
          color: Colors.green,
          onTap: () => _showSystemHealthReport(),
        ),
        ReportItem(
          title: 'سجلات النشاط',
          subtitle: 'سجلات العمليات والأنشطة',
          icon: Icons.history,
          color: Colors.blue,
          onTap: () => _showActivityLogsReport(),
        ),
        ReportItem(
          title: 'النسخ الاحتياطية',
          subtitle: 'تقرير النسخ الاحتياطية',
          icon: Icons.backup,
          color: Colors.orange,
          onTap: () => _showBackupsReport(),
        ),
      ],
    );
  }

  /// بناء قسم التقارير المخصصة
  Widget _buildCustomReportsSection() {
    return _buildReportSection(
      title: 'تقارير مخصصة',
      icon: Icons.dashboard_customize,
      reports: [
        ReportItem(
          title: 'إنشاء تقرير مخصص',
          subtitle: 'إنشاء تقرير حسب المعايير المحددة',
          icon: Icons.add_chart,
          color: Colors.purple,
          onTap: () => _showCustomReportBuilder(),
        ),
        ReportItem(
          title: 'التقارير المحفوظة',
          subtitle: 'عرض التقارير المحفوظة مسبقاً',
          icon: Icons.bookmark,
          color: Colors.teal,
          onTap: () => _showSavedReports(),
        ),
        ReportItem(
          title: 'تصدير البيانات',
          subtitle: 'تصدير البيانات بصيغ مختلفة',
          icon: Icons.file_download,
          color: Colors.indigo,
          onTap: () => _showDataExport(),
        ),
      ],
    );
  }

  /// بناء قسم تقرير
  Widget _buildReportSection({
    required String title,
    required IconData icon,
    required List<ReportItem> reports,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        ...reports.map((report) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: AdminCardWidget(
            title: report.title,
            subtitle: report.subtitle,
            icon: report.icon,
            iconColor: report.color,
            onTap: report.onTap,
          ),
        )),
      ],
    );
  }

  // ===== طرق عرض التقارير =====

  void _showUserActivityReport() {
    Get.dialog(
      AlertDialog(
        title: const Text('تقرير نشاط المستخدمين'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildReportRow('إجمالي المستخدمين', '${_adminController.totalUsers}'),
                _buildReportRow('المستخدمين النشطين', '${_adminController.activeUsers}'),
                _buildReportRow('نسبة النشاط', '${_calculateActivityRate()}%'),
                const Divider(),
                Text(
                  'آخر تحديث: ${DateTime.now().toString().split('.')[0]}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.snackbar('قيد التطوير', 'ستتوفر ميزة التصدير قريباً');
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  void _showUserDistributionReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showNewUsersReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showRoleDistributionReport() {
    Get.dialog(
      AlertDialog(
        title: const Text('توزيع الأدوار'),
        content: SizedBox(
          width: double.maxFinite,
          child: Obx(() {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildReportRow('إجمالي الأدوار', '${_adminController.totalRoles}'),
                _buildReportRow('الأدوار النشطة', '${_adminController.activeRoles}'),
                const Divider(),
                Text(
                  'تفاصيل التوزيع ستتوفر في التحديث القادم',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            );
          }),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showPermissionUsageReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showCustomRolesReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showSystemHealthReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showActivityLogsReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showBackupsReport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showCustomReportBuilder() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showSavedReports() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  void _showDataExport() {
    Get.snackbar('قيد التطوير', 'ستتوفر هذه الميزة في التحديث القادم');
  }

  // ===== طرق مساعدة =====

  Widget _buildReportRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  String _formatLastUpdate(dynamic timestamp) {
    if (timestamp == null) return 'غير محدد';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp as int);
      return '${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  double _calculateActivityRate() {
    final total = _adminController.totalUsers;
    final active = _adminController.activeUsers;
    if (total == 0) return 0.0;
    return (active / total * 100);
  }
}

/// نموذج عنصر التقرير
class ReportItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const ReportItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
