import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/notifications_controller.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../routes/app_routes.dart';

/// قائمة جانبية موحدة للتطبيق
class AppDrawer extends StatelessWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationsController>();
    final currentUser = authController.currentUser.value;

    if (currentUser == null) {
      return const SizedBox.shrink();
    }

    return Drawer(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: <PERSON>View(
          padding: EdgeInsets.zero,
          children: [
            // رأس القائمة
            UserAccountsDrawerHeader(
              accountName: Text(
                currentUser.name,
                style: AppStyles.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              accountEmail: Text(
                currentUser.email,
                style: AppStyles.bodyMedium.copyWith(
                  color: Colors.white,
                ),
              ),
              currentAccountPicture: CircleAvatar(
                backgroundColor: Colors.white,
                child: Text(
                  currentUser.name.isNotEmpty
                      ? currentUser.name[0].toUpperCase()
                      : '?',
                  style: AppStyles.headingLarge.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
              decoration: BoxDecoration(
                color: AppColors.primary,
              ),
            ),

            // البحث الشامل
            ListTile(
              leading: const Icon(Icons.search),
              title: const Text('البحث الشامل'),
              onTap: () {
                Get.back();
                Get.toNamed(AppRoutes.unifiedSearch);
              },
            ),

            // الصفحة الرئيسية
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: Text('home'.tr),
              onTap: () {
                Get.offAllNamed(AppRoutes.home);
              },
            ),

            // المهام
            ListTile(
              leading: const Icon(Icons.task),
              title: Text('tasks'.tr),
              onTap: () {
                Get.back();
                Get.toNamed(AppRoutes.tasks);
              },
            ),

            // الأقسام
            ListTile(
              leading: const Icon(Icons.business),
              title: Text('departments'.tr),
              onTap: () {
                Get.back();
                // التنقل إلى شاشة الأقسام
                Get.toNamed(AppRoutes.departmentDashboard);
                // تغيير التبويب إلى الأقسام (index 2)
                if (Get.isRegistered(tag: 'HomeScreenState')) {
                  final homeScreenState = Get.find(tag: 'HomeScreenState');
                  homeScreenState.changeTab(2);
                }
              },
            ),

            // المحادثات
            ListTile(
              leading: const Icon(Icons.chat),
              title: Text('messages'.tr),
              onTap: () {
                Get.back();
                Get.toNamed(AppRoutes.unifiedChatList);
              },
            ),
   
            // التقارير (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ExpansionTile(
                leading: const Icon(Icons.bar_chart),
                title: Text('reports'.tr),
                children: [
                  // نظام التقارير الجديد
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.dashboard),
                    title: const Text('نظام التقارير الجديد'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.reportingDashboard);
                    },
                  ),
                  // تقارير Monday.com
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.insights),
                    title: const Text('تقارير Monday.com'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.mondayStyleReports);
                    },
                  ),
                  // التقارير الرئيسية
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.summarize),
                    title: const Text('التقارير الرئيسية'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.reports);
                    },
                  ),
                  // التقارير الثابتة
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.description),
                    title: const Text('التقارير الثابتة'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.staticReports);
                    },
                  ),
                  // التقارير المحسنة
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.analytics_outlined),
                    title: const Text('التقارير المحسنة'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.enhancedReports);
                    },
                  ),
                  // الرسوم البيانية المحسنة
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.area_chart),
                    title: const Text('الرسوم البيانية المحسنة'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.enhancedCharts);
                    },
                  ),
                  // تقارير PDF
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.picture_as_pdf),
                    title: const Text('تقارير PDF'),
                    onTap: () {
                      Get.back();
                      Get.toNamed('/task-pdf-report');
                    },
                  ),
                  // نظام التقارير الاحترافية
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.blue.shade400,
                          Colors.blue.shade600,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Get.back();
                          Get.toNamed(AppRoutes.professionalReports);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: const Icon(
                                  Icons.analytics,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  '🚀 التقارير الاحترافية',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Text(
                                  'جديد',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

            // التقويم - تصميم محسن
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withAlpha(179), // 0.7 * 255 = 179
                    AppColors.primary,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withAlpha(77), // 0.3 * 255 = 77
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    Get.back();
                    Get.toNamed(AppRoutes.calendar);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.calendar_month,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'التقويم',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              _buildCalendarEventsText(),
                            ],
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // لوحة المعلومات
            ExpansionTile(
              leading: const Icon(Icons.dashboard_customize),
              title: const Text('لوحة المعلومات'),
              children: [
                // لوحة المعلومات القابلة للتخصيص
                ListTile(
                  contentPadding: const EdgeInsets.only(right: 32),
                  leading: const Icon(Icons.dashboard),
                  title: const Text('لوحة المعلومات التقليدية'),
                  onTap: () {
                    Get.back();
                    Get.toNamed(AppRoutes.customizableDashboard);
                  },
                ),
                // لوحة المعلومات الجديدة
                ListTile(
                  contentPadding: const EdgeInsets.only(right: 32),
                  leading: const Icon(Icons.dashboard_customize),
                  title: const Text('لوحة المعلومات الجديدة'),
                  onTap: () {
                    Get.back();
                    Get.toNamed(AppRoutes.newDashboard);
                  },
                ),
                // لوحة معلومات Monday.com
                ListTile(
                  contentPadding: const EdgeInsets.only(right: 32),
                  leading: const Icon(Icons.view_module),
                  title: const Text('لوحة معلومات Monday.com'),
                  onTap: () {
                    Get.back();
                    Get.toNamed(AppRoutes.mondayDashboard);
                  },
                ),
                // لوحة معلومات المهام
                ListTile(
                  contentPadding: const EdgeInsets.only(right: 32),
                  leading: const Icon(Icons.task_outlined),
                  title: const Text('لوحة معلومات المهام'),
                  onTap: () {
                    Get.back();
                    Get.toNamed(AppRoutes.taskStats);
                  },
                ),
              ],
            ),

            // باور بي آي (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ExpansionTile(
                leading: const Icon(Icons.analytics),
                title: const Text('باور بي آي'),
                children: [
                  // تقارير باور بي آي
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.dashboard),
                    title: const Text('تقارير باور بي آي'),
                    onTap: () {
                      // Get.back();
                      Get.toNamed(AppRoutes.powerBI);
                    },
                  ),
                  // التقارير الديناميكية
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.auto_graph),
                    title: const Text('التقارير الديناميكية'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.dynamicPowerBI);
                    },
                  ),
                ],
              ),

            // الإشعارات
            ListTile(
              leading: Stack(
                alignment: Alignment.center,
                children: [
                  const Icon(Icons.notifications),
                  Obx(() {
                    if (notificationController.unreadCount > 0) {
                      return Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(1),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 10,
                            minHeight: 10,
                          ),
                          child: Text(
                            notificationController.unreadCount > 9
                                ? '9+'
                                : notificationController.unreadCount.toString(),
                            style: TextStyle(
                              color:
                                  Get.isDarkMode ? Colors.black : Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      );
                    } else {
                      return const SizedBox.shrink();
                    }
                  }),
                ],
              ),
              title: Text('notifications'.tr),
              onTap: () {
                Get.back();
                // التنقل إلى شاشة الإشعارات
                Get.toNamed(AppRoutes.notifications);
                // تغيير التبويب إلى الإشعارات (index 5)
                if (Get.isRegistered(tag: 'HomeScreenState')) {
                  final homeScreenState = Get.find(tag: 'HomeScreenState');
                  homeScreenState.changeTab(5);
                }
              },
            ),

            // المكونات المتقدمة (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ListTile(
                leading: const Icon(Icons.widgets),
                title: const Text('المكونات المتقدمة'),
                onTap: () {
                  Get.back();
                  Get.toNamed('/syncfusion-showcase');
                },
              ),

            // المستندات النصية (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ExpansionTile(
                leading: const Icon(Icons.description),
                title: const Text('المستندات النصية'),
                children: [
                  // قائمة المستندات
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.list),
                    title: const Text('قائمة المستندات'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.textDocumentsList);
                    },
                  ),
                  // إنشاء مستند جديد
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.add),
                    title: const Text('إنشاء مستند جديد'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.textDocumentEditor);
                    },
                  ),
                ],
              ),

            // نظام الأرشفة الإلكترونية (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ExpansionTile(
                leading: const Icon(Icons.archive),
                title: const Text('الأرشفة الإلكترونية'),
                children: [
                  // الصفحة الرئيسية للأرشيف
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.home),
                    title: const Text('الرئيسية'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.archiveHome);
                    },
                  ),
                  // تصفح الوثائق
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.search),
                    title: const Text('تصفح الوثائق'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.documentBrowser);
                    },
                  ),
                  // رفع وثيقة جديدة
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.upload_file),
                    title: const Text('رفع وثيقة جديدة'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.documentUpload);
                    },
                  ),
                  // إدارة التصنيفات
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.category),
                    title: const Text('إدارة التصنيفات'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.categoryManagement);
                    },
                  ),
                  // إدارة الوسوم
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.local_offer),
                    title: const Text('إدارة الوسوم'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.tagManagement);
                    },
                  ),
                ],
              ),

            const Divider(),

            // إعدادات التزامن (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ListTile(
                leading: const Icon(Icons.sync),
                title: const Text('إعدادات التزامن'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.syncSettings);
                },
              ),

          
            // لوحة التحكم الإدارية (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ListTile(
                leading: const Icon(Icons.admin_panel_settings),
                title: Text('adminDashboard'.tr),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.admin);
                },
              ),


            // إصلاح قاعدة البيانات (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ListTile(
                leading: const Icon(Icons.build),
                title: const Text('إصلاح قاعدة البيانات'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.databaseRepair);
                },
              ),

            // تشخيص المشاكل (للمديرين أو kha)
            if (authController.canSeeAllPages)
              ListTile(
                leading: const Icon(Icons.medical_services),
                title: const Text('تشخيص المشاكل'),
                onTap: () {
                  Get.back();
                  Get.toNamed(AppRoutes.diagnostics);
                },
              ),

         
              ExpansionTile(
                leading: const Icon(Icons.science, color: Colors.orange),
                title: const Text('🧪 أدوات الاختبار'),
                children: [
                  // اختبار نظام التخزين المحسن
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.cloud_upload, color: Colors.blue),
                    title: const Text('نظام التخزين المحسن'),
                    subtitle: const Text('اختبار رفع الملفات والإحصائيات'),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.fileStorageTest);
                    },
                  ),
                 
                  // اختبار الصلاحيات
                  ListTile(
                    contentPadding: const EdgeInsets.only(right: 32),
                    leading: const Icon(Icons.security, color: Colors.purple),
                    title: const Text('🔐 اختبار الصلاحيات'),
                    subtitle: const Text('اختبار الصلاحيات الـ 71 الجديدة'),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        'جديد',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    onTap: () {
                      Get.back();
                      Get.toNamed(AppRoutes.permissionsTest);
                    },
                  ),
                ],
              ),

            const Divider(),

            // تسجيل الخروج
            ListTile(
              leading: const Icon(Icons.logout),
              title: Text('logout'.tr),
              onTap: () async {
                Get.back();
                await authController.logout();
                Get.offAllNamed(AppRoutes.login);
              },
            ),

          
             
             
          
          ],
        ),
      ),
    );
  }

  /// بناء نص أحداث التقويم مع معالجة آمنة للأخطاء
  Widget _buildCalendarEventsText() {
    // التحقق من وجود متحكم التقويم
    if (!Get.isRegistered<app_calendar.CalendarController>()) {
      return const Text(
        'استكشف التقويم',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      );
    }

    try {
      final calendarController = Get.find<app_calendar.CalendarController>();

      // استخدام Obx فقط مع المتغيرات التفاعلية
      return Obx(() {
        final today = DateTime.now();
        // الوصول إلى القائمة التفاعلية للأحداث
        final allEvents = calendarController.events;
        final todayEvents = allEvents.where((event) {
          final eventDate = event.startDateTime;
          return eventDate.year == today.year &&
                 eventDate.month == today.month &&
                 eventDate.day == today.day;
        }).toList();

        return Text(
          todayEvents.isNotEmpty
              ? '${todayEvents.length} أحداث اليوم'
              : 'لا توجد أحداث اليوم',
          style: TextStyle(
            color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
            fontSize: 12,
          ),
        );
      });
    } catch (e) {
      // في حالة حدوث خطأ
      return const Text(
        'استكشف التقويم',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
        ),
      );
    }
  }
}
