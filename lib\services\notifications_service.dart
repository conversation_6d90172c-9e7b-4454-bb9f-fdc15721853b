import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

class NotificationsService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();
  static bool _initialized = false;

  static Future<void> initialize(BuildContext context) async {
    if (_initialized) return;
    // تهيئة بسيطة تناسب ويندوز (بدون إعدادات خاصة)
    final InitializationSettings initializationSettings = InitializationSettings();
    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        // يمكنك هنا توجيه المستخدم إلى شاشة معينة عند الضغط على الإشعار
      },
    );
    _initialized = true;
  }

  static Future<void> showSimpleNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_initialized) {
      throw Exception('NotificationsService not initialized. Call initialize() first.');
    }
    // NotificationDetails مع صوت افتراضي لجميع الأنظمة المدعومة
    const NotificationDetails notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails(
        'default_channel_id',
        'الإشعارات',
        channelDescription: 'إشعارات النظام',
        importance: Importance.max,
        priority: Priority.high,
        playSound: true,
      ),
      iOS: DarwinNotificationDetails(
        presentSound: true,
      ),
      // لا يوجد windows هنا، الصوت الافتراضي سيعمل في ويندوز إذا كان النظام يسمح بذلك
    );
    await _notificationsPlugin.show(
      0,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }
}
