import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:signalr_netcore/signalr_client.dart';
import '../models/message_models.dart';
import '../models/task_comment_models.dart';
import '../utils/logger.dart';
import '../controllers/auth_controller.dart';
import '../controllers/notifications_controller.dart';
import '../controllers/task_messages_controller.dart';
import '../controllers/unified_chat_controller.dart';
import '../controllers/notification_settings_controller.dart';
import '../services/notifications_service.dart';

/// خدمة SignalR موحدة لجميع أنواع الاتصالات الفورية
/// تدعم: المحادثات العامة، رسائل المهام، تعليقات المهام
class UnifiedSignalRService extends GetxService {
  // اتصالات Hub متعددة
  HubConnection? _chatHubConnection;
  HubConnection? _taskCommentsHubConnection;
  HubConnection? _notificationHubConnection;
  
  // URLs للـ Hubs
  final String _chatHubUrl = "https://localhost:7111/chatHub";
  final String _taskCommentsHubUrl = "https://localhost:7111/taskCommentsHub";
  final String _notificationHubUrl = "https://localhost:7111/notificationHub";
  
  // حالة الاتصالات
  final RxBool _isChatHubConnected = false.obs;
  final RxBool _isTaskCommentsHubConnected = false.obs;
  final RxBool _isNotificationHubConnected = false.obs;
  
  // متغيرات للمحادثات العامة
  final Rx<Message?> _newMessage = Rx<Message?>(null);
  
  // متغيرات لتعليقات المهام
  final RxList<TaskComment> _newComments = <TaskComment>[].obs;
  final RxList<TaskComment> _updatedComments = <TaskComment>[].obs;
  final RxList<int> _deletedCommentIds = <int>[].obs;
  
  // متغيرات لرسائل المهام
  final RxList<Map<String, dynamic>> _newTaskMessages = <Map<String, dynamic>>[].obs;
  
  // متغيرات للإشعارات
  final RxList<Map<String, dynamic>> _newNotifications = <Map<String, dynamic>>[].obs;
  final RxInt _unreadNotificationsCount = 0.obs;
  
  // Timers لإعادة الاتصال والتحديث الدوري
  Timer? _chatReconnectTimer;
  Timer? _commentsReconnectTimer;
  Timer? _notificationReconnectTimer;
  Timer? _periodicUpdateTimer;
  
  // متغيرات إعادة الاتصال
  bool _isChatReconnecting = false;
  bool _isCommentsReconnecting = false;
  bool _isNotificationReconnecting = false;
  int _chatReconnectAttempts = 0;
  int _commentsReconnectAttempts = 0;
  int _notificationReconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;

  // Getters للحالة
  bool get isChatHubConnected => _isChatHubConnected.value;
  bool get isTaskCommentsHubConnected => _isTaskCommentsHubConnected.value;
  bool get isNotificationHubConnected => _isNotificationHubConnected.value;
  bool get isFullyConnected => isChatHubConnected && isTaskCommentsHubConnected && isNotificationHubConnected;
  
  // Getters للبيانات
  Rx<Message?> get newMessageStream => _newMessage;
  List<TaskComment> get newComments => _newComments;
  List<TaskComment> get updatedComments => _updatedComments;
  List<int> get deletedCommentIds => _deletedCommentIds;
  List<Map<String, dynamic>> get newTaskMessages => _newTaskMessages;
  List<Map<String, dynamic>> get newNotifications => _newNotifications;
  int get unreadNotificationsCount => _unreadNotificationsCount.value;
  
  // Getters للاتصالات (للتوافق مع الكود الحالي)
  HubConnection? get hubConnection => _chatHubConnection;
  HubConnection? get chatHubConnection => _chatHubConnection;
  HubConnection? get taskCommentsHubConnection => _taskCommentsHubConnection;

  @override
  void onInit() {
    super.onInit();
    _initializeConnections();
  }

  @override
  void onClose() {
    _cleanup();
    super.onClose();
  }

  /// تهيئة جميع الاتصالات
  Future<void> _initializeConnections() async {
    debugPrint('🔗 بدء تهيئة خدمة SignalR الموحدة...');

    // تهيئة اتصال المحادثات العامة
    await _initializeChatHub();

    // تهيئة اتصال تعليقات المهام
    await _initializeTaskCommentsHub();
    
    // تهيئة اتصال الإشعارات
    await _initializeNotificationHub();

    // بدء فحص دوري للاتصال (كل دقيقة)
    _startPeriodicConnectionCheck();

    debugPrint('✅ تم تهيئة خدمة SignalR الموحدة');
  }

  /// بدء فحص دوري لحالة الاتصال (كل دقيقة)
  void _startPeriodicConnectionCheck() {
    _periodicUpdateTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkAndReconnectIfNeeded();
    });
  }

  /// فحص الاتصال وإعادة الاتصال إذا لزم الأمر
  void _checkAndReconnectIfNeeded() {
    // فحص Chat Hub
    if (_chatHubConnection?.state != HubConnectionState.Connected && !_isChatReconnecting) {
      debugPrint('🔄 إعادة اتصال Chat Hub...');
      _reconnectChatHub();
    }

    // فحص Task Comments Hub
    if (_taskCommentsHubConnection?.state != HubConnectionState.Connected && !_isCommentsReconnecting) {
      debugPrint('🔄 إعادة اتصال Task Comments Hub...');
      _reconnectTaskCommentsHub();
    }
    
    // فحص Notification Hub
    if (_notificationHubConnection?.state != HubConnectionState.Connected && !_isNotificationReconnecting) {
      debugPrint('🔄 إعادة اتصال Notification Hub...');
      _reconnectNotificationHub();
    }
  }

  /// تهيئة Hub المحادثات العامة مع معالجة أخطاء محسنة
  Future<void> _initializeChatHub() async {
    try {
      debugPrint('🔗 تهيئة Chat Hub: $_chatHubUrl');

      _chatHubConnection = HubConnectionBuilder()
          .withUrl(_chatHubUrl)
          .build();

      _setupChatHubEventHandlers();
      await _connectChatHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Chat Hub: $e');
      // إعادة المحاولة بعد تأخير
      Timer(const Duration(seconds: 5), () => _initializeChatHub());
    }
  }

  /// تهيئة Hub تعليقات المهام مع معالجة أخطاء محسنة
  Future<void> _initializeTaskCommentsHub() async {
    try {
      debugPrint('🔗 تهيئة Task Comments Hub: $_taskCommentsHubUrl');

      _taskCommentsHubConnection = HubConnectionBuilder()
          .withUrl(_taskCommentsHubUrl)
          .build();

      _setupTaskCommentsHubEventHandlers();
      await _connectTaskCommentsHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Task Comments Hub: $e');
      // إعادة المحاولة بعد تأخير
      Timer(const Duration(seconds: 5), () => _initializeTaskCommentsHub());
    }
  }
  
  /// تهيئة Hub الإشعارات مع معالجة أخطاء محسنة
  Future<void> _initializeNotificationHub() async {
    try {
      debugPrint('🔗 تهيئة Notification Hub: $_notificationHubUrl');

      _notificationHubConnection = HubConnectionBuilder()
          .withUrl(_notificationHubUrl)
          .build();

      _setupNotificationHubEventHandlers();
      await _connectNotificationHub();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Notification Hub: $e');
      // إعادة المحاولة بعد تأخير
      Timer(const Duration(seconds: 5), () => _initializeNotificationHub());
    }
  }

  /// إعداد معالجات أحداث Chat Hub
  void _setupChatHubEventHandlers() {
    if (_chatHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _chatHubConnection!.onclose((error) {
      _isChatHubConnected.value = false;
      AppLogger.error("Chat Hub Connection closed: $error");

      if (!_isChatReconnecting) {
        _reconnectChatHub();
      }
    });

    // معالجة أخطاء SignalR
    _chatHubConnection!.on("Error", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final errorData = arguments[0];
          AppLogger.error("خطأ من SignalR Hub: $errorData");

          // يمكن إضافة معالجة خاصة للأخطاء هنا
          if (errorData is Map<String, dynamic>) {
            final message = errorData['Message'] ?? 'خطأ غير معروف';
            final groupId = errorData['GroupId'];
            AppLogger.error("خطأ في المجموعة $groupId: $message");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة رسالة الخطأ من SignalR: $e");
      }
    });

    // معالجة أخطاء إرسال الرسائل
    _chatHubConnection!.on("MessageSendError", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final errorData = arguments[0];
          AppLogger.error("خطأ في إرسال الرسالة: $errorData");
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة خطأ إرسال الرسالة: $e");
      }
    });

    // استقبال الرسائل العامة
    _chatHubConnection!.on("ReceiveMessage", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
          final messageData = arguments[0];

          // التحقق من نوع البيانات
          if (messageData is Map<String, dynamic>) {
            // التحقق من وجود البيانات المطلوبة
            if (messageData.containsKey('id') &&
                messageData.containsKey('groupId') &&
                messageData.containsKey('senderId') &&
                messageData.containsKey('content') &&
                messageData.containsKey('createdAt')) {

              final message = Message.fromJson(messageData);
              _newMessage.value = message;
              AppLogger.debug("تم استقبال رسالة جديدة: ${message.id}");
            } else {
              AppLogger.warning("رسالة مستقبلة تحتوي على بيانات ناقصة: $messageData");
            }
          } else {
            AppLogger.warning("نوع بيانات الرسالة غير صحيح: ${messageData.runtimeType}");
          }
        } else {
          AppLogger.warning("تم استقبال رسالة فارغة أو null");
        }
      } catch (e, stackTrace) {
        AppLogger.error("خطأ في معالجة الرسالة الجديدة: $e");
        AppLogger.error("Stack trace: $stackTrace");
      }
    });

    // استقبال رسائل المهام مع تحديث فوري
    _chatHubConnection!.on("ReceiveTaskMessage", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final messageData = arguments[0] as Map<String, dynamic>;

          // إضافة الرسالة للقائمة
          _newTaskMessages.add(messageData);

          // تحديث فوري للمتحكمات
          _notifyMessagesControllers(messageData);

          AppLogger.debug("تم استقبال رسالة مهمة جديدة فورياً: ${messageData['id']}");
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة رسالة المهمة: $e");
      }
    });

    // باقي أحداث Chat Hub
    _setupChatHubAdditionalEvents();
  }

  /// إعداد أحداث إضافية لـ Chat Hub
  void _setupChatHubAdditionalEvents() {
    if (_chatHubConnection == null) return;

    // مستمع لتحديثات المهام
    _chatHubConnection!.on("TaskUpdated", (arguments) {
      AppLogger.debug("Task Updated: $arguments");
    });

    // مستمع لتحديثات حالة المهام
    _chatHubConnection!.on("TaskStatusUpdated", (arguments) {
      if (arguments != null && arguments.length >= 2) {
        int taskId = arguments[0];
        String newStatus = arguments[1];
        AppLogger.debug("Task $taskId status updated to $newStatus");
      }
    });

    // مستمع لإشعارات الكتابة في المهام
    _chatHubConnection!.on("UserTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final taskId = int.tryParse(data['TaskId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (taskId != null && userName.isNotEmpty) {
            _notifyTaskTypingStarted(taskId, userName);
            AppLogger.debug("User $userName started typing in Task: $taskId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار الكتابة في المهمة: $e");
      }
    });

    // مستمع لإشعارات توقف الكتابة في المهام
    _chatHubConnection!.on("UserStoppedTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final taskId = int.tryParse(data['TaskId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (taskId != null && userName.isNotEmpty) {
            _notifyTaskTypingStopped(taskId, userName);
            AppLogger.debug("User $userName stopped typing in Task: $taskId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار توقف الكتابة في المهمة: $e");
      }
    });

    // مستمع لإشعارات الكتابة في المجموعات
    _chatHubConnection!.on("GroupUserTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final groupId = int.tryParse(data['GroupId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (groupId != null && userName.isNotEmpty) {
            _notifyGroupTypingStarted(groupId, userName);
            AppLogger.debug("User $userName started typing in Group: $groupId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار الكتابة في المجموعة: $e");
      }
    });

    // مستمع لإشعارات توقف الكتابة في المجموعات
    _chatHubConnection!.on("GroupUserStoppedTyping", (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final groupId = int.tryParse(data['GroupId']?.toString() ?? '');
          final userName = data['UserName']?.toString() ?? '';

          if (groupId != null && userName.isNotEmpty) {
            _notifyGroupTypingStopped(groupId, userName);
            AppLogger.debug("User $userName stopped typing in Group: $groupId");
          }
        }
      } catch (e) {
        AppLogger.error("خطأ في معالجة إشعار توقف الكتابة في المجموعة: $e");
      }
    });

    // مستمع لقراءة الرسائل
    _chatHubConnection!.on("MessageRead", (arguments) {
      AppLogger.debug("Message Read: $arguments");
    });

    // مستمع لقراءة جميع رسائل المجموعة
    _chatHubConnection!.on("GroupMessagesRead", (arguments) {
      AppLogger.debug("Group Messages Read: $arguments");
    });
  }

  /// إعداد معالجات أحداث Task Comments Hub
  void _setupTaskCommentsHubEventHandlers() {
    if (_taskCommentsHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _taskCommentsHubConnection!.onclose((error) {
      _isTaskCommentsHubConnected.value = false;
      debugPrint('🔌 انقطع الاتصال مع Task Comments Hub: $error');
      
      if (!_isCommentsReconnecting) {
        _reconnectTaskCommentsHub();
      }
    });

    // استقبال تعليق جديد مع تحديث فوري
    _taskCommentsHubConnection!.on('ReceiveTaskComment', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final commentData = arguments[0] as Map<String, dynamic>;
          final comment = TaskComment.fromJson(commentData);

          // إضافة التعليق للقائمة
          _newComments.add(comment);

          // تحديث فوري للمتحكمات
          _notifyCommentsControllers(comment);

          debugPrint('📨 تم استقبال تعليق جديد فورياً: ${comment.id}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تعليق جديد: $e');
      }
    });

    // استقبال تحديث تعليق
    _taskCommentsHubConnection!.on('TaskCommentUpdated', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final commentData = arguments[0] as Map<String, dynamic>;
          final comment = TaskComment.fromJson(commentData);
          _updatedComments.add(comment);
          debugPrint('✏️ تم استقبال تحديث تعليق: ${comment.id}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تحديث تعليق: $e');
      }
    });

    // استقبال حذف تعليق
    _taskCommentsHubConnection!.on('TaskCommentDeleted', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final commentId = data['CommentId'] as int;
          _deletedCommentIds.add(commentId);
          debugPrint('🗑️ تم استقبال حذف تعليق: $commentId');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة حذف تعليق: $e');
      }
    });
  }
  
  /// إعداد معالجات أحداث Notification Hub
  void _setupNotificationHubEventHandlers() {
    if (_notificationHubConnection == null) return;
    
    // معالجة إغلاق الاتصال
    _notificationHubConnection!.onclose((error) {
      _isNotificationHubConnected.value = false;
      debugPrint('🔌 انقطع الاتصال مع Notification Hub: $error');
      
      if (!_isNotificationReconnecting) {
        _reconnectNotificationHub();
      }
    });

    // استقبال إشعار جديد
    _notificationHubConnection!.on('ReceiveNotification', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final notificationData = arguments[0] as Map<String, dynamic>;
          
          // إضافة الإشعار للقائمة
          _newNotifications.add(notificationData);
          
          // تحديث عدد الإشعارات غير المقروءة
          if (notificationData['isRead'] == false) {
            _unreadNotificationsCount.value++;
          }
          
          // إشعار المتحكمات بالإشعار الجديد
          _notifyNotificationControllers(notificationData);
          
          debugPrint('📨 تم استقبال إشعار جديد: ${notificationData['id']}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة إشعار جديد: $e');
      }
    });

    // تحديث عدد الإشعارات غير المقروءة
    _notificationHubConnection!.on('UnreadCountUpdated', (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          final count = data['Count'] as int;
          _unreadNotificationsCount.value = count;
          debugPrint('🔢 تم تحديث عدد الإشعارات غير المقروءة: $count');
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة تحديث عدد الإشعارات غير المقروءة: $e');
      }
    });
  }

  /// الاتصال بـ Chat Hub
  Future<void> _connectChatHub() async {
    if (_chatHubConnection?.state == HubConnectionState.Connected) {
      _isChatHubConnected.value = true;
      return;
    }

    try {
      await _chatHubConnection?.start();
      _isChatHubConnected.value = true;
      _chatReconnectAttempts = 0;
      _isChatReconnecting = false;
      AppLogger.debug("Chat Hub Connection started successfully");
    } catch (e) {
      _isChatHubConnected.value = false;
      AppLogger.error("Error starting Chat Hub connection: $e");
      _reconnectChatHub();
    }
  }

  /// الاتصال بـ Task Comments Hub
  Future<void> _connectTaskCommentsHub() async {
    if (_taskCommentsHubConnection?.state == HubConnectionState.Connected) {
      _isTaskCommentsHubConnected.value = true;
      return;
    }

    try {
      await _taskCommentsHubConnection?.start();
      _isTaskCommentsHubConnected.value = true;
      _commentsReconnectAttempts = 0;
      _isCommentsReconnecting = false;
      debugPrint("✅ Task Comments Hub Connection started successfully");
    } catch (e) {
      _isTaskCommentsHubConnected.value = false;
      debugPrint("❌ Error starting Task Comments Hub connection: $e");
      _reconnectTaskCommentsHub();
    }
  }
  
  /// الاتصال بـ Notification Hub
  Future<void> _connectNotificationHub() async {
    if (_notificationHubConnection?.state == HubConnectionState.Connected) {
      _isNotificationHubConnected.value = true;
      return;
    }

    try {
      await _notificationHubConnection?.start();
      _isNotificationHubConnected.value = true;
      _notificationReconnectAttempts = 0;
      _isNotificationReconnecting = false;
      debugPrint("✅ Notification Hub Connection started successfully");
      
      // الانضمام لمجموعة الإشعارات الخاصة بالمستخدم الحالي
      await _joinUserNotificationGroup();
    } catch (e) {
      _isNotificationHubConnected.value = false;
      debugPrint("❌ Error starting Notification Hub connection: $e");
      _reconnectNotificationHub();
    }
  }

  /// إعادة الاتصال بـ Chat Hub
  void _reconnectChatHub() {
    if (_isChatReconnecting || _chatReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isChatReconnecting = true;
    _chatReconnectAttempts++;

    final delay = Duration(seconds: _chatReconnectAttempts * 2);
    _chatReconnectTimer = Timer(delay, () async {
      AppLogger.debug("محاولة إعادة الاتصال بـ Chat Hub (المحاولة $_chatReconnectAttempts)");
      await _connectChatHub();
    });
  }

  /// إعادة الاتصال بـ Task Comments Hub
  void _reconnectTaskCommentsHub() {
    if (_isCommentsReconnecting || _commentsReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isCommentsReconnecting = true;
    _commentsReconnectAttempts++;

    final delay = Duration(seconds: _commentsReconnectAttempts * 2);
    _commentsReconnectTimer = Timer(delay, () async {
      debugPrint("محاولة إعادة الاتصال بـ Task Comments Hub (المحاولة $_commentsReconnectAttempts)");
      await _connectTaskCommentsHub();
    });
  }
  
  /// إعادة الاتصال بـ Notification Hub
  void _reconnectNotificationHub() {
    if (_isNotificationReconnecting || _notificationReconnectAttempts >= _maxReconnectAttempts) {
      return;
    }

    _isNotificationReconnecting = true;
    _notificationReconnectAttempts++;

    final delay = Duration(seconds: _notificationReconnectAttempts * 2);
    _notificationReconnectTimer = Timer(delay, () async {
      debugPrint("محاولة إعادة الاتصال بـ Notification Hub (المحاولة $_notificationReconnectAttempts)");
      await _connectNotificationHub();
    });
  }

  /// الانضمام لمجموعة الإشعارات الخاصة بالمستخدم الحالي
  Future<void> _joinUserNotificationGroup() async {
    try {
      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final userId = authController.currentUser.value?.id;
      
      if (userId == null) {
        debugPrint('⚠️ لا يمكن الانضمام لمجموعة الإشعارات: المستخدم غير مسجل الدخول');
        return;
      }
      
      // الانضمام لمجموعة الإشعارات
      await _notificationHubConnection?.invoke('JoinUserNotificationGroup', args: [userId.toString()]);
      debugPrint('👥 تم الانضمام لمجموعة إشعارات المستخدم: $userId');
      
      // تحديث عدد الإشعارات غير المقروءة
      await _updateUnreadNotificationsCount(userId);
    } catch (e) {
      debugPrint('❌ خطأ في الانضمام لمجموعة الإشعارات: $e');
    }
  }
  
  /// تحديث عدد الإشعارات غير المقروءة
  Future<void> _updateUnreadNotificationsCount(int userId) async {
    try {
      await _notificationHubConnection?.invoke('UpdateUnreadCount', args: [userId.toString()]);
      debugPrint('🔄 تم طلب تحديث عدد الإشعارات غير المقروءة للمستخدم: $userId');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث عدد الإشعارات غير المقروءة: $e');
    }
  }

  /// تنظيف الموارد
  void _cleanup() {
    // إلغاء وتنظيف المؤقتات
    _chatReconnectTimer?.cancel();
    _chatReconnectTimer = null;
    _commentsReconnectTimer?.cancel();
    _commentsReconnectTimer = null;
    _notificationReconnectTimer?.cancel();
    _notificationReconnectTimer = null;
    _periodicUpdateTimer?.cancel();
    _periodicUpdateTimer = null;

    // إيقاف الاتصالات
    _chatHubConnection?.stop();
    _taskCommentsHubConnection?.stop();

    // مسح القوائم
    _newComments.clear();
    _updatedComments.clear();
    _deletedCommentIds.clear();
    _newTaskMessages.clear();
  }

  // ==================== وظائف المحادثات العامة ====================

  /// الانضمام لمجموعة محادثة
  Future<void> joinChatGroup(String groupId) async {
    try {
      // التحقق من صحة معرف المجموعة
      if (groupId.isEmpty) {
        AppLogger.warning('محاولة انضمام بمعرف مجموعة فارغ');
        return;
      }

      // التأكد من الاتصال
      if (!isChatHubConnected) {
        AppLogger.debug('Chat Hub غير متصل، محاولة الاتصال...');
        await _connectChatHub();

        // انتظار قصير للتأكد من الاتصال
        await Future.delayed(const Duration(milliseconds: 500));

        if (!isChatHubConnected) {
          AppLogger.error('فشل في الاتصال بـ Chat Hub');
          return;
        }
      }

      // محاولة الانضمام مع معالجة الأخطاء
      await _chatHubConnection?.invoke("JoinChatGroup", args: [groupId]);
      AppLogger.debug('تم الانضمام للمجموعة: $groupId');
    } catch (e) {
      AppLogger.error('خطأ في الانضمام للمجموعة $groupId: $e');

      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        AppLogger.debug('تم تجاهل خطأ null subtype في الانضمام للمجموعة');
        return;
      }

      // محاولة إعادة الاتصال في حالة فشل الانضمام
      if (e.toString().contains('connection') || e.toString().contains('Connection')) {
        AppLogger.debug('محاولة إعادة الاتصال بسبب خطأ في الاتصال...');
        _reconnectChatHub();
      }
    }
  }

  /// مغادرة مجموعة محادثة
  Future<void> leaveChatGroup(String groupId) async {
    try {
      await _chatHubConnection?.invoke("LeaveChatGroup", args: [groupId]);
      AppLogger.debug('تم مغادرة المجموعة: $groupId');
    } catch (e) {
      AppLogger.error('خطأ في مغادرة المجموعة $groupId: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// إرسال رسالة للمجموعة
  Future<void> sendMessageToHub(String groupId, Message message) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال الرسالة");
        return;
      }

      await _chatHubConnection?.invoke("SendMessageToHub",
          args: [groupId, message.toJson()]);
      AppLogger.debug('تم إرسال رسالة للمجموعة $groupId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال رسالة للمجموعة $groupId: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  // ==================== وظائف رسائل المهام ====================

  /// الانضمام لمحادثة مهمة
  Future<void> joinTaskGroup(String taskId) async {
    try {
      if (!isChatHubConnected) {
        await _connectChatHub();
      }

      await _chatHubConnection?.invoke("JoinTaskGroup", args: [taskId]);
      AppLogger.debug('تم الانضمام لمحادثة المهمة: $taskId');
    } catch (e) {
      AppLogger.error('خطأ في الانضمام لمحادثة المهمة $taskId: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// مغادرة محادثة مهمة
  Future<void> leaveTaskGroup(String taskId) async {
    try {
      await _chatHubConnection?.invoke("LeaveTaskGroup", args: [taskId]);
      AppLogger.debug('تم مغادرة محادثة المهمة: $taskId');
    } catch (e) {
      AppLogger.error('خطأ في مغادرة محادثة المهمة $taskId: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// إرسال رسالة مهمة
  Future<void> sendTaskMessage(String taskId, Map<String, dynamic> message) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال رسالة المهمة");
        return;
      }

      await _chatHubConnection?.invoke("SendTaskMessage", args: [taskId, message]);
      AppLogger.debug('تم إرسال رسالة للمهمة $taskId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال رسالة للمهمة $taskId: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// إرسال إشعار الكتابة
  Future<void> sendTypingIndicator(String taskId, String userName) async {
    try {
      await _chatHubConnection?.invoke("UserTyping", args: [taskId, userName]);
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار الكتابة: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// إرسال إشعار توقف الكتابة
  Future<void> sendStoppedTypingIndicator(String taskId, String userName) async {
    try {
      await _chatHubConnection?.invoke("UserStoppedTyping", args: [taskId, userName]);
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار توقف الكتابة: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  // ==================== وظائف المجموعات العامة ====================

  /// إرسال إشعار الكتابة للمجموعة العامة
  Future<void> sendGroupTypingIndicator(String groupId, String userName) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال إشعار الكتابة للمجموعة");
        return;
      }

      await _chatHubConnection?.invoke("GroupUserTyping", args: [groupId, userName]);
      AppLogger.debug('تم إرسال إشعار بدء الكتابة للمجموعة $groupId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار الكتابة للمجموعة: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }

  /// إرسال إشعار توقف الكتابة للمجموعة العامة
  Future<void> sendGroupStoppedTypingIndicator(String groupId, String userName) async {
    try {
      if (!isChatHubConnected) {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن إرسال إشعار توقف الكتابة للمجموعة");
        return;
      }

      await _chatHubConnection?.invoke("GroupUserStoppedTyping", args: [groupId, userName]);
      AppLogger.debug('تم إرسال إشعار توقف الكتابة للمجموعة $groupId');
    } catch (e) {
      AppLogger.error('خطأ في إرسال إشعار توقف الكتابة للمجموعة: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        return;
      }
    }
  }



  // ==================== وظائف تعليقات المهام ====================

  /// الانضمام لمجموعة تعليقات مهمة
  Future<void> joinTaskCommentsGroup(int taskId) async {
    try {
      if (!isTaskCommentsHubConnected) {
        await _connectTaskCommentsHub();
      }

      // استخدام invoke مع معالجة أفضل للأخطاء
      await _taskCommentsHubConnection?.invoke('JoinTaskCommentsGroup',
          args: [taskId.toString()]);
      debugPrint('👥 تم الانضمام لمجموعة تعليقات المهمة: $taskId');
    } catch (e) {
      debugPrint('❌ خطأ في الانضمام لمجموعة التعليقات: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        rethrow;
      }
    }
  }

  /// مغادرة مجموعة تعليقات مهمة
  Future<void> leaveTaskCommentsGroup(int taskId) async {
    try {
      await _taskCommentsHubConnection?.invoke('LeaveTaskCommentsGroup',
          args: [taskId.toString()]);
      debugPrint('👋 تم مغادرة مجموعة تعليقات المهمة: $taskId');
    } catch (e) {
      debugPrint('❌ خطأ في مغادرة مجموعة التعليقات: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        rethrow;
      }
    }
  }

  /// إرسال تعليق جديد
  Future<void> sendTaskComment(int taskId, TaskComment comment) async {
    try {
      if (!isTaskCommentsHubConnected) {
        debugPrint('⚠️ Task Comments Hub غير متصل، لا يمكن إرسال التعليق');
        return;
      }

      await _taskCommentsHubConnection?.invoke('SendTaskComment', args: [
        taskId.toString(),
        comment.toJson()
      ]);
      debugPrint('📤 تم إرسال تعليق عبر SignalR: ${comment.id}');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال التعليق: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (!e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        rethrow;
      }
    }
  }

  // ==================== وظائف التحديث الفوري ====================

  /// إشعار متحكمات التعليقات بتعليق جديد
  void _notifyCommentsControllers(TaskComment comment) {
    try {
      // تحديث فوري عبر GetX
      Get.find<GetxController>().update();
      debugPrint('تم إشعار المتحكمات بتعليق جديد: ${comment.id}');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم التعليقات: $e');
    }
  }

  /// إشعار متحكمات الرسائل برسالة جديدة
  void _notifyMessagesControllers(Map<String, dynamic> messageData) {
    try {
      // تحديث فوري عبر GetX
      Get.find<GetxController>().update();
      debugPrint('تم إشعار المتحكمات برسالة جديدة: ${messageData['id']}');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الرسائل: $e');
    }
  }
  
  /// إشعار متحكمات الإشعارات بإشعار جديد
  void _notifyNotificationControllers(Map<String, dynamic> notificationData) {
    try {
      // محاولة العثور على متحكم الإشعارات وتحديثه
      if (Get.isRegistered<NotificationsController>()) {
        final notificationsController = Get.find<NotificationsController>();
        notificationsController.addNewNotification(notificationData);
        notificationsController.update();
        // فحص إعدادات المستخدم قبل إظهار إشعار النظام
        bool showSystemNotification = true;
        if (Get.isRegistered<NotificationSettingsController>()) {
          final settingsController = Get.find<NotificationSettingsController>();
          if (!settingsController.pushNotifications) {
            showSystemNotification = false;
          }
        }
        if (showSystemNotification) {
          NotificationsService.showSimpleNotification(
            title: notificationData['title'] ?? 'إشعار جديد',
            body: notificationData['content'] ?? '',
            payload: notificationData['actionUrl'],
          );
        }
        // عرض إشعار منبثق (toast)
        _showNotificationToast(notificationData);
        debugPrint('تم إشعار متحكم الإشعارات بإشعار جديد: ${notificationData['id']}');
      } else {
        debugPrint('متحكم الإشعارات غير مسجل');
      }
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الإشعارات: $e');
    }
  }
  
  /// عرض إشعار منبثق (toast) للإشعار الجديد
  void _showNotificationToast(Map<String, dynamic> notificationData) {
    try {
      final title = notificationData['title'] as String? ?? 'إشعار جديد';
      final content = notificationData['content'] as String? ?? '';
      
      // استخدام GetX لعرض إشعار منبثق
      Get.snackbar(
        title,
        content,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue.shade100,
        colorText: Colors.black87,
        borderRadius: 10,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 4),
        isDismissible: true,
        icon: const Icon(Icons.notifications, color: Colors.blue),
        onTap: (_) {
          // عند النقر على الإشعار، انتقل إلى شاشة الإشعارات
          Get.toNamed('/notifications');
        },
      );
    } catch (e) {
      debugPrint('خطأ في عرض إشعار منبثق: $e');
    }
  }

  /// إشعار متحكمات المهام ببدء الكتابة
  void _notifyTaskTypingStarted(int taskId, String userName) {
    try {
      if (Get.isRegistered<TaskMessagesController>()) {
        final controller = Get.find<TaskMessagesController>();
        controller.addTypingUser(taskId, userName);
      }
      debugPrint('تم إشعار المتحكمات ببدء الكتابة في المهمة $taskId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المهام بتوقف الكتابة
  void _notifyTaskTypingStopped(int taskId, String userName) {
    try {
      if (Get.isRegistered<TaskMessagesController>()) {
        final controller = Get.find<TaskMessagesController>();
        controller.removeTypingUser(taskId, userName);
      }
      debugPrint('تم إشعار المتحكمات بتوقف الكتابة في المهمة $taskId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المجموعات ببدء الكتابة
  void _notifyGroupTypingStarted(int groupId, String userName) {
    try {
      if (Get.isRegistered<UnifiedChatController>()) {
        final controller = Get.find<UnifiedChatController>();
        controller.addTypingUser(groupId, userName);
      }
      debugPrint('تم إشعار المتحكمات ببدء الكتابة في المجموعة $groupId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  /// إشعار متحكمات المجموعات بتوقف الكتابة
  void _notifyGroupTypingStopped(int groupId, String userName) {
    try {
      if (Get.isRegistered<UnifiedChatController>()) {
        final controller = Get.find<UnifiedChatController>();
        controller.removeTypingUser(groupId, userName);
      }
      debugPrint('تم إشعار المتحكمات بتوقف الكتابة في المجموعة $groupId: $userName');
    } catch (e) {
      debugPrint('خطأ في إشعار متحكم الكتابة: $e');
    }
  }

  // ==================== وظائف عامة ====================

  /// مسح الأحداث المستقبلة
  void clearEvents() {
    _newComments.clear();
    _updatedComments.clear();
    _deletedCommentIds.clear();
    _newTaskMessages.clear();
    _newMessage.value = null;
  }

  /// إعادة الاتصال يدوياً لجميع الـ Hubs
  Future<void> reconnectAll() async {
    debugPrint('🔄 إعادة الاتصال لجميع الـ Hubs...');

    _chatReconnectAttempts = 0;
    _commentsReconnectAttempts = 0;
    _isChatReconnecting = false;
    _isCommentsReconnecting = false;

    // إلغاء وتنظيف المؤقتات
    _chatReconnectTimer?.cancel();
    _chatReconnectTimer = null;
    _commentsReconnectTimer?.cancel();
    _commentsReconnectTimer = null;

    await _chatHubConnection?.stop();
    await _taskCommentsHubConnection?.stop();

    await _initializeConnections();
  }

  // ==================== وظائف للتوافق مع الكود الحالي ====================

  /// للتوافق مع SignalRService الحالية
  void on(String methodName, Function(dynamic) callback) {
    _chatHubConnection?.on(methodName, (arguments) {
      try {
        if (arguments != null && arguments.isNotEmpty) {
          callback(arguments[0]);
        } else {
          callback(null);
        }
      } catch (e) {
        debugPrint('❌ خطأ في معالجة SignalR callback لـ $methodName: $e');
        // تجاهل الخطأ وعدم استدعاء callback لتجنب crash
      }
    });
  }

  /// للتوافق مع SignalRService الحالية
  Future<void> invoke(String methodName, {List<Object>? args}) async {
    try {
      if (_chatHubConnection?.state == HubConnectionState.Connected) {
        // استخدام المعاملات كما هي أو قائمة فارغة
        final filteredArgs = args ?? [];
        await _chatHubConnection?.invoke(methodName, args: filteredArgs);
        AppLogger.debug('تم استدعاء $methodName في Chat Hub');
      } else {
        AppLogger.warning("Chat Hub غير متصل. لا يمكن استدعاء $methodName");
      }
    } catch (e) {
      AppLogger.error('خطأ في استدعاء $methodName: $e');
      // تجاهل الخطأ إذا كان متعلق بـ null subtype
      if (e.toString().contains('Null\' is not a subtype of type \'Object\'')) {
        AppLogger.debug('تم تجاهل خطأ null subtype في استدعاء $methodName');
        return;
      }
      // عدم إعادة رمي الخطأ لتجنب crash التطبيق
    }
  }

  /// للتوافق مع SimpleSignalRService الحالية
  bool get isConnected => isTaskCommentsHubConnected;

  /// للتوافق مع جميع الخدمات
  Future<void> connect() async {
    await _initializeConnections();
  }

  /// للتوافق مع جميع الخدمات
  Future<void> disconnect() async {
    _cleanup();
  }
}
