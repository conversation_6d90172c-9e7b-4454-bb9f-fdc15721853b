import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:convert';

/// مثال توضيحي لحل مشكلة اتجاه النص في Flutter Quill
/// يوضح كيفية إصلاح مشكلة ظهور الأسطر الجديدة في الأعلى بدلاً من الأسفل
class QuillDirectionFixExample extends StatefulWidget {
  const QuillDirectionFixExample({Key? key}) : super(key: key);

  @override
  State<QuillDirectionFixExample> createState() => _QuillDirectionFixExampleState();
}

class _QuillDirectionFixExampleState extends State<QuillDirectionFixExample> {
  late QuillController _controller;
  
  // نص تجريبي عربي
  final String _sampleArabicText = '''مرحباً بك في محرر النصوص المتقدم
هذا نص تجريبي باللغة العربية
يمكنك الآن الضغط على Enter وستظهر الأسطر الجديدة في الأسفل بالترتيب الطبيعي
تم إصلاح مشكلة اتجاه النص بنجاح''';

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  /// تهيئة المتحكم مع النص التجريبي
  void _initializeController() {
    // إنشاء مستند جديد مع النص التجريبي
    final document = Document();
    
    // إدراج النص بالطريقة الصحيحة لتجنب مشكلة اتجاه الأسطر
    document.insert(0, _sampleArabicText);
    
    _controller = QuillController(
      document: document,
      selection: const TextSelection.collapsed(offset: 0),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال إصلاح مشكلة اتجاه النص'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // شرح المشكلة والحل
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border.all(color: Colors.green.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '✅ تم إصلاح المشكلة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'المشكلة: عند الضغط على Enter، كانت الأسطر الجديدة تظهر في الأعلى بدلاً من الأسفل',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 4),
                const Text(
                  'الحل: تم إصلاح طريقة إنشاء المستند وإدراج النص الأولي',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                const Text(
                  'جرب الآن: اضغط في المحرر أدناه واضغط Enter - ستظهر الأسطر الجديدة في الأسفل',
                  style: TextStyle(fontSize: 14, color: Colors.blue),
                ),
              ],
            ),
          ),
          
          // المحرر
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  // شريط أدوات مبسط
                  QuillSimpleToolbar(
                    controller: _controller,
                    configurations: const QuillSimpleToolbarConfigurations(
                      showBoldButton: true,
                      showItalicButton: true,
                      showUnderLineButton: true,
                      showColorButton: true,
                      showAlignmentButtons: true,
                      showDirection: true,
                      showUndo: true,
                      showRedo: true,
                    ),
                  ),
                  
                  // المحرر نفسه
                  Expanded(
                    child: QuillEditor.basic(
                      controller: _controller,
                      configurations: const QuillEditorConfigurations(
                        scrollable: true,
                        autoFocus: true,
                        expands: true,
                        padding: EdgeInsets.all(16),
                        placeholder: 'ابدأ الكتابة هنا واضغط Enter لإنشاء سطر جديد...',
                        showCursor: true,
                        enableInteractiveSelection: true,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _resetWithSampleText,
                  child: const Text('إعادة تعيين النص التجريبي'),
                ),
                ElevatedButton(
                  onPressed: _clearEditor,
                  child: const Text('مسح المحرر'),
                ),
                ElevatedButton(
                  onPressed: _showDocumentJson,
                  child: const Text('عرض JSON'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// إعادة تعيين النص التجريبي
  void _resetWithSampleText() {
    final document = Document();
    document.insert(0, _sampleArabicText);
    
    setState(() {
      _controller.document = document;
      _controller.updateSelection(
        const TextSelection.collapsed(offset: 0),
        ChangeSource.local,
      );
    });
  }

  /// مسح المحرر
  void _clearEditor() {
    final document = Document();
    
    setState(() {
      _controller.document = document;
      _controller.updateSelection(
        const TextSelection.collapsed(offset: 0),
        ChangeSource.local,
      );
    });
  }

  /// عرض محتوى المستند كـ JSON
  void _showDocumentJson() {
    final delta = _controller.document.toDelta();
    final jsonString = const JsonEncoder.withIndent('  ').convert(delta.toJson());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('محتوى المستند (JSON)'),
        content: SingleChildScrollView(
          child: SelectableText(
            jsonString,
            style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}